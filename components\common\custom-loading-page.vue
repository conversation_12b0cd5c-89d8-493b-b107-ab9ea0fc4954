<template>
  <view v-if="isVisible" class="custom-loading-page">
    <view class="loading-wrapper">
      <view class="loading-icon">
        <u-loading-icon :size="28" color="#C8C8C8"></u-loading-icon>
      </view>
      <text class="loading-text">{{ loadingContent }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomLoadingPage',
  props: {
    // 是否显示加载中
    isLoadingProp: {
      type: Boolean,
      default: false
    },
    // 加载提示文本
    loadingContent: {
      type: String,
      default: '加载中...'
    }
  },
  data() {
    return {
      // 内部状态，避免直接修改 prop
      visible: false
    }
  },
  computed: {
    // 计算属性，用于控制组件显示/隐藏
    isVisible() {
      return this.visible;
    }
  },
  watch: {
    // 监听 isLoadingProp prop 的变化
    isLoadingProp: {
      immediate: true,
      handler(val) {
        this.visible = val;
      }
    }
  },
  methods: {
    // 显示加载
    showLoadingPage() {
      this.visible = true;
    },
    // 隐藏加载
    hideLoadingPage() {
      this.visible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-loading-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -150px;

    .loading-icon {
      margin-bottom: 10px;
    }

    .loading-text {
      font-size: 19px;
      color: #C8C8C8;
    }
  }
}
</style>
