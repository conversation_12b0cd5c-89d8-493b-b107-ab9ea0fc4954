import request from '@/utils/request';

// 防抖计时器和缓存
const _debounceTimers = {};
const _requestCache = {};
const CACHE_EXPIRY = 30000; // 缓存过期时间：30秒

export default {
  /**
   * 获取门店商品库存
   * @param {Number} storeId 门店ID
   * @param {Boolean} forceRefresh 是否强制刷新
   * @returns {Promise} Promise对象
   */
  getStoreInventory(storeId, forceRefresh = false) {
    // 检查用户是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      console.log('API: 用户未登录，不调用门店库存API');
      return Promise.resolve([]); // 直接返回空数组，不调用API
    }

    // 检查当前页面是否是登录页
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1].route : '';
    if (currentPage && currentPage.includes('login')) {
      console.log('API: 当前在登录页面，不调用门店库存API');
      return Promise.resolve([]); // 直接返回空数组，不调用API
    }

    // 确保 storeId 是数字
    if (typeof storeId === 'object' && storeId !== null) {
      console.warn('storeId 参数是对象类型，尝试提取 storeId 属性');
      storeId = storeId.storeId;
    }

    console.log('获取门店商品库存，门店ID:', storeId, '强制刷新:', forceRefresh);

    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    // 生成缓存键
    const cacheKey = `store_inventory_${storeId}`;

    // 如果不是强制刷新，检查缓存
    if (!forceRefresh && _requestCache[cacheKey] && _requestCache[cacheKey].expiry > Date.now()) {
      console.log(`使用缓存的门店 ${storeId} 商品库存数据，有效期还剩 ${(_requestCache[cacheKey].expiry - Date.now()) / 1000} 秒`);
      return Promise.resolve(_requestCache[cacheKey].data);
    }

    // 如果已经有相同的请求正在进行中，取消之前的请求
    if (_debounceTimers[cacheKey]) {
      console.log(`取消之前的门店 ${storeId} 商品库存请求`);
      clearTimeout(_debounceTimers[cacheKey]);
    }

    // 返回一个新的 Promise
    return new Promise((resolve, reject) => {
      // 设置防抖计时器
      _debounceTimers[cacheKey] = setTimeout(() => {
        // 添加时间戳和关键词，避免浏览器缓存
        const params = {
          _t: Date.now(),
          keyword: 'empty' // 添加空关键词，避免筛选
        };

        console.log(`发起门店 ${storeId} 商品库存请求，时间戳: ${params._t}`);

        request.get(`/api/store-inventory/${storeId}`, params)
          .then(response => {
            console.log('获取门店商品库存响应:', response);

            if (!response) {
              console.warn('门店商品库存响应为空');
              return { success: true, data: [] };
            }

            if (response.success === false) {
              throw new Error(response.message || '获取门店商品库存失败');
            }

            // 处理响应数据
            let result;
            if (Array.isArray(response)) {
              result = { success: true, data: response };
            } else if (response.data) {
              result = response;
            } else {
              result = { success: true, data: response };
            }

            // 更新缓存
            _requestCache[cacheKey] = {
              data: result,
              expiry: Date.now() + CACHE_EXPIRY
            };

            // 清除防抖计时器
            delete _debounceTimers[cacheKey];

            resolve(result);
          })
          .catch(error => {
            console.error('获取门店商品库存失败:', error);

            // 清除防抖计时器
            delete _debounceTimers[cacheKey];

            reject(error);
          });
      }, 300); // 300ms 防抖延迟
    });
  },

  /**
   * 更新门店商品库存
   * @param {Number} storeId 门店ID
   * @param {Array} products 商品列表，每个商品包含productId和quantity
   * @param {Number} retryCount 重试次数，默认为0
   * @returns {Promise} Promise对象
   */
  updateStoreInventory(storeId, products, retryCount = 0) {
    console.log('更新门店商品库存，门店ID:', storeId, '商品:', products, '重试次数:', retryCount);

    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    if (!products || !Array.isArray(products) || products.length === 0) {
      return Promise.reject(new Error('缺少商品数据'));
    }

    // 验证每个商品数据
    for (const product of products) {
      if (!product.productId) {
        return Promise.reject(new Error('商品缺少ID'));
      }

      if (product.quantity === undefined || product.quantity === null) {
        return Promise.reject(new Error('商品缺少数量'));
      }

      // 确保数量是整数
      if (typeof product.quantity !== 'number') {
        product.quantity = parseInt(product.quantity, 10);
        if (isNaN(product.quantity)) {
          return Promise.reject(new Error('商品数量必须是有效数字'));
        }
      }
    }

    // 注意：我们现在允许一次处理多个商品，因为后端已经优化了批量处理
    // 但为了兼容性，如果商品数量超过10个，仍然分批处理
    if (products.length > 10) {
      console.warn(`检测到大量商品 (${products.length})，分批处理以避免事务冲突`);

      // 分批处理，每批最多10个商品
      const batches = [];
      for (let i = 0; i < products.length; i += 10) {
        batches.push(products.slice(i, i + 10));
      }

      console.log(`将 ${products.length} 个商品分为 ${batches.length} 批处理`);

      // 返回一个 Promise，等待所有批次处理完成
      return new Promise((resolve, reject) => {
        // 串行处理每个批次，避免并发导致的事务冲突
        const processNextBatch = (index) => {
          if (index >= batches.length) {
            // 所有批次处理完成
            resolve({ success: true, message: '所有批次处理完成' });
            return;
          }

          // 处理当前批次
          const currentBatch = batches[index];
          console.log(`处理第 ${index + 1}/${batches.length} 批，包含 ${currentBatch.length} 个商品`);

          // 递增重试次数，避免无限递归
          const batchRetryCount = retryCount > 0 ? retryCount + 1 : 0;

          // 调用自身处理当前批次
          this.updateStoreInventory(storeId, currentBatch, batchRetryCount)
            .then(() => {
              // 当前批次处理成功，处理下一批次
              processNextBatch(index + 1);
            })
            .catch(error => {
              console.error(`第 ${index + 1} 批处理失败:`, error);
              // 批次处理失败，但仍然继续处理下一批次
              processNextBatch(index + 1);
            });
        };

        // 开始处理第一批
        processNextBatch(0);
      });
    }

    // 添加时间戳，避免缓存
    const timestamp = Date.now();
    console.log(`发起更新请求，时间戳: ${timestamp}`);

    // 计算指数退避延迟时间（毫秒）
    const baseDelay = 500; // 基础延迟时间
    const exponentialDelay = retryCount === 0 ? baseDelay : baseDelay * Math.pow(2, retryCount);
    const jitter = Math.random() * 300; // 添加随机抖动，避免多个请求同时发送
    const delay = exponentialDelay + jitter;

    console.log(`使用延迟: ${delay}ms`);

    // 使用 Promise 包装，添加延迟和重试逻辑
    return new Promise((resolve, reject) => {
      // 添加延迟，避免事务冲突
      setTimeout(() => {
        // 构建请求数据 - 确保格式与后端 UpdateInventoryRequest 类匹配
        const requestData = {
          products: products.map(p => ({
            productId: p.productId,
            quantity: parseInt(p.quantity, 10) // 确保是整数
          }))
        };

        // 添加额外参数，尝试避免事务冲突
        requestData.singleTransaction = true; // 提示后端使用单独的事务
        requestData.clientTimestamp = timestamp; // 客户端时间戳

        // 添加调试信息
        console.log('发送请求数据:', JSON.stringify(requestData));

        // 使用 request 方法发送请求，确保包含认证信息
        request.post(`/api/store-inventory/${storeId}/update?_t=${timestamp}`, requestData)
        .then(response => {
          console.log('更新门店商品库存响应:', response);

          if (!response) {
            throw new Error('服务器返回空响应');
          }

          if (response.success === false) {
            // 检查是否是事务错误
            if (response.error && response.error.includes('SqlTransaction')) {
              throw new Error('SQL事务错误: ' + response.error);
            }
            throw new Error(response.message || '更新门店商品库存失败');
          }

          resolve(response);
        })
        .catch(error => {
          console.error('更新门店商品库存失败:', error);

          // 检查是否是事务错误
          const isTransactionError =
            (typeof error === 'string' && (
              error.includes('SqlTransaction') ||
              error.includes('transaction') ||
              error.includes('事务')
            )) ||
            (error.message && (
              error.message.includes('SqlTransaction') ||
              error.message.includes('transaction') ||
              error.message.includes('事务') ||
              error.message.includes('HTTP 错误')
            )) ||
            (error.error && (
              typeof error.error === 'string' && (
                error.error.includes('SqlTransaction') ||
                error.error.includes('transaction') ||
                error.error.includes('事务')
              )
            ));

          // 如果是事务错误或HTTP错误且重试次数小于5，则重试
          if ((isTransactionError || error.message?.includes('HTTP 错误')) && retryCount < 5) {
            const nextRetryDelay = Math.pow(2, retryCount + 1) * 1000; // 指数退避
            console.log(`检测到错误，${nextRetryDelay/1000}秒后重试...`);

            // 延迟重试，使用指数退避策略
            setTimeout(() => {
              console.log(`开始第${retryCount + 1}次重试...`);
              this.updateStoreInventory(storeId, products, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, nextRetryDelay);
          } else {
            // 尝试使用原始的 request 方法作为备选
            if (retryCount < 1) {
              console.log('尝试使用原始 request 方法作为备选...');

              // 构建与主方法相同的请求数据
              const fallbackRequestData = {
                products: products.map(p => ({
                  productId: p.productId,
                  quantity: parseInt(p.quantity, 10) // 确保是整数
                })),
                singleTransaction: true,
                clientTimestamp: Date.now()
              };
              console.log('备选方法请求数据:', JSON.stringify(fallbackRequestData));
              request.post(`/api/store-inventory/${storeId}/update?_t=${Date.now()}`, fallbackRequestData)
                .then(response => {
                  console.log('备选方法更新响应:', response);
                  resolve(response);
                })
                .catch(fallbackError => {
                  console.error('备选方法也失败:', fallbackError);
                  reject(error); // 返回原始错误
                });
            } else {
              reject(error);
            }
          }
        });
      }, delay);
    });
  }
};
