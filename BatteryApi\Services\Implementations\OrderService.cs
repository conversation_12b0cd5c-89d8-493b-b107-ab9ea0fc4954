using BatteryApi.DTOs;
using BatteryApi.DTOs.Common;
using BatteryApi.DTOs.Order;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using SqlSugar;

namespace BatteryApi.Services.Implementations;

public class OrderService : IOrderService
{
    private readonly ISqlSugarClient _db;

    public OrderService(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<PagedResponse<OrderDto>> GetOrdersAsync(OrderQueryParameters parameters)
    {
        var query = _db.Queryable<Order>()
            .WhereIF(parameters.UserId.HasValue, o => o.UserId == parameters.UserId.Value)
            //.WhereIF(parameters.BatteryId.HasValue, o => o.BatteryId == parameters.BatteryId.Value)
            .WhereIF(parameters.StoreId.HasValue, o => o.StoreId == parameters.StoreId.Value)
            //.WhereIF(parameters.Type.HasValue, o => o.Type == parameters.Type.Value)
            .WhereIF(parameters.Status.HasValue, o => o.Status == parameters.Status.Value)
            //.WhereIF(parameters.StartDate.HasValue, o => o.CreateTime >= parameters.StartDate.Value)
            //.WhereIF(parameters.EndDate.HasValue, o => o.CreateTime <= parameters.EndDate.Value)
            .WhereIF(!string.IsNullOrEmpty(parameters.OrderNo), o => o.OrderNo.Contains(parameters.OrderNo));
            //.Includes(o => o.User)
            ////.Includes(o => o.Battery)
            ////.Includes(o => o.Store)
            //.Includes(o => o.Operations);

        var total = await query.CountAsync();
        var orders = await query
            .OrderByDescending(o => o.CreateTime)
            .Skip((parameters.Page - 1) * parameters.PageSize)
            .Take(parameters.PageSize)
            .ToListAsync();

        var orderDtos = orders.Select(o => MapToOrderDto(o)).ToList();

        return new PagedResponse<OrderDto>(orderDtos, total, parameters.Page, parameters.PageSize);
    }

    public async Task<OrderDto> GetOrderByIdAsync(int id)
    {
        var order = await _db.Queryable<Order>()
            .Includes(o => o.User)
            .Includes(o => o.Product)
            .Includes(o => o.Store)
            .Includes(o => o.Operations)
            .FirstAsync(o => o.Id == id);

        if (order == null)
        {
            throw new Exception($"Order with ID {id} not found");
        }

        return MapToOrderDto(order);
    }

    public async Task<OrderDto> GetOrderByOrderNoAsync(string orderNo)
    {
        var order = await _db.Queryable<Order>()
            .Includes(o => o.User)
            .Includes(o => o.Product)
            .Includes(o => o.Store)
            .Includes(o => o.Operations)
            .FirstAsync(o => o.OrderNo == orderNo);

        if (order == null)
        {
            throw new Exception($"Order with order number {orderNo} not found");
        }

        return MapToOrderDto(order);
    }

    public async Task<List<OrderDto>> GetUserOrdersAsync(int userId)
    {
        var orders = await _db.Queryable<Order>()
            .Where(o => o.UserId == userId)
            .Includes(o => o.User)
            .Includes(o => o.Product)
            .Includes(o => o.Store)
            .Includes(o => o.Operations)
            .OrderByDescending(o => o.CreateTime)
            .ToListAsync();

        return orders.Select(o => MapToOrderDto(o)).ToList();
    }

    public async Task<OrderDto> CreateOrderAsync(int userId, OrderCreateDto orderDto)
    {
        // 检查商品是否存在
        var product = await _db.Queryable<Product>().FirstAsync(p => p.Id == orderDto.BatteryId);
        if (product == null)
        {
            throw new Exception($"Product with ID {orderDto.BatteryId} not found");
        }

        // 检查门店是否存在
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == orderDto.StoreId);
        if (store == null)
        {
            throw new Exception($"Store with ID {orderDto.StoreId} not found");
        }

        // 检查门店是否有该电池的库存
        var inventory = await _db.Queryable<StoreInventory>()
            .FirstAsync(i => i.StoreId == orderDto.StoreId && i.ProductId == orderDto.BatteryId);
        if (inventory == null || inventory.AvailableQuantity <= 0)
        {
            throw new Exception($"Battery with ID {orderDto.BatteryId} is not available in store with ID {orderDto.StoreId}");
        }

        // 创建订单
        var order = new Order
        {
            OrderNo = "ORD" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999),
            UserId = userId,
            ProductId = orderDto.BatteryId,
            StoreId = orderDto.StoreId,
            Type = orderDto.Type,
            Status = 1, // 待支付
            TotalAmount = orderDto.TotalAmount,
            RentAmount = orderDto.RentAmount,
            Deposit = orderDto.Deposit,
            RentDays = orderDto.RentDays,
            CreateTime = DateTime.UtcNow
        };

        await _db.Insertable(order).ExecuteCommandIdentityIntoEntityAsync();

        // 创建订单操作记录
        var operation = new OrderOperation
        {
            OrderId = order.Id,
            OperationType = 1, // 创建
            OperationTime = DateTime.UtcNow,
            OperatorId = userId,
            OperatorName = (await _db.Queryable<User>().FirstAsync(u => u.Id == userId))?.Username ?? "Unknown",
            Remark = "订单创建"
        };

        await _db.Insertable(operation).ExecuteCommandAsync();

        // 重新加载订单，包括关联数据
        return await GetOrderByIdAsync(order.Id);
    }

    public async Task<OrderDto> PayOrderAsync(int userId, OrderPayDto payDto)
    {
        var order = await _db.Queryable<Order>().FirstAsync(o => o.Id == payDto.OrderId);
        if (order == null)
        {
            throw new Exception($"Order with ID {payDto.OrderId} not found");
        }

        if (order.Status != 1) // 不是待支付状态
        {
            throw new Exception("Order cannot be paid because it is not in pending payment status");
        }

        // 更新订单状态
        order.Status = 2; // 待处理
        order.PayTime = payDto.PayTime;
        order.PayMethod = payDto.PayMethod;
        order.TransactionId = payDto.TransactionId;

        await _db.Updateable(order).ExecuteCommandAsync();

        // 创建订单操作记录
        var operation = new OrderOperation
        {
            OrderId = order.Id,
            OperationType = 2, // 支付
            OperationTime = DateTime.UtcNow,
            OperatorId = userId,
            OperatorName = (await _db.Queryable<User>().FirstAsync(u => u.Id == userId))?.Username ?? "Unknown",
            Remark = "订单支付"
        };

        await _db.Insertable(operation).ExecuteCommandAsync();

        // 如果是购买订单，减少库存
        if (order.Type == 2) // 购买
        {
            var inventory = await _db.Queryable<StoreInventory>()
                .FirstAsync(i => i.StoreId == order.StoreId && i.ProductId == order.ProductId);
            if (inventory != null)
            {
                inventory.AvailableQuantity -= 1;
                inventory.UpdatedAt = DateTime.UtcNow;
                await _db.Updateable(inventory).ExecuteCommandAsync();
            }
        }

        // 重新加载订单，包括关联数据
        return await GetOrderByIdAsync(order.Id);
    }

    public async Task<OrderDto> CancelOrderAsync(int userId, OrderCancelDto cancelDto)
    {
        var order = await _db.Queryable<Order>().FirstAsync(o => o.Id == cancelDto.OrderId);
        if (order == null)
        {
            throw new Exception($"Order with ID {cancelDto.OrderId} not found");
        }

        if (order.Status != 1 && order.Status != 2) // 不是待支付或待提货状态
        {
            throw new Exception("Order cannot be cancelled because it is not in pending payment or pending pickup status");
        }

        // 更新订单状态
        order.Status = 4; // 已取消
        order.CancelTime = DateTime.UtcNow;
        order.CancelReason = cancelDto.Reason;

        await _db.Updateable(order).ExecuteCommandAsync();

        // 创建订单操作记录
        var operation = new OrderOperation
        {
            OrderId = order.Id,
            OperationType = 4, // 取消
            OperationTime = DateTime.UtcNow,
            OperatorId = userId,
            OperatorName = (await _db.Queryable<User>().FirstAsync(u => u.Id == userId))?.Username ?? "Unknown",
            Remark = "订单取消: " + cancelDto.Reason
        };

        await _db.Insertable(operation).ExecuteCommandAsync();

        // 如果订单已支付且是购买订单，恢复库存
        if (order.Status == 2 && order.Type == 2) // 已支付且是购买
        {
            var inventory = await _db.Queryable<StoreInventory>()
                .FirstAsync(i => i.StoreId == order.StoreId && i.ProductId == order.ProductId);
            if (inventory != null)
            {
                inventory.AvailableQuantity += 1;
                inventory.UpdatedAt = DateTime.UtcNow;
                await _db.Updateable(inventory).ExecuteCommandAsync();
            }
        }

        // 重新加载订单，包括关联数据
        return await GetOrderByIdAsync(order.Id);
    }

    public async Task<OrderDto> CompleteOrderAsync(int userId, OrderCompleteDto completeDto)
    {
        var order = await _db.Queryable<Order>().FirstAsync(o => o.Id == completeDto.OrderId);
        if (order == null)
        {
            throw new Exception($"Order with ID {completeDto.OrderId} not found");
        }

        if (order.Status != 2) // 不是待提货状态
        {
            throw new Exception("Order cannot be completed because it is not in pending pickup status");
        }

        // 更新订单状态
        order.Status = 3; // 已完成
        order.CompleteTime = DateTime.UtcNow;

        await _db.Updateable(order).ExecuteCommandAsync();

        // 创建订单操作记录
        var operation = new OrderOperation
        {
            OrderId = order.Id,
            OperationType = 3, // 完成
            OperationTime = DateTime.UtcNow,
            OperatorId = userId,
            OperatorName = (await _db.Queryable<User>().FirstAsync(u => u.Id == userId))?.Username ?? "Unknown",
            Remark = completeDto.Remark ?? "订单完成"
        };

        await _db.Insertable(operation).ExecuteCommandAsync();

        // 重新加载订单，包括关联数据
        return await GetOrderByIdAsync(order.Id);
    }

    public async Task<Dictionary<string, int>> GetOrderCountsAsync(int? userId = null)
    {
        try
        {
            // 使用单个查询获取所有订单状态的计数
            var query = _db.Queryable<Order>();
            if (userId.HasValue)
            {
                query = query.Where(o => o.UserId == userId.Value);
                Console.WriteLine($"Filtering orders for user ID: {userId.Value}");
            }
            else
            {
                Console.WriteLine("Getting counts for all orders");
            }

            //// 获取所有订单并在内存中计算各个状态的数量
            //var orders = await query.ToListAsync();
            //Console.WriteLine($"Total orders retrieved: {orders.Count}");

            //// 输出所有订单的状态信息以进行调试
            //foreach (var order in orders)
            //{
            //    Console.WriteLine($"Order ID: {order.Id}, Status: {order.Status}, Type: {order.Type}, UserId: {order.UserId}");
            //}

            // 如果没有订单，创建一些测试订单
            //if (orders.Count == 0 && !userId.HasValue)
            //{
            //    Console.WriteLine("No orders found, creating test orders...");
            //    await CreateTestOrdersAsync();
            //    // 重新获取订单
            //    orders = await query.ToListAsync();
            //    Console.WriteLine($"After creating test orders, total orders: {orders.Count}");
            //}

            // 按照新的状态定义统计订单数量
            int status1Count = query.Count(o => o.Status == 1); // 待支付
            int status2Count = query.Count(o => o.Status == 2); // 待提货
            int status3Count = query.Count(o => o.Status == 3); // 已完成
            int status4Count = query.Count(o => o.Status == 4); // 已取消


            // 按类型统计
            int type1Count = query.Count(o => o.Type == 1); // 租赁
            int type2Count = query.Count(o => o.Type == 2); // 购买

            Console.WriteLine($"Type counts - 1 (Rental): {type1Count}, 2 (Purchase): {type2Count}");

            // 返回前端期望的格式
            var counts = new Dictionary<string, int>
            {
                { "status1", status1Count }, // 待支付
                { "status2", status2Count }, // 待提货
                { "status3", status3Count }, // 已完成
                { "status4", status4Count }, // 已取消
                { "rental", type1Count },    // 租赁订单数量
                { "purchase", type2Count }   // 购买订单数量
            };

            Console.WriteLine($"Final counts: {string.Join(", ", counts.Select(kv => $"{kv.Key}={kv.Value}"))}");
            return counts;
        }
        catch (Exception ex)
        {
            // 记录异常信息
            Console.WriteLine($"Error in GetOrderCountsAsync: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            throw;
        }
    }

    public async Task<List<Order>> GetAllOrdersAsync(int? userId = null)
    {
        try
        {
            var query = _db.Queryable<Order>();
            if (userId.HasValue)
            {
                query = query.Where(o => o.UserId == userId.Value);
            }
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in GetAllOrdersAsync: {ex.Message}");
            throw;
        }
    }

    public async Task CreateTestOrdersAsync()
    {
        try
        {
            // 检查是否有用户
            var users = await _db.Queryable<User>().ToListAsync();
            if (users.Count == 0)
            {
                Console.WriteLine("No users found, cannot create test orders");
                return;
            }

            // 检查是否有商品
            var products = await _db.Queryable<Product>().ToListAsync();
            if (products.Count == 0)
            {
                Console.WriteLine("No products found, cannot create test orders");
                return;
            }

            // 检查是否有商店
            var stores = await _db.Queryable<Store>().ToListAsync();
            if (stores.Count == 0)
            {
                Console.WriteLine("No stores found, cannot create test orders");
                return;
            }

            // 创建测试订单
            var testOrders = new List<Order>
            {
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}1",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 1, // 租赁
                    Status = 1, // 待支付
                    TotalAmount = 100,
                    RentAmount = 50,
                    Deposit = 50,
                    RentDays = 7,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}2",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 1, // 租赁
                    Status = 2, // 待收货
                    TotalAmount = 100,
                    RentAmount = 50,
                    Deposit = 50,
                    RentDays = 7,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1,
                    PayTime = DateTime.UtcNow
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}3",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 1, // 租赁
                    Status = 3, // 已完成
                    TotalAmount = 100,
                    RentAmount = 50,
                    Deposit = 50,
                    RentDays = 7,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1,
                    PayTime = DateTime.UtcNow
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}4",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 1, // 租赁
                    Status = 3, // 已完成
                    TotalAmount = 100,
                    RentAmount = 50,
                    Deposit = 50,
                    RentDays = 7,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1,
                    PayTime = DateTime.UtcNow,
                    CompleteTime = DateTime.UtcNow
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}5",
                    UserId = users[2].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 1, // 租赁
                    Status = 4, // 已取消
                    TotalAmount = 100,
                    RentAmount = 50,
                    Deposit = 50,
                    RentDays = 7,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1,
                    CancelTime = DateTime.UtcNow,
                    CancelReason = "测试取消"
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}6",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 2, // 购买
                    Status = 1, // 待支付
                    TotalAmount = 200,
                    RentAmount = 0,
                    Deposit = 0,
                    RentDays = 0,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1
                },
                new Order
                {
                    OrderNo = $"TEST{DateTime.Now.ToString("yyyyMMddHHmmss")}7",
                    UserId = users[1].Id,
                    ProductId = products[0].Id,
                    StoreId = stores[0].Id,
                    Type = 2, // 购买
                    Status = 3, // 已完成
                    TotalAmount = 200,
                    RentAmount = 0,
                    Deposit = 0,
                    RentDays = 0,
                    CreateTime = DateTime.UtcNow,
                    PayMethod = 1,
                    PayTime = DateTime.UtcNow,
                    CompleteTime = DateTime.UtcNow
                }
            };

            // 批量插入订单
            await _db.Insertable(testOrders).ExecuteCommandAsync();
            Console.WriteLine($"Created {testOrders.Count} test orders");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating test orders: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private OrderDto MapToOrderDto(Order order)
    {
        return new OrderDto
        {
            Id = order.Id,
            OrderNo = order.OrderNo,
            UserId = order.UserId,
            Username = order.User?.Username,
            BatteryId = order.ProductId,
            BatteryInfo = order.Product != null ? new BatteryInfoDto
            {
                Id = order.Product.Id,
                Code = $"{order.Product.Voltage}V{order.Product.Capacity}Ah",
                Spec = order.Product.Model,
                Price = order.Product.Price,
                RentPrice = order.Product.RentPrice
            } : null,
            StoreId = order.StoreId,
            StoreInfo = order.Store != null ? new StoreInfoDto
            {
                Id = order.Store.Id,
                Name = order.Store.Name,
                Address = order.Store.Address
            } : null,
            Type = order.Type,
            Status = order.Status,
            TotalAmount = order.TotalAmount,
            RentAmount = order.RentAmount,
            Deposit = order.Deposit,
            RentDays = order.RentDays,
            CreateTime = order.CreateTime,
            PayTime = order.PayTime,
            CompleteTime = order.CompleteTime,
            CancelTime = order.CancelTime,
            CancelReason = order.CancelReason,
            PayMethod = order.PayMethod,
            TransactionId = order.TransactionId,
            OperationRecords = order.Operations?.Select(op => new OrderOperationDto
            {
                Id = op.Id,
                OrderId = op.OrderId,
                OperationType = op.OperationType,
                OperationTime = op.OperationTime,
                OperatorId = op.OperatorId,
                OperatorName = op.OperatorName,
                Remark = op.Remark
            }).ToList() ?? new List<OrderOperationDto>()
        };
    }
}
