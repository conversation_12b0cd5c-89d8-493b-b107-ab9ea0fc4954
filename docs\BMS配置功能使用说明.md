# BMS信息功能使用说明

## 功能概述

本功能实现了BMS（电池管理系统）完整信息的查看和管理，包括实时状态、配置参数、电压温度等所有数据，支持两种使用方式：
1. **弹窗方式** - 在当前页面以弹窗形式显示完整BMS信息
2. **页面方式** - 跳转到专门的BMS信息页面查看详细信息

## 文件结构

```
├── pages/admin/battery/
│   ├── bms-config.vue          # BMS配置参数完整页面
│   └── edit.vue                # 新增/编辑电池页面（已集成BMS配置功能）
├── components/
│   └── bms-config-modal.vue    # BMS配置参数弹窗组件
├── api/
│   └── bms.js                  # BMS API接口
└── docs/
    └── BMS配置功能使用说明.md   # 本文档
```

## 功能特性

### 1. 实时状态显示
- **SOC**：电池剩余电量百分比
- **总电压**：电池组总电压
- **电流**：充放电电流（正值为充电，负值为放电）
- **功率**：实时功率
- **充电状态**：未充电/充电中/充电完成/充电异常
- **连接状态**：已连接/未连接

### 2. 电压信息显示
- **电压统计**：最高/最低/平均电压、压差
- **单体电压数量**：电池串数
- **单体电压详情**：每节电池的实时电压（高亮显示最高/最低电压）

### 3. 温度信息显示
- **温度统计**：最高/最低温度
- **温度传感器数量**：温度探头数量
- **温度传感器详情**：每个传感器的实时温度（颜色区分温度范围）

### 4. 系统信息显示
- **固件版本**：BMS固件版本号
- **协议版本**：通信协议版本
- **容量**：电池容量
- **循环次数**：充放电循环计数
- **MOS状态**：充电/放电MOS管状态
- **均衡状态**：电池均衡工作状态

### 5. 故障和报警显示
- **故障状态**：系统故障信息列表
- **报警状态**：系统报警信息列表

### 6. 电芯特征显示
- 电池类型（磷酸铁锂）
- 电池标称容量
- 电池实际容量
- 休眠等待时间
- 电池编号

### 7. 保护参数显示
- **电压保护**：单体过压/欠压保护、总压保护、压差保护
- **电流保护**：充电/放电过流保护

### 8. 温度保护显示
- **充电温度保护**：高温/低温保护及恢复值
- **放电温度保护**：高温/低温保护及恢复值
- **其他温度保护**：温差保护、功率管温度保护、电池箱内温度保护

### 9. 均衡控制显示
- 均衡开启电压
- 均衡开启压差
- 主动均衡控制开关状态

### 10. 其他设置显示
- 电流校准开关状态
- 电流校准值
- 加热开关状态

## 使用方法

### 1. 在新增电池页面中使用

在新增电池页面（`/pages/admin/battery/edit`）中，当选择电池类别后：

1. **输入MAC ID**：在MAC ID输入框中输入电池设备的MAC ID
2. **点击查看BMS**：点击"查看BMS"按钮，弹窗显示完整的BMS信息
3. **查看详细信息**：在弹窗中可以查看所有实时状态和配置参数，也可以点击"查看详情"跳转到完整页面

### 2. 弹窗组件使用

#### 引入组件
```vue
<script>
import BMSConfigModal from '@/components/bms-config-modal.vue'

export default {
  components: {
    BMSConfigModal
  }
}
</script>
```

#### 模板使用
```vue
<template>
  <view>
    <!-- 触发按钮 -->
    <button @click="showBMSConfig">查看BMS配置</button>

    <!-- BMS配置弹窗 -->
    <bms-config-modal
      :visible="showModal"
      :macId="deviceMacId"
      @close="closeModal"
    />
  </view>
</template>
```

#### 数据和方法
```vue
<script>
export default {
  data() {
    return {
      showModal: false,
      deviceMacId: 'InputUserdata60322504290'
    }
  },

  methods: {
    showBMSConfig() {
      this.showModal = true
    },

    closeModal() {
      this.showModal = false
    }
  }
}
</script>
```

### 2. 页面跳转使用

```javascript
// 跳转到BMS配置页面
uni.navigateTo({
  url: `/pages/admin/battery/bms-config?macId=${macId}`
})
```

### 4. 集成到现有页面

如果要在其他页面中集成BMS配置功能，参考新增电池页面的实现：

```vue
<!-- 在模板中添加按钮 -->
<button
  class="test-btn"
  @tap="showBMSConfig"
  :disabled="!macId"
  style="background-color: #52c41a;"
>查看BMS</button>

<!-- 在页面底部添加弹窗组件 -->
<bms-config-modal
  :visible="showBMSModal"
  :macId="macId"
  @close="closeBMSModal"
/>
```

### 3. API接口使用

```javascript
import BMSAPI from '@/api/bms.js'

// 获取BMS配置数据
try {
  const response = await BMSAPI.getBatteryStatus(macId)
  console.log('BMS配置数据:', response.data.data)
} catch (error) {
  console.error('获取失败:', error)
}
```

## 组件属性

### BMSConfigModal 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 控制弹窗显示/隐藏 |
| macId | String | '' | 电池设备的MAC ID |

### BMSConfigModal 组件事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭弹窗时触发 | - |

## 数据格式

### BMS配置数据结构
```javascript
{
  "sets": [
    {
      "vars": [
        {
          "label": "电池类型",
          "value": "磷酸铁锂",
          "type": "radio",
          "unit": "",
          "option": [
            {"label": "磷酸铁锂", "value": "1"},
            {"label": "三元锂", "value": "2"}
          ]
        },
        {
          "label": "电池标称容量",
          "value": "40",
          "unit": "AH"
        }
        // ... 更多配置参数
      ]
    }
  ]
}
```

## 样式特性

### 1. 响应式设计
- 支持移动端和桌面端适配
- 弹窗在不同屏幕尺寸下自适应

### 2. 美观的UI设计
- 渐变色背景
- 圆角卡片设计
- 清晰的参数分组
- 状态指示器（开启/关闭）

### 3. 交互体验
- 加载状态显示
- 错误状态处理
- 平滑的动画效果

## 注意事项

1. **MAC ID 必需**：调用BMS配置功能时必须提供有效的MAC ID
2. **网络连接**：功能依赖后端BMS API，需要确保网络连接正常
3. **错误处理**：组件内置了错误处理机制，会显示友好的错误提示
4. **数据缓存**：配置数据不会自动缓存，每次打开都会重新获取最新数据

## 扩展功能

### 1. 配置编辑
目前仅支持查看，后续可扩展编辑功能：
```javascript
// 编辑配置参数
editConfig() {
  // 实现配置编辑逻辑
}
```

### 2. 配置导出
支持将配置导出为JSON或文本格式：
```javascript
// 导出配置
exportConfig() {
  // 实现配置导出逻辑
}
```

### 3. 配置对比
支持多个设备的配置参数对比：
```javascript
// 配置对比
compareConfigs(macIds) {
  // 实现配置对比逻辑
}
```

## 技术支持

如有问题或建议，请联系开发团队。
