/**
 * 全局混入
 * 为所有页面添加通用功能
 */

export default {
  data() {
    return {
      // 加载状态
      loading: false,
      // 加载提示文本
      loadingText: '加载中...'
    }
  },

  // 在页面模板中自动添加 uView 的 Toast 组件
  onReady() {
    // 检查页面是否已经有 uToast 组件
    if (!this.$refs.uToast) {
      console.warn('页面中缺少 uToast 组件，全局 Toast 功能可能无法正常工作');
    }

    // 检查页面是否已经有 uLoading-icon 组件
    if (!this.$refs.uLoading) {
      console.warn('页面中缺少 uLoading-icon 组件，全局加载功能可能无法正常工作');
    }
  }
}
