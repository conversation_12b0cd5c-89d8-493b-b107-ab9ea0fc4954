<template>
  <view class="bms-detail-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">BMS电池状态详情</text>
        <view class="header-info">
          <text class="mac-id">MAC ID: {{ macId }}</text>
          <view class="refresh-btn" @click="refreshData">
            <text class="refresh-icon">🔄</text>
            <text>刷新</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在获取BMS数据...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadBMSData">重试</button>
    </view>

    <!-- BMS数据显示 -->
    <view v-else-if="bmsData" class="bms-content">
      <!-- 基本状态信息 -->
      <view class="status-section">
        <view class="section-title">
          <text class="title-icon">🔋</text>
          <text class="title-text">电池状态</text>
        </view>

        <view class="status-grid">
          <view class="status-item">
            <text class="status-label">总电压</text>
            <text class="status-value">{{ bmsData.data.state.voltageAll }}V</text>
          </view>
          <view class="status-item">
            <text class="status-label">电流</text>
            <text class="status-value">{{ bmsData.data.state.current }}A</text>
          </view>
          <view class="status-item">
            <text class="status-label">SOC</text>
            <text class="status-value">{{ bmsData.data.state.soc }}%</text>
          </view>
          <view class="status-item">
            <text class="status-label">功率</text>
            <text class="status-value">{{ bmsData.data.state.power }}W</text>
          </view>
          <view class="status-item">
            <text class="status-label">容量</text>
            <text class="status-value">{{ bmsData.data.state.capacity }}AH</text>
          </view>
          <view class="status-item">
            <text class="status-label">循环次数</text>
            <text class="status-value">{{ bmsData.data.state.cycle }}</text>
          </view>
        </view>
      </view>

      <!-- 电压信息 -->
      <view class="voltage-section">
        <view class="section-title">
          <text class="title-icon">⚡</text>
          <text class="title-text">单体电压 ({{ bmsData.data.state.count }}节)</text>
        </view>

        <view class="voltage-stats">
          <view class="voltage-stat">
            <text class="stat-label">最高电压</text>
            <text class="stat-value high">{{ bmsData.data.state.maxVoltage }}V</text>
          </view>
          <view class="voltage-stat">
            <text class="stat-label">最低电压</text>
            <text class="stat-value low">{{ bmsData.data.state.minVoltage }}V</text>
          </view>
          <view class="voltage-stat">
            <text class="stat-label">平均电压</text>
            <text class="stat-value">{{ bmsData.data.state.averageVoltage.toFixed(3) }}V</text>
          </view>
          <view class="voltage-stat">
            <text class="stat-label">压差</text>
            <text class="stat-value">{{ (bmsData.data.state.maxVoltage - bmsData.data.state.minVoltage).toFixed(3) }}V</text>
          </view>
        </view>

        <view class="voltage-grid">
          <view
            v-for="(voltage, index) in bmsData.data.state.voltage"
            :key="index"
            class="voltage-cell"
            :class="getVoltageCellClass(voltage)"
          >
            <text class="cell-number">{{ index + 1 }}</text>
            <text class="cell-voltage">{{ voltage.toFixed(3) }}V</text>
          </view>
        </view>
      </view>

      <!-- 温度信息 -->
      <view class="temperature-section">
        <view class="section-title">
          <text class="title-icon">🌡️</text>
          <text class="title-text">温度监控</text>
        </view>

        <view class="temp-stats">
          <view class="temp-stat">
            <text class="stat-label">最高温度</text>
            <text class="stat-value high">{{ bmsData.data.state.maxTemp }}℃</text>
          </view>
          <view class="temp-stat">
            <text class="stat-label">最低温度</text>
            <text class="stat-value low">{{ bmsData.data.state.minTemp }}℃</text>
          </view>
          <view class="temp-stat">
            <text class="stat-label">温度探头</text>
            <text class="stat-value">{{ bmsData.data.state.tempCount }}个</text>
          </view>
        </view>

        <view class="temp-grid">
          <view
            v-for="(temp, index) in bmsData.data.state.temp"
            :key="index"
            class="temp-item"
            :class="getTempItemClass(temp)"
          >
            <text class="temp-label">探头{{ index + 1 }}</text>
            <text class="temp-value">{{ temp }}℃</text>
          </view>
        </view>
      </view>

      <!-- MOS状态 -->
      <view class="mos-section">
        <view class="section-title">
          <text class="title-icon">🔌</text>
          <text class="title-text">MOS状态</text>
        </view>

        <view class="mos-grid">
          <view class="mos-item">
            <text class="mos-label">充电MOS</text>
            <view class="mos-status" :class="bmsData.data.state.chargingMOS ? 'on' : 'off'">
              <text class="mos-text">{{ bmsData.data.state.chargingMOS ? '开启' : '关闭' }}</text>
            </view>
          </view>
          <view class="mos-item">
            <text class="mos-label">放电MOS</text>
            <view class="mos-status" :class="bmsData.data.state.dischargeMOS ? 'on' : 'off'">
              <text class="mos-text">{{ bmsData.data.state.dischargeMOS ? '开启' : '关闭' }}</text>
            </view>
          </view>
          <view class="mos-item">
            <text class="mos-label">充电状态</text>
            <view class="mos-status" :class="getChargeStateClass()">
              <text class="mos-text">{{ getChargeStateText() }}</text>
            </view>
          </view>
          <view class="mos-item">
            <text class="mos-label">均衡状态</text>
            <view class="mos-status" :class="bmsData.data.state.equilibrium ? 'on' : 'off'">
              <text class="mos-text">{{ bmsData.data.state.equilibrium ? '均衡中' : '未均衡' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 故障和报警信息 -->
      <view class="fault-section">
        <view class="section-title">
          <text class="title-icon">⚠️</text>
          <text class="title-text">故障与报警</text>
        </view>

        <view class="fault-content">
          <view class="fault-group">
            <text class="fault-group-title">故障状态</text>
            <view v-if="bmsData.data.state.faultState && bmsData.data.state.faultState.length > 0" class="fault-list">
              <view
                v-for="(fault, index) in bmsData.data.state.faultState"
                :key="index"
                class="fault-item error"
              >
                <text class="fault-icon">🚨</text>
                <text class="fault-text">{{ fault }}</text>
              </view>
            </view>
            <view v-else class="no-fault">
              <text class="no-fault-icon">✅</text>
              <text class="no-fault-text">无故障</text>
            </view>
          </view>

          <view class="fault-group">
            <text class="fault-group-title">报警状态</text>
            <view v-if="bmsData.data.state.alarmState && bmsData.data.state.alarmState.length > 0" class="fault-list">
              <view
                v-for="(alarm, index) in bmsData.data.state.alarmState"
                :key="index"
                class="fault-item warning"
              >
                <text class="fault-icon">⚠️</text>
                <text class="fault-text">{{ alarm }}</text>
              </view>
            </view>
            <view v-else class="no-fault">
              <text class="no-fault-icon">✅</text>
              <text class="no-fault-text">无报警</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 固件信息 -->
      <view class="firmware-section">
        <view class="section-title">
          <text class="title-icon">💾</text>
          <text class="title-text">设备信息</text>
        </view>

        <view class="firmware-grid">
          <view class="firmware-item">
            <text class="firmware-label">固件版本</text>
            <text class="firmware-value">{{ bmsData.data.state.firmware }}</text>
          </view>
          <view class="firmware-item">
            <text class="firmware-label">通信协议</text>
            <text class="firmware-value">{{ bmsData.data.state.protocol }}</text>
          </view>
          <view class="firmware-item">
            <text class="firmware-label">连接状态</text>
            <view class="connection-status" :class="bmsData.data.state.connectState ? 'connected' : 'disconnected'">
              <text class="connection-text">{{ bmsData.data.state.connectState ? '已连接' : '未连接' }}</text>
            </view>
          </view>
          <view class="firmware-item">
            <text class="firmware-label">数据时间</text>
            <text class="firmware-value">{{ formatTime(bmsData.data.time) }}</text>
          </view>
        </view>
      </view>

      <!-- 配置参数 -->
      <view class="config-section">
        <view class="section-title">
          <text class="title-icon">⚙️</text>
          <text class="title-text">配置参数</text>
        </view>

        <view class="config-tabs">
          <view
            v-for="(set, index) in bmsData.data.sets"
            :key="index"
            class="config-tab"
            :class="{ active: activeConfigTab === index }"
            @click="activeConfigTab = index"
          >
            <text class="tab-text">{{ set.class }}</text>
          </view>
        </view>

        <view v-if="bmsData.data.sets[activeConfigTab]" class="config-content">
          <view
            v-for="(param, index) in bmsData.data.sets[activeConfigTab].vars"
            :key="index"
            class="config-param"
          >
            <view class="param-info">
              <text class="param-label">{{ param.label }}</text>
              <text class="param-name">({{ param.name }})</text>
            </view>
            <view class="param-value">
              <text v-if="param.type === 'radio'" class="value-text">
                {{ getRadioOptionLabel(param) }}
              </text>
              <text v-else class="value-text">{{ param.value }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BMSAPI from '@/api/bms.js'

export default {
  name: 'BMSDetail',
  data() {
    return {
      macId: '',
      bmsData: null,
      loading: false,
      error: null,
      refreshTimer: null,
      activeConfigTab: 0
    }
  },

  onLoad(options) {
    if (options.macId) {
      this.macId = options.macId
      this.loadBMSData()

      // 设置自动刷新（每30秒）
      this.startAutoRefresh()
    } else {
      this.error = '缺少MAC ID参数'
    }
  },

  onUnload() {
    // 清除定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  methods: {
    async loadBMSData() {
      this.loading = true
      this.error = null

      try {
        console.log('获取BMS数据，MAC ID:', this.macId)
        const response = await BMSAPI.getBatteryStatus(this.macId)

        console.log('BMS数据响应:', response)

        if (response && response.data) {
          this.bmsData = response
        } else {
          throw new Error('获取BMS数据失败')
        }
      } catch (error) {
        console.error('获取BMS数据失败:', error)
        this.error = error.message || '获取BMS数据失败，请检查网络连接'
      } finally {
        this.loading = false
      }
    },

    refreshData() {
      this.loadBMSData()
    },

    startAutoRefresh() {
      // 每30秒自动刷新一次
      this.refreshTimer = setInterval(() => {
        this.loadBMSData()
      }, 30000)
    },

    getVoltageCellClass(voltage) {
      const maxVoltage = this.bmsData.data.state.maxVoltage
      const minVoltage = this.bmsData.data.state.minVoltage

      if (voltage === maxVoltage) return 'highest'
      if (voltage === minVoltage) return 'lowest'
      if (voltage > 3.5) return 'high'
      if (voltage < 2.8) return 'low'
      return 'normal'
    },

    getTempItemClass(temp) {
      if (temp > 45) return 'high'
      if (temp < 0) return 'low'
      return 'normal'
    },

    getChargeStateClass() {
      const state = this.bmsData.data.state.chargeState
      if (state === 1) return 'charging'
      if (state === 2) return 'discharging'
      return 'idle'
    },

    getChargeStateText() {
      const state = this.bmsData.data.state.chargeState
      switch (state) {
        case 1: return '充电中'
        case 2: return '放电中'
        default: return '空闲'
      }
    },

    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    },

    getRadioOptionLabel(param) {
      if (param.option && param.option.length > 0) {
        const option = param.option.find(opt => opt.value === param.value)
        return option ? option.label : param.value
      }
      return param.value
    }
  }
}
</script>

<style lang="scss" scoped>
.bms-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;

  .header-content {
    .header-title {
      font-size: 36rpx;
      font-weight: bold;
      display: block;
      margin-bottom: 10rpx;
    }

    .header-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .mac-id {
        font-size: 28rpx;
        opacity: 0.9;
      }

      .refresh-btn {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        .refresh-icon {
          margin-right: 8rpx;
        }
      }
    }
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text, .error-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }

  .error-icon {
    font-size: 60rpx;
    margin-bottom: 20rpx;
  }

  .retry-btn {
    background-color: #667eea;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.bms-content {
  padding: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .title-icon {
    font-size: 32rpx;
    margin-right: 10rpx;
  }

  .title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

// 状态网格
.status-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .status-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .status-item {
      text-align: center;
      padding: 20rpx;
      background: #f8f9ff;
      border-radius: 10rpx;

      .status-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .status-value {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

// 电压部分
.voltage-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .voltage-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15rpx;
    margin-bottom: 30rpx;

    .voltage-stat {
      text-align: center;
      padding: 15rpx;
      background: #f8f9ff;
      border-radius: 8rpx;

      .stat-label {
        display: block;
        font-size: 22rpx;
        color: #666;
        margin-bottom: 5rpx;
      }

      .stat-value {
        display: block;
        font-size: 26rpx;
        font-weight: bold;

        &.high {
          color: #ff4757;
        }

        &.low {
          color: #3742fa;
        }
      }
    }
  }

  .voltage-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10rpx;

    .voltage-cell {
      text-align: center;
      padding: 15rpx 10rpx;
      border-radius: 8rpx;
      border: 2rpx solid #e1e8ed;

      .cell-number {
        display: block;
        font-size: 20rpx;
        color: #666;
        margin-bottom: 5rpx;
      }

      .cell-voltage {
        display: block;
        font-size: 22rpx;
        font-weight: bold;
      }

      &.highest {
        background: #ffe6e6;
        border-color: #ff4757;
        color: #ff4757;
      }

      &.lowest {
        background: #e6f2ff;
        border-color: #3742fa;
        color: #3742fa;
      }

      &.high {
        background: #fff5e6;
        border-color: #ffa726;
        color: #ffa726;
      }

      &.low {
        background: #f3e5f5;
        border-color: #ab47bc;
        color: #ab47bc;
      }

      &.normal {
        background: #f0f8f0;
        border-color: #4caf50;
        color: #4caf50;
      }
    }
  }
}

// 温度部分
.temperature-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .temp-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15rpx;
    margin-bottom: 30rpx;

    .temp-stat {
      text-align: center;
      padding: 15rpx;
      background: #f8f9ff;
      border-radius: 8rpx;

      .stat-label {
        display: block;
        font-size: 22rpx;
        color: #666;
        margin-bottom: 5rpx;
      }

      .stat-value {
        display: block;
        font-size: 26rpx;
        font-weight: bold;

        &.high {
          color: #ff4757;
        }

        &.low {
          color: #3742fa;
        }
      }
    }
  }

  .temp-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15rpx;

    .temp-item {
      text-align: center;
      padding: 20rpx;
      border-radius: 10rpx;
      border: 2rpx solid #e1e8ed;

      .temp-label {
        display: block;
        font-size: 22rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .temp-value {
        display: block;
        font-size: 26rpx;
        font-weight: bold;
      }

      &.high {
        background: #ffe6e6;
        border-color: #ff4757;
        color: #ff4757;
      }

      &.low {
        background: #e6f2ff;
        border-color: #3742fa;
        color: #3742fa;
      }

      &.normal {
        background: #f0f8f0;
        border-color: #4caf50;
        color: #4caf50;
      }
    }
  }
}

// MOS状态部分
.mos-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .mos-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .mos-item {
      .mos-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 10rpx;
      }

      .mos-status {
        padding: 15rpx 20rpx;
        border-radius: 8rpx;
        text-align: center;

        .mos-text {
          font-size: 26rpx;
          font-weight: bold;
        }

        &.on {
          background: #e8f5e8;
          color: #4caf50;
        }

        &.off {
          background: #ffe6e6;
          color: #ff4757;
        }

        &.charging {
          background: #e3f2fd;
          color: #2196f3;
        }

        &.discharging {
          background: #fff3e0;
          color: #ff9800;
        }

        &.idle {
          background: #f5f5f5;
          color: #666;
        }
      }
    }
  }
}

// 故障报警部分
.fault-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .fault-content {
    .fault-group {
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .fault-group-title {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 15rpx;
      }

      .fault-list {
        .fault-item {
          display: flex;
          align-items: center;
          padding: 15rpx;
          border-radius: 8rpx;
          margin-bottom: 10rpx;

          .fault-icon {
            margin-right: 10rpx;
            font-size: 24rpx;
          }

          .fault-text {
            font-size: 26rpx;
            flex: 1;
          }

          &.error {
            background: #ffe6e6;
            color: #d32f2f;
          }

          &.warning {
            background: #fff3e0;
            color: #f57c00;
          }
        }
      }

      .no-fault {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30rpx;
        background: #f0f8f0;
        border-radius: 8rpx;

        .no-fault-icon {
          margin-right: 10rpx;
          font-size: 28rpx;
        }

        .no-fault-text {
          font-size: 26rpx;
          color: #4caf50;
        }
      }
    }
  }
}

// 设备信息部分
.firmware-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .firmware-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .firmware-item {
      .firmware-label {
        display: block;
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .firmware-value {
        display: block;
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
      }

      .connection-status {
        padding: 8rpx 15rpx;
        border-radius: 15rpx;
        text-align: center;

        .connection-text {
          font-size: 24rpx;
          font-weight: bold;
        }

        &.connected {
          background: #e8f5e8;
          color: #4caf50;
        }

        &.disconnected {
          background: #ffe6e6;
          color: #ff4757;
        }
      }
    }
  }
}

// 配置参数部分
.config-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .config-tabs {
    display: flex;
    margin-bottom: 30rpx;
    border-bottom: 2rpx solid #f0f0f0;

    .config-tab {
      flex: 1;
      text-align: center;
      padding: 20rpx 10rpx;
      border-bottom: 4rpx solid transparent;

      .tab-text {
        font-size: 26rpx;
        color: #666;
      }

      &.active {
        border-bottom-color: #667eea;

        .tab-text {
          color: #667eea;
          font-weight: bold;
        }
      }
    }
  }

  .config-content {
    .config-param {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .param-info {
        flex: 1;

        .param-label {
          display: block;
          font-size: 26rpx;
          color: #333;
          margin-bottom: 5rpx;
        }

        .param-name {
          font-size: 22rpx;
          color: #999;
        }
      }

      .param-value {
        .value-text {
          font-size: 26rpx;
          font-weight: bold;
          color: #667eea;
        }
      }
    }
  }
}
</style>
