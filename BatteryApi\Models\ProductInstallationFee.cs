using SqlSugar;

namespace BatteryApi.Models;

/// <summary>
/// 商品安装费用实体
/// </summary>
[SugarTable("ProductInstallationFees")]
public class ProductInstallationFee
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int ProductId { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 费用金额
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Price { get; set; }

    /// <summary>
    /// 费用描述
    /// </summary>
    [SugarColumn(Length = 500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int Sort { get; set; } = 0;

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的商品（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProductId))]
    public Product? Product { get; set; }
}
