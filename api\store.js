/**
 * 门店API服务
 */
import request from '@/utils/request';

// API接口
const StoreAPI = {
  /**
   * 获取门店列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getStoreList(params = {}) {
    // 检查用户是否已登录
    const token = uni.getStorageSync('token');
    if (!token) {
      console.log('API: 用户未登录，不调用门店列表API');
      return Promise.resolve([]); // 直接返回空数组，不调用API
    }

    // 检查当前页面是否是登录页
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1].route : '';
    if (currentPage && currentPage.includes('login')) {
      console.log('API: 当前在登录页面，不调用门店列表API');
      return Promise.resolve([]); // 直接返回空数组，不调用API
    }

    // 确保参数类型正确
    const queryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 20 // 使用较小的默认值，避免一次加载过多数据
    };

    // 添加查询参数 - 使用后端期望的参数名
    if (params.keyword) {
      queryParams.Name = params.keyword; // 将keyword映射到Name参数
    }

    if (params.status) {
      queryParams.Status = params.status; // 首字母大写，与后端参数匹配
    }

    // 添加联系人查询参数 - 使用Contact字段
    if (params.contact) {
      queryParams.Contact = params.contact; // 使用Contact字段，与数据库匹配
    }

    // 兼容旧代码，如果前端传入了manager参数，也使用Contact字段
    if (params.manager) {
      queryParams.Contact = params.manager; // 使用Contact字段，与数据库匹配
    }

    // 添加电话查询参数
    if (params.phone) {
      queryParams.Phone = params.phone; // 首字母大写，与后端参数匹配
    }

    // 添加刷新标志
    if (params.refresh) {
      queryParams.refresh = true;
    }

    // 添加时间戳，避免缓存
    queryParams._t = params._t || Date.now();

    console.log('发送到服务器的参数:', queryParams);

    // 添加debugger，用于调试500错误
    console.log('即将调用 /api/store 接口，参数:', queryParams);

    // 使用统一的请求方法，不使用硬编码URL
    return request.get('/api/store', queryParams).then(response => {
      console.log('门店列表API响应:', response);

      // 检查响应是否为空
      if (!response) {
        console.warn('门店列表API响应为空');
        return [];
      }

      // 处理不同的响应格式
      if (Array.isArray(response)) {
        return response;
      } else if (response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response.items && Array.isArray(response.items)) {
        return response.items;
      }

      // 直接返回响应数据，让Vuex处理不同的响应格式
      return response;
    }).catch(error => {
      // 添加debugger，用于调试错误
      console.error('获取门店列表失败:', error);
      console.error('错误详情:', JSON.stringify(error));

      // 如果API调用失败，返回空数组
      return [];
    });
  },

  /**
   * 获取门店详情
   * @param {Number} id 门店ID
   * @returns {Promise} Promise对象
   */
  getStoreDetail(id) {
    return request.get(`/api/store/${id}`).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 创建门店
   * @param {Object} storeData 门店数据
   * @returns {Promise} Promise对象
   */
  createStore(storeData) {
    console.log('调用 createStore API，参数:', JSON.stringify(storeData));

    // 确保所有必需字段都存在
    if (!storeData.Name) {
      throw new Error('门店名称不能为空');
    }
    if (!storeData.Address) {
      throw new Error('门店地址不能为空');
    }
    if (!storeData.Phone) {
      throw new Error('联系电话不能为空');
    }
    // Manager字段验证已移除

    // 直接使用传入的数据，不进行字段名称转换
    // 前端页面已经使用首字母大写的字段名称
    const requestData = storeData;
    console.log('包装后的请求数据:', JSON.stringify(requestData));

    return request.post('/api/store', requestData).then(response => {
      console.log('createStore API 响应:', response);
      return {
        code: 0,
        message: 'success',
        data: response
      };
    }).catch(error => {
      console.error('createStore API 错误:', error);
      throw error;
    });
  },

  /**
   * 更新门店
   * @param {Number} id 门店ID
   * @param {Object} storeData 门店数据
   * @returns {Promise} Promise对象
   */
  updateStore(id, storeData) {
    console.log(`调用 updateStore API，ID: ${id}，参数:`, JSON.stringify(storeData));

    // 确保所有必需字段都存在
    if (!storeData.Name) {
      throw new Error('门店名称不能为空');
    }
    if (!storeData.Address) {
      throw new Error('门店地址不能为空');
    }
    if (!storeData.Phone) {
      throw new Error('联系电话不能为空');
    }
    // Manager字段验证已移除

    // 直接使用传入的数据，不进行字段名称转换
    // 前端页面已经使用首字母大写的字段名称
    const requestData = storeData;
    console.log('包装后的请求数据:', JSON.stringify(requestData));

    return request.put(`/api/store/${id}`, requestData).then(response => {
      console.log('updateStore API 响应:', response);
      return {
        code: 0,
        message: 'success',
        data: response
      };
    }).catch(error => {
      console.error('updateStore API 错误:', error);
      throw error;
    });
  },

  /**
   * 删除门店
   * @param {Number} id 门店ID
   * @returns {Promise} Promise对象
   */
  deleteStore(id) {
    return request.delete(`/api/store/${id}`).then(response => {
      return response.data || {
        code: 0,
        message: 'success'
      };
    });
  },

  /**
   * 软删除门店
   * @param {Number} id 门店ID
   * @param {Object} data 删除原因等数据
   * @returns {Promise} Promise对象
   */
  async softDeleteStore(id, data) {
    console.log('调用软删除门店API，参数:', { id, data });

    try {
      // 先获取门店详情，检查是否已被删除
      const storeDetail = await this.getStoreDetail(id);
      console.log('获取门店详情用于检查:', storeDetail);

      if (storeDetail && storeDetail.data && storeDetail.data.isDeleted) {
        console.warn('门店已被删除，无法再次删除');
        return {
          code: 1,
          message: '门店已被删除，无法再次删除',
          detailedMessage: '门店已被删除，无法再次删除'
        };
      }

      // 继续执行删除操作
      const response = await request.post(`/api/store/${id}/soft-delete`, data);
      console.log('软删除门店API响应:', response);

      return response.data || {
        code: 0,
        message: 'success'
      };
    } catch (error) {
      console.error('软删除门店API错误:', error);

      // 检查是否包含"已被删除"的错误信息
      if (error.message && error.message.includes('已被删除')) {
        return {
          code: 1,
          message: '门店已被删除，无法再次删除',
          detailedMessage: '门店已被删除，无法再次删除'
        };
      }

      // 抛出错误，让调用者处理
      throw error;
    }
  },

  /**
   * 一键关闭并软删除门店
   * @param {Number} id 门店ID
   * @param {Object} data 删除原因等数据
   * @returns {Promise} Promise对象
   */
  async closeAndDeleteStore(id, data) {
    console.log('调用一键关闭并删除门店API，参数:', { id, data });

    try {
      // 先获取门店详情，检查是否已被删除
      const storeDetail = await this.getStoreDetail(id);
      console.log('获取门店详情用于检查:', storeDetail);

      if (storeDetail && storeDetail.data && storeDetail.data.isDeleted) {
        console.warn('门店已被删除，无法再次删除');
        return {
          code: 1,
          message: '门店已被删除，无法再次删除',
          detailedMessage: '门店已被删除，无法再次删除'
        };
      }

      // 继续执行删除操作
      const response = await request.post(`/api/store/${id}/close-and-delete`, data);
      console.log('一键关闭并删除门店API响应:', response);

      // 检查响应格式
      if (response && typeof response === 'object') {
        // 如果响应中有data字段，返回整个响应
        if (response.data) {
          return response;
        }

        // 如果响应本身就是数据，包装为标准格式
        return {
          code: 0,
          message: 'success',
          data: response
        };
      }

      // 默认返回成功响应
      return {
        code: 0,
        message: 'success'
      };
    } catch (error) {
      console.error('一键关闭并删除门店API错误:', error);

      // 检查是否包含"已被删除"的错误信息
      if (error.message && error.message.includes('已被删除')) {
        return {
          code: 1,
          message: '门店已被删除，无法再次删除',
          detailedMessage: '门店已被删除，无法再次删除'
        };
      }

      // 构造错误响应
      const errorResponse = {
        code: 1,
        message: error.message || '操作失败'
      };

      // 如果错误对象中有详细信息，添加到响应中
      if (error.detailedMessage) {
        errorResponse.detailedMessage = error.detailedMessage;
      }

      // 抛出错误，让调用者处理
      throw errorResponse;
    }
  },

  /**
   * 恢复已删除的门店
   * @param {Number} id 门店ID
   * @param {Object} data 恢复原因等数据
   * @returns {Promise} Promise对象
   */
  restoreStore(id, data) {
    console.log('调用恢复门店API，参数:', { id, data });

    return request.post(`/api/store/${id}/restore`, data)
      .then(response => {
        console.log('恢复门店API响应:', response);

        // 检查响应格式
        if (!response) {
          console.warn('恢复门店API响应为空');
          return {
            code: 0,
            message: 'success'
          };
        }

        // 如果响应已经是标准格式，直接返回
        if (response.code !== undefined) {
          return response;
        }

        // 如果响应包含data字段，直接返回
        if (response.data) {
          return response;
        }

        // 其他情况，包装为标准格式
        return {
          code: 0,
          message: 'success',
          data: response
        };
      })
      .catch(error => {
        console.error('恢复门店API错误:', error);

        // 检查是否包含特定错误信息
        if (error.message && error.message.includes('未被删除')) {
          return {
            code: 1,
            message: '门店未被删除，无需恢复',
            detailedMessage: '门店未被删除，无需恢复'
          };
        }

        // 抛出错误，让调用者处理
        throw error;
      });
  },

  /**
   * 获取已删除的门店列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getDeletedStores(params = {}) {
    console.log('调用获取已删除门店列表API，参数:', params);

    return request.get('/api/store/deleted', params)
      .then(response => {
        console.log('获取已删除门店列表API响应:', response);

        // 检查响应格式
        if (!response) {
          console.warn('获取已删除门店列表API响应为空');
          return {
            code: 0,
            message: 'success',
            data: {
              items: [],
              totalItems: 0,
              totalPages: 0,
              page: 1,
              pageSize: 10
            }
          };
        }

        // 如果响应已经是标准格式，直接返回
        if (response.code !== undefined) {
          return response;
        }

        // 如果响应是数组，包装为标准格式
        if (Array.isArray(response)) {
          return {
            code: 0,
            message: 'success',
            data: {
              items: response,
              totalItems: response.length,
              totalPages: 1,
              page: 1,
              pageSize: response.length
            }
          };
        }

        // 如果响应包含items字段，包装为标准格式
        if (response.items) {
          return {
            code: 0,
            message: 'success',
            data: response
          };
        }

        // 如果响应包含data字段，直接返回
        if (response.data) {
          return response;
        }

        // 其他情况，包装为标准格式
        return {
          code: 0,
          message: 'success',
          data: {
            items: [],
            totalItems: 0,
            totalPages: 0,
            page: 1,
            pageSize: 10
          }
        };
      })
      .catch(error => {
        console.error('获取已删除门店列表API错误:', error);
        throw error;
      });
  },

  /**
   * 获取附近门店
   * @param {Object} params 查询参数，包含latitude、longitude和batteryId
   * @returns {Promise} Promise对象
   */
  getNearbyStores(params = {}) {
    // 构建查询参数
    const queryParams = {
      latitude: params.latitude,
      longitude: params.longitude,
      maxDistance: params.maxDistance || 5000
    };

    // 如果有电池ID，添加到查询参数
    if (params.batteryId) {
      queryParams.batteryId = params.batteryId;
    }

    console.log('发送请求到 /api/store/nearby，参数:', queryParams);

    return request.get('/api/store/nearby', queryParams)
      .then(response => {
        console.log('获取附近门店响应:', response);

        // 处理响应数据
        if (Array.isArray(response)) {
          return {
            code: 0,
            message: 'success',
            data: response
          };
        }

        // 默认返回空数组
        return {
          code: 0,
          message: 'success',
          data: []
        };
      })
      .catch(error => {
        console.error('获取附近门店失败:', error);
        throw error;
      });
  },

  /**
   * 获取门店库存
   * @param {Number} storeId 门店ID
   * @returns {Promise} Promise对象
   */
  getStoreInventory(storeId) {
    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    // 添加时间戳，避免缓存
    const params = {
      _t: Date.now(),
      keyword: 'empty' // 添加空关键词，避免筛选
    };

    // 使用新的 API 端点
    return request.get(`/api/store-inventory/${storeId}`, params)
      .then(response => {
        console.log('获取门店库存响应:', response);

        if (!response) {
          console.warn('门店库存响应为空');
          return { success: true, data: [] };
        }

        if (response.success === false) {
          throw new Error(response.message || '获取门店库存失败');
        }

        // 如果响应是数组，包装为标准格式
        if (Array.isArray(response)) {
          return { success: true, data: response };
        }

        // 如果响应包含 data 字段，直接返回
        if (response.data) {
          return response;
        }

        // 其他情况，包装为标准格式
        return { success: true, data: response };
      })
      .catch(error => {
        console.error('获取门店库存失败:', error);
        throw error;
      });
  },

  /**
   * 更新门店库存
   * @param {Number} storeId 门店ID
   * @param {Object} inventoryData 库存数据
   * @returns {Promise} Promise对象
   */
  updateStoreInventory(storeId, inventoryData) {
    return request.put(`/api/store/${storeId}/inventory`, inventoryData).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 为门店分配商品
   * @param {Object} data 分配数据，包含storeId和products数组
   * @returns {Promise} Promise对象
   */
  assignProductsToStore(data) {
    console.log('调用 assignProductsToStore API，参数:', data);

    // 验证参数
    if (!data.storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    if (!data.products || !Array.isArray(data.products) || data.products.length === 0) {
      return Promise.reject(new Error('缺少商品数据'));
    }

    // 确保每个商品都有ID和数量
    for (const product of data.products) {
      if (!product.productId) {
        return Promise.reject(new Error('商品缺少ID'));
      }
      if (!product.quantity || product.quantity <= 0) {
        return Promise.reject(new Error('商品数量必须大于0'));
      }
    }

    return request.post(`/api/store/${data.storeId}/assign-products`, {
      products: data.products
    }).then(response => {
      console.log('assignProductsToStore API 响应:', response);

      if (!response) {
        throw new Error('服务器返回空响应');
      }

      // 检查响应格式
      if (response.success === false) {
        throw new Error(response.message || '商品分配失败');
      }

      return {
        success: true,
        message: '商品分配成功',
        data: response
      };
    }).catch(error => {
      console.error('assignProductsToStore API 错误:', error);
      // 不再使用模拟数据，直接抛出错误
      throw error;
    });
  }
};

export default StoreAPI;
