using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using BatteryApi.DTOs.Product;
using BatteryApi.DTOs.Battery;
using BatteryApi.Services.Interfaces;
using BatteryApi.DTOs.Common;
using BatteryApi.Models;
using SqlSugar;

namespace BatteryApi.Controllers;

/// <summary>
/// 商品管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProductController : ControllerBase
{
    private readonly IProductService _productService;
    private readonly ILogger<ProductController> _logger;
    private readonly ISqlSugarClient _db;

    public ProductController(IProductService productService, ILogger<ProductController> logger, ISqlSugarClient db)
    {
        _productService = productService;
        _logger = logger;
        _db = db;
    }

    /// <summary>
    /// 创建商品
    /// </summary>
    /// <param name="request">创建商品请求</param>
    /// <returns>创建的商品信息</returns>
    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] CreateProductRequest request)
    {
        try
        {
            Console.WriteLine("📋 接收到创建商品请求");
            Console.WriteLine($"📋 商品名称: {request.Name}");
            Console.WriteLine($"📋 规格: {request.Spec}");
            Console.WriteLine($"📋 分类ID: {request.CategoryId}");
            Console.WriteLine($"📋 MainImages 数量: {request.MainImages?.Count ?? 0}");
            Console.WriteLine($"📋 MainImagesFiles 数量: {request.MainImagesFiles?.Count ?? 0}");
            
            if (request.MainImages != null)
            {
                for (int i = 0; i < request.MainImages.Count; i++)
                {
                    var image = request.MainImages[i];
                    Console.WriteLine($"📎 图片 {i + 1}: {image.Name}, 临时路径: {image.TempPath}");
                }
            }

            var result = await _productService.CreateProductAsync(request);
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "商品创建成功",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = $"发生了内部服务器错误：{ex.Message}"
            });
        }
    }

    /// <summary>
    /// 更新商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <param name="request">更新商品请求</param>
    /// <returns>更新的商品信息</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateProduct(int id, [FromBody] UpdateProductRequest request)
    {
        try
        {
            Console.WriteLine($"📋 接收到更新商品请求，ID: {id}");
            Console.WriteLine($"📋 商品名称: {request.Name}");
            Console.WriteLine($"📋 规格: {request.Spec}");
            Console.WriteLine($"📋 MainImages 数量: {request.MainImages?.Count ?? 0}");

            var result = await _productService.UpdateProductAsync(id, request);
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "商品更新成功",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = $"发生了内部服务器错误：{ex.Message}"
            });
        }
    }

    /// <summary>
    /// 获取商品详情
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>商品详情</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetProduct(int id)
    {
        try
        {
            var result = await _productService.GetProductByIdAsync(id);
            
            if (result == null)
            {
                return NotFound(new ApiResponse<object>
                {
                    Code = 404,
                    Message = "商品不存在"
                });
            }
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "获取商品详情成功",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品详情失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = $"发生了内部服务器错误：{ex.Message}"
            });
        }
    }

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <param name="categoryId">分类ID</param>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="status">状态</param>
    /// <returns>商品列表</returns>
    [HttpGet]
    [AllowAnonymous] // 临时允许匿名访问以测试
    public async Task<IActionResult> GetProducts(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] int? categoryId = null,
        [FromQuery] string? keyword = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var result = await _productService.GetProductsAsync(page, pageSize, categoryId, keyword, status);
            
            return Ok(new ApiResponse<PagedResult<ProductDto>>
            {
                Code = 200,
                Message = "获取商品列表成功",
                Data = result
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品列表失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = $"发生了内部服务器错误：{ex.Message}"
            });
        }
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteProduct(int id)
    {
        try
        {
            await _productService.DeleteProductAsync(id);
            
            return Ok(new ApiResponse<object>
            {
                Code = 200,
                Message = "商品删除成功"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = $"发生了内部服务器错误：{ex.Message}"
            });
        }
    }

    /// <summary>
    /// 测试端点 - 查看请求数据
    /// </summary>
    [HttpPost("test")]
    [AllowAnonymous]
    public IActionResult TestCreateProduct([FromBody] CreateProductRequest request)
    {
        try
        {
            Console.WriteLine("📋 测试端点 - 接收到创建商品请求");
            Console.WriteLine($"📋 请求数据: {System.Text.Json.JsonSerializer.Serialize(request)}");

            return Ok(new
            {
                code = 200,
                message = "测试成功",
                data = new
                {
                    receivedData = request,
                    mainImagesCount = request.MainImages?.Count ?? 0,
                    mainImagesFilesCount = request.MainImagesFiles?.Count ?? 0,
                    servicesCount = request.Services?.Count ?? 0,
                    installationFeesCount = request.InstallationFees?.Count ?? 0
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 测试端点异常: {ex.Message}");
            return StatusCode(500, new
            {
                code = 500,
                message = "测试失败",
                error = ex.Message
            });
        }
    }

    // ==================== 商品规格管理 API ====================

    /// <summary>
    /// 获取所有商品规格
    /// </summary>
    /// <returns>商品规格列表</returns>
    [HttpGet("specs")]
    [AllowAnonymous]
    public async Task<ActionResult<List<BatterySpecDto>>> GetProductSpecs()
    {
        try
        {
            Console.WriteLine("📋 开始获取商品规格列表");

            var specs = await _db.Queryable<BatterySpec>()
                .OrderBy(s => s.CategoryId)
                .OrderBy(s => s.Name)
                .ToListAsync();

            var specDtos = specs.Select(MapToSpecDto).ToList();

            Console.WriteLine($"📋 成功获取商品规格列表，数量: {specDtos.Count}");
            return Ok(specDtos);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 获取商品规格列表失败: {ex.Message}");
            return StatusCode(500, new { message = "获取商品规格列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 根据分类ID获取商品规格列表
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>商品规格列表</returns>
    [HttpGet("specs/category/{categoryId:int}")]
    [AllowAnonymous]
    public async Task<ActionResult<List<BatterySpecDto>>> GetProductSpecsByCategoryId(int categoryId)
    {
        try
        {
            Console.WriteLine($"📋 开始获取分类商品规格列表，分类ID: {categoryId}");

            var specs = await _db.Queryable<BatterySpec>()
                .Where(s => s.CategoryId == categoryId)
                .OrderBy(s => s.Name)
                .ToListAsync();

            var specDtos = specs.Select(MapToSpecDto).ToList();

            Console.WriteLine($"📋 成功获取分类商品规格列表，分类ID: {categoryId}, 数量: {specDtos.Count}");
            return Ok(specDtos);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 获取分类商品规格列表失败，分类ID: {categoryId}, 错误: {ex.Message}");
            return StatusCode(500, new { message = "获取分类商品规格列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 创建新的商品规格
    /// </summary>
    /// <param name="request">创建商品规格请求</param>
    /// <returns>创建的商品规格</returns>
    [HttpPost("specs")]
    [AllowAnonymous] // 临时允许匿名访问以测试
    public async Task<ActionResult<BatterySpecDto>> CreateProductSpec(CreateBatterySpecRequest request)
    {
        try
        {
            Console.WriteLine($"📋 开始创建商品规格: {request.Name}");

            // 检查规格名称是否已存在
            var existingSpec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Name == request.Name && s.CategoryId == request.CategoryId);

            if (existingSpec != null)
            {
                return BadRequest(new { message = $"分类中已存在名称为 '{request.Name}' 的规格" });
            }

            // 创建新规格
            var spec = new BatterySpec
            {
                Name = request.Name,
                Voltage = request.Voltage,
                Capacity = request.Capacity,
                Weight = request.Weight,
                Dimensions = request.Dimensions,
                Price = request.Price,
                Description = request.Description,
                ImageUrl = request.ImageUrl,
                CategoryId = request.CategoryId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            await _db.Insertable(spec).ExecuteCommandAsync();

            var specDto = MapToSpecDto(spec);
            Console.WriteLine($"📋 成功创建商品规格: {spec.Name}, ID: {spec.Id}");

            return CreatedAtAction(nameof(GetProductSpecById), new { id = spec.Id }, specDto);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 创建商品规格失败: {request.Name}, 错误: {ex.Message}");
            return StatusCode(500, new { message = "创建商品规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 根据ID获取商品规格详情
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <returns>商品规格详情</returns>
    [HttpGet("specs/{id:int}")]
    [AllowAnonymous]
    public async Task<ActionResult<BatterySpecDto>> GetProductSpecById(int id)
    {
        try
        {
            Console.WriteLine($"📋 开始获取商品规格详情，ID: {id}");

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的商品规格" });
            }

            var specDto = MapToSpecDto(spec);
            Console.WriteLine($"📋 成功获取商品规格详情: {spec.Name}");
            return Ok(specDto);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 获取商品规格详情失败，ID: {id}, 错误: {ex.Message}");
            return StatusCode(500, new { message = "获取商品规格详情失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 更新商品规格
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <param name="request">更新商品规格请求</param>
    /// <returns>更新后的商品规格</returns>
    [HttpPut("specs/{id:int}")]
    [AllowAnonymous] // 临时允许匿名访问以测试
    public async Task<ActionResult<BatterySpecDto>> UpdateProductSpec(int id, UpdateBatterySpecRequest request)
    {
        try
        {
            Console.WriteLine($"📋 开始更新商品规格，ID: {id}");

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的商品规格" });
            }

            // 检查规格名称是否与其他规格冲突
            var existingSpec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Name == request.Name && s.Id != id);

            if (existingSpec != null)
            {
                return BadRequest(new { message = $"已存在名称为 '{request.Name}' 的规格" });
            }

            // 更新规格信息
            spec.Name = request.Name;
            spec.Voltage = request.Voltage;
            spec.Capacity = request.Capacity;
            spec.Weight = request.Weight;
            spec.Dimensions = request.Dimensions;
            spec.Price = request.Price;
            spec.Description = request.Description;
            spec.ImageUrl = request.ImageUrl;
            spec.UpdatedAt = DateTime.Now;

            await _db.Updateable(spec).ExecuteCommandAsync();

            var specDto = MapToSpecDto(spec);
            Console.WriteLine($"📋 成功更新商品规格: {spec.Name}");

            return Ok(specDto);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 更新商品规格失败，ID: {id}, 错误: {ex.Message}");
            return StatusCode(500, new { message = "更新商品规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 删除商品规格
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("specs/{id:int}")]
    [AllowAnonymous] // 临时允许匿名访问以测试
    public async Task<ActionResult> DeleteProductSpec(int id)
    {
        try
        {
            Console.WriteLine($"📋 开始删除商品规格，ID: {id}");

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的商品规格" });
            }

            // 检查是否有商品使用此规格
            var productCount = await _db.Queryable<Product>()
                .CountAsync(p => p.Model == spec.Name);

            if (productCount > 0)
            {
                return BadRequest(new { message = $"无法删除规格 '{spec.Name}'，因为有 {productCount} 个商品正在使用此规格" });
            }

            await _db.Deleteable<BatterySpec>().Where(s => s.Id == id).ExecuteCommandAsync();

            Console.WriteLine($"📋 成功删除商品规格: {spec.Name}");
            return Ok(new { message = "商品规格删除成功", id = id });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 删除商品规格失败，ID: {id}, 错误: {ex.Message}");
            return StatusCode(500, new { message = "删除商品规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 将BatterySpec实体映射为DTO
    /// </summary>
    /// <param name="spec">BatterySpec实体</param>
    /// <returns>BatterySpecDto</returns>
    private static BatterySpecDto MapToSpecDto(BatterySpec spec)
    {
        return new BatterySpecDto
        {
            Id = spec.Id,
            Name = spec.Name,
            Voltage = spec.Voltage,
            Capacity = spec.Capacity,
            Weight = spec.Weight,
            Dimensions = spec.Dimensions,
            Price = spec.Price,
            Description = spec.Description,
            ImageUrl = spec.ImageUrl,
            CategoryId = spec.CategoryId,
            CreatedAt = spec.CreatedAt,
            UpdatedAt = spec.UpdatedAt,
            IsActive = spec.IsActive,
            DisplayOrder = spec.DisplayOrder
        };
    }
}
