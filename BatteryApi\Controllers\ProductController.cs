using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using BatteryApi.DTOs.Product;
using BatteryApi.Services.Interfaces;
using BatteryApi.DTOs.Common;

namespace BatteryApi.Controllers;

/// <summary>
/// 商品管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProductController : ControllerBase
{
    private readonly IProductService _productService;
    private readonly ILogger<ProductController> _logger;

    public ProductController(IProductService productService, ILogger<ProductController> logger)
    {
        _productService = productService;
        _logger = logger;
    }

    /// <summary>
    /// 创建商品
    /// </summary>
    /// <param name="request">创建商品请求</param>
    /// <returns>创建的商品信息</returns>
    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] CreateProductRequest request)
    {
        try
        {
            Console.WriteLine("📋 接收到创建商品请求");
            Console.WriteLine($"📋 商品名称: {request.Name}");
            Console.WriteLine($"📋 规格: {request.Spec}");
            Console.WriteLine($"📋 分类ID: {request.CategoryId}");
            Console.WriteLine($"📋 MainImages 数量: {request.MainImages?.Count ?? 0}");
            Console.WriteLine($"📋 MainImagesFiles 数量: {request.MainImagesFiles?.Count ?? 0}");
            
            if (request.MainImages != null)
            {
                for (int i = 0; i < request.MainImages.Count; i++)
                {
                    var image = request.MainImages[i];
                    Console.WriteLine($"📎 图片 {i + 1}: {image.Name}, 临时路径: {image.TempPath}");
                }
            }

            var result = await _productService.CreateProductAsync(request);
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "商品创建成功",
                Data = result,
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = "发生了内部服务器错误。请稍后再试。",
                DetailedMessage = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// 更新商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <param name="request">更新商品请求</param>
    /// <returns>更新的商品信息</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateProduct(int id, [FromBody] UpdateProductRequest request)
    {
        try
        {
            Console.WriteLine($"📋 接收到更新商品请求，ID: {id}");
            Console.WriteLine($"📋 商品名称: {request.Name}");
            Console.WriteLine($"📋 规格: {request.Spec}");
            Console.WriteLine($"📋 MainImages 数量: {request.MainImages?.Count ?? 0}");

            var result = await _productService.UpdateProductAsync(id, request);
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "商品更新成功",
                Data = result,
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = "发生了内部服务器错误。请稍后再试。",
                DetailedMessage = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// 获取商品详情
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>商品详情</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetProduct(int id)
    {
        try
        {
            var result = await _productService.GetProductByIdAsync(id);
            
            if (result == null)
            {
                return NotFound(new ApiResponse<object>
                {
                    Code = 404,
                    Message = "商品不存在",
                    Success = false
                });
            }
            
            return Ok(new ApiResponse<ProductDto>
            {
                Code = 200,
                Message = "获取商品详情成功",
                Data = result,
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品详情失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = "发生了内部服务器错误。请稍后再试。",
                DetailedMessage = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <param name="categoryId">分类ID</param>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="status">状态</param>
    /// <returns>商品列表</returns>
    [HttpGet]
    public async Task<IActionResult> GetProducts(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] int? categoryId = null,
        [FromQuery] string? keyword = null,
        [FromQuery] string? status = null)
    {
        try
        {
            var result = await _productService.GetProductsAsync(page, pageSize, categoryId, keyword, status);
            
            return Ok(new ApiResponse<PagedResult<ProductDto>>
            {
                Code = 200,
                Message = "获取商品列表成功",
                Data = result,
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品列表失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = "发生了内部服务器错误。请稍后再试。",
                DetailedMessage = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteProduct(int id)
    {
        try
        {
            await _productService.DeleteProductAsync(id);
            
            return Ok(new ApiResponse<object>
            {
                Code = 200,
                Message = "商品删除成功",
                Success = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除商品失败");
            return StatusCode(500, new ApiResponse<object>
            {
                Code = 500,
                Message = "发生了内部服务器错误。请稍后再试。",
                DetailedMessage = ex.Message,
                Success = false
            });
        }
    }

    /// <summary>
    /// 测试端点 - 查看请求数据
    /// </summary>
    [HttpPost("test")]
    [AllowAnonymous]
    public IActionResult TestCreateProduct([FromBody] CreateProductRequest request)
    {
        try
        {
            Console.WriteLine("📋 测试端点 - 接收到创建商品请求");
            Console.WriteLine($"📋 请求数据: {System.Text.Json.JsonSerializer.Serialize(request)}");
            
            return Ok(new
            {
                code = 200,
                message = "测试成功",
                data = new
                {
                    receivedData = request,
                    mainImagesCount = request.MainImages?.Count ?? 0,
                    mainImagesFilesCount = request.MainImagesFiles?.Count ?? 0,
                    servicesCount = request.Services?.Count ?? 0,
                    installationFeesCount = request.InstallationFees?.Count ?? 0
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"📋 测试端点异常: {ex.Message}");
            return StatusCode(500, new
            {
                code = 500,
                message = "测试失败",
                error = ex.Message
            });
        }
    }
}
