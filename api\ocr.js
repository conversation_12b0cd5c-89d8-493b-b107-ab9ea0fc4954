/**
 * OCR识别API服务
 */
import request from '@/utils/request';

// API接口
const OcrAPI = {
  /**
   * 识别身份证信息
   * @param {Object} data 请求数据
   * @param {File|string} data.image 身份证图片文件或路径
   * @param {string} data.side 身份证正反面 (front/back)
   * @returns {Promise} Promise对象
   */
  recognizeIdCard(data) {
    console.log('识别身份证请求:', data);

    // 如果是H5环境，直接使用uni.uploadFile上传
    // 这个方法在verification.vue中直接调用了uni.uploadFile
    // 保留此方法是为了兼容性，以防其他地方调用

    // 如果传入的是File对象，使用FormData上传
    if (data.image instanceof File) {
      const formData = new FormData();
      formData.append('image', data.image);
      formData.append('side', data.side);

      return request.post('/api/ocr/idcard', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(response => {
        console.log('识别身份证成功:', response);
        return response;
      })
      .catch(error => {
        console.error('识别身份证失败:', error);
        throw error;
      });
    }
    // 如果传入的是文件路径，返回一个Promise，提示使用uni.uploadFile
    else {
      console.warn('请直接使用uni.uploadFile上传图片到OCR接口');
      return Promise.reject(new Error('请直接使用uni.uploadFile上传图片到OCR接口'));
    }
  }
};

export default OcrAPI;
