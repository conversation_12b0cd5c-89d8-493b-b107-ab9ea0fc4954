using SqlSugar;

namespace BatteryApi.Models
{
    /// <summary>
    /// 库存调拨记录
    /// </summary>
    [SugarTable("InventoryTransfer")]
    public class InventoryTransfer
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 源门店ID
        /// </summary>
        public int FromStoreId { get; set; }

        /// <summary>
        /// 目标门店ID
        /// </summary>
        public int ToStoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 调拨数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500)]
        public string Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 源门店
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(FromStoreId))]
        public Store FromStore { get; set; }

        /// <summary>
        /// 目标门店
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ToStoreId))]
        public Store ToStore { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductId))]
        public Product Product { get; set; }
    }
}
