using SqlSugar;

namespace BatteryApi.Models;

/// <summary>
/// 商品实体
/// </summary>
[SugarTable("Products")]
public class Product
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 商品规格/型号
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Spec { get; set; } = string.Empty;

    /// <summary>
    /// 商品状态 (Available, Rented, Sold, Maintenance)
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string Status { get; set; } = "Available";

    /// <summary>
    /// 销售价格
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Price { get; set; }

    /// <summary>
    /// 租赁价格（日租金）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal RentPrice { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime ManufactureDate { get; set; }

    /// <summary>
    /// 使用寿命（月）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int Lifespan { get; set; } = 36;

    /// <summary>
    /// 商品描述
    /// </summary>
    [SugarColumn(Length = 1000)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(Length = 500)]
    public string Notes { get; set; } = string.Empty;

    /// <summary>
    /// 分类ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int CategoryId { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    [SugarColumn(Length = 50)]
    public string CategoryCode { get; set; } = string.Empty;

    /// <summary>
    /// 分类名称
    /// </summary>
    [SugarColumn(Length = 100)]
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 电压
    /// </summary>
    [SugarColumn(Length = 20)]
    public string Voltage { get; set; } = string.Empty;

    /// <summary>
    /// 容量
    /// </summary>
    [SugarColumn(Length = 20)]
    public string Capacity { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    [SugarColumn(Length = 20)]
    public string CycleCount { get; set; } = string.Empty;

    /// <summary>
    /// 充电时间
    /// </summary>
    [SugarColumn(Length = 20)]
    public string ChargeTime { get; set; } = string.Empty;

    /// <summary>
    /// 关联的电池ID（用于BMS信息）
    /// 注意：这个字段保留用于BMS功能，但不再关联到Battery实体
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? BatteryId { get; set; }

    /// <summary>
    /// 商品分类（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(CategoryId))]
    public BatteryCategory? Category { get; set; }

    /// <summary>
    /// 关联的图片列表（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ProductImage.ProductId))]
    public List<ProductImage> Images { get; set; } = new List<ProductImage>();

    /// <summary>
    /// 关联的服务列表（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ProductService.ProductId))]
    public List<ProductService> Services { get; set; } = new List<ProductService>();

    /// <summary>
    /// 关联的安装费用列表（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ProductInstallationFee.ProductId))]
    public List<ProductInstallationFee> InstallationFees { get; set; } = new List<ProductInstallationFee>();

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}
