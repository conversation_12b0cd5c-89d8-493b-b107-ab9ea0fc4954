<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Product API 测试页面</h1>
    
    <div class="test-section">
        <h3>1. 测试获取商品列表</h3>
        <button onclick="testGetProducts()">获取商品列表</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试获取商品分类</h3>
        <button onclick="testGetCategories()">获取商品分类</button>
        <div id="categories-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试创建商品</h3>
        <button onclick="testCreateProduct()">创建测试商品</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试获取商品详情</h3>
        <input type="number" id="product-id" placeholder="输入商品ID" value="1">
        <button onclick="testGetProductDetail()">获取商品详情</button>
        <div id="detail-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5242';

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testGetProducts() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.textContent = '正在请求...';
            
            const result = await makeRequest('/api/product?page=1&pageSize=10');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        async function testGetCategories() {
            const resultDiv = document.getElementById('categories-result');
            resultDiv.textContent = '正在请求...';
            
            const result = await makeRequest('/api/battery/categories');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        async function testCreateProduct() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.textContent = '正在请求...';
            
            const testProduct = {
                name: '测试商品',
                productCode: 'TEST-' + Date.now(),
                categoryId: 1,
                spec: '测试规格',
                price: 100.00,
                rentPrice: 10.00,
                status: 'Available',
                description: '这是一个测试商品',
                voltage: '12V',
                capacity: '100Ah',
                cycleCount: '3000',
                chargeTime: '4小时',
                lifespan: 36,
                manufactureDate: new Date().toISOString().split('T')[0]
            };
            
            const result = await makeRequest('/api/product', {
                method: 'POST',
                body: JSON.stringify(testProduct)
            });
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        async function testGetProductDetail() {
            const resultDiv = document.getElementById('detail-result');
            const productId = document.getElementById('product-id').value;
            
            if (!productId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入商品ID';
                return;
            }
            
            resultDiv.textContent = '正在请求...';
            
            const result = await makeRequest(`/api/product/${productId}`);
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            console.log('页面加载完成，开始测试API连接...');
            testGetCategories();
        };
    </script>
</body>
</html>
