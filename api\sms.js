/**
 * 短信API服务
 * 提供发送和验证短信验证码的API接口
 */
import request from '@/utils/request';

// API接口
const SmsAPI = {
  /**
   * 发送验证码
   * @param {Object} data 请求数据
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.type 验证码类型:
   *   - register: 注册验证码
   *   - login: 登录验证码
   *   - reset: 重置密码验证码
   *   - password: 修改密码验证码
   *   - verification: 实名认证验证码
   * @returns {Promise} Promise对象，返回发送结果
   */
  sendVerificationCode(data) {
    console.log('发送验证码请求:', data);

    return request.post('/api/sms/send', {
      phoneNumber: data.phoneNumber,
      type: data.type
    })
    .then(response => {
      console.log('发送验证码成功:', response);
      return response;
    })
    .catch(error => {
      console.error('发送验证码失败:', error);
      // 统一错误处理
      const errorMsg = error.message || '发送验证码失败，请稍后再试';
      throw new Error(errorMsg);
    });
  },

  /**
   * 验证验证码
   * @param {Object} data 请求数据
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.code 验证码
   * @param {string} data.type 验证码类型:
   *   - register: 注册验证码
   *   - login: 登录验证码
   *   - reset: 重置密码验证码
   *   - password: 修改密码验证码
   *   - verification: 实名认证验证码
   * @returns {Promise} Promise对象，返回验证结果
   */
  verifyCode(data) {
    console.log('验证验证码请求:', data);

    return request.post('/api/sms/verify', {
      phoneNumber: data.phoneNumber,
      code: data.code,
      type: data.type
    })
    .then(response => {
      console.log('验证验证码成功:', response);
      return response;
    })
    .catch(error => {
      console.error('验证验证码失败:', error);
      // 统一错误处理
      const errorMsg = error.message || '验证码验证失败，请稍后再试';
      throw new Error(errorMsg);
    });
  }
};

export default SmsAPI;
