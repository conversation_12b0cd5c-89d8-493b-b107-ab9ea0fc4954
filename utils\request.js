/**
 * request封装
 */
import store from '@/store'

// 请求基础URL
// 尝试从配置文件中获取基础URL
let baseURL = '';

try {
  // 导入配置文件
  const { apiConfig } = require('@/config/index');
  baseURL = apiConfig.baseUrl;
  console.log('使用配置文件中的API基础URL:', baseURL);
} catch (error) {
  console.error('加载配置文件失败，使用默认URL');
  // 如果加载配置文件失败，使用默认URL
  baseURL = 'http://localhost:5242';
}

// 尝试从本地存储中获取基础URL
try {
  const savedBaseUrl = uni.getStorageSync('apiBaseUrl');
  if (savedBaseUrl) {
    baseURL = savedBaseUrl;
    console.log('使用存储的API基础URL:', baseURL);
  }
} catch (error) {
  console.error('获取存储的API基础URL失败:', error);
}

// 请求队列
const pending = [];
// 刷新token的promise
let isRefreshing = false;
let requests = [];

/**
 * 请求拦截
 * @param {Object} config 请求配置
 */
function requestInterceptor(config) {
  // 白名单路径，不需要登录就可以访问
  const whiteList = [
    '/api/auth/login',
    '/api/auth/register',
    '/api/auth/register/phone', // 添加手机号注册接口
    '/api/auth/refresh',
    '/api/auth/refresh-token',
    '/api/auth/reset-password',
    '/api/auth/verify-code',
    '/api/sms/send',           // 添加发送短信验证码接口
    '/api/sms/verify',         // 添加验证短信验证码接口
    '/api/ocr/idcard',         // 添加身份证OCR识别接口
    '/api/auth/verification/recognize-idcard', // 身份证识别接口
    '/api/auth/verification/upload-idcard-front', // 上传身份证正面照片
    '/api/auth/verification/upload-idcard-back', // 上传身份证背面照片
    '/api/auth/verification/upload-face', // 上传人脸照片
    '/api/auth/verification/check-idcard-photos', // 检查身份证照片
    '/api/auth/verification/status', // 获取认证状态
    '/api/auth/verification/submit' // 提交认证信息
  ];

  // 检查是否是白名单路径
  const isWhiteListPath = whiteList.some(path => config.url.includes(path));

  // 记录请求URL和白名单状态
  console.log(`请求拦截器: 处理请求 ${config.url}, 白名单状态: ${isWhiteListPath ? '允许' : '需要登录'}`);

  // 检查是否是刷新令牌请求
  const isRefreshTokenRequest = config.url.includes('/api/auth/refresh-token');

  // 获取token
  const token = uni.getStorageSync('token');

  // 获取用户登录状态
  const isLoggedIn = store.getters['user/isLoggedIn'];

  // 如果是刷新令牌请求，但用户未登录，则中断请求
  if (isRefreshTokenRequest && !isLoggedIn) {
    console.log('请求拦截器: 未登录状态下尝试刷新令牌，中断请求');
    // 清除可能存在的无效令牌
    uni.removeStorageSync('token');
    uni.removeStorageSync('refreshToken');
    // 抛出错误，中断请求
    throw new Error('未登录状态下无法刷新令牌');
  }

  // 如果不是白名单路径，且没有token，则直接跳转到登录页
  if (!isWhiteListPath && !token) {
    console.log('请求拦截器: 未登录状态下尝试访问需要授权的接口，跳转到登录页');

    // 延迟执行，确保当前代码执行完毕
    setTimeout(() => {
      // 清除用户状态
      store.commit('user/CLEAR_USER');

      // 显示提示
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      });
    }, 100);

    // 抛出错误，中断请求
    throw new Error('未登录，请先登录');
  }

  // 添加token
  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${token}`
    };
  }

  // 处理请求队列
  const requestKey = `${config.url}&${config.method}`;
  pending.push(requestKey);

  return config;
}

/**
 * 响应拦截
 * @param {Object} response 响应对象
 */
async function responseInterceptor(response) {
  console.log('响应拦截器收到响应:', {
    statusCode: response.statusCode,
    url: response.config.url
  });

  // 移除请求队列中的当前请求
  const requestKey = `${response.config.url}&${response.config.method}`;
  const index = pending.indexOf(requestKey);
  if (index > -1) {
    pending.splice(index, 1);
  }

  // 如果响应状态码是401（未授权），尝试刷新token或跳转到登录页
  if (response.statusCode === 401) {
    console.log('收到401未授权响应');

    // 如果是登录接口或刷新token接口，直接返回错误
    if (response.config.url.includes('/api/auth/login') || response.config.url.includes('/api/auth/refresh-token')) {
      console.log('登录或刷新接口返回401，直接拒绝');
      return Promise.reject(response.data);
    }

    // 获取用户登录状态
    const isLoggedIn = store.getters['user/isLoggedIn'];

    // 如果用户未登录，不尝试刷新令牌
    if (!isLoggedIn) {
      console.log('用户未登录，不尝试刷新令牌');
      // 清除可能存在的无效令牌
      uni.removeStorageSync('token');
      uni.removeStorageSync('refreshToken');
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1000);
      return Promise.reject({ message: '请先登录' });
    }

    // 检查是否有刷新token
    const refreshToken = uni.getStorageSync('refreshToken');
    if (!refreshToken) {
      console.log('没有刷新token，直接跳转到登录页');
      // 清除用户状态
      store.commit('user/CLEAR_USER');
      // 跳转到登录页
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1000);
      return Promise.reject({ message: '登录已过期，请重新登录' });
    }

    if (!isRefreshing) {
      isRefreshing = true;
      console.log('开始刷新token');

      try {
        // 检查是否有刷新令牌
        const refreshToken = uni.getStorageSync('refreshToken');
        if (!refreshToken) {
          throw new Error('没有刷新令牌，无法刷新');
        }

        // 检查用户是否已登录
        const isLoggedIn = store.getters['user/isLoggedIn'];
        if (!isLoggedIn) {
          console.log('用户未登录，不尝试刷新令牌');
          throw new Error('用户未登录，不尝试刷新令牌');
        }

        console.log('开始刷新令牌...');
        // 尝试刷新token
        await store.dispatch('user/refreshToken');
        isRefreshing = false;
        console.log('token刷新成功');

        // 重新发送所有等待的请求
        requests.forEach(cb => cb());
        requests = [];

        // 使用新token重试当前请求
        const newToken = uni.getStorageSync('token');
        if (!newToken) {
          throw new Error('刷新后没有获取到新令牌');
        }

        response.config.header.Authorization = `Bearer ${newToken}`;
        console.log('使用新token重试请求:', response.config.url);
        return uni.request(response.config);
      } catch (error) {
        isRefreshing = false;
        requests = [];
        console.error('token刷新失败，详细错误:', error);

        // token刷新失败，清除用户状态并跳转到登录页
        store.commit('user/CLEAR_USER');
        uni.removeStorageSync('token');
        uni.removeStorageSync('refreshToken');

        // 显示友好的错误提示
        let errorMessage = '登录已过期，请重新登录';
        if (error && error.message) {
          console.error('错误消息:', error.message);
          // 可以根据错误消息定制提示
          if (error.message.includes('网络') || error.message.includes('timeout')) {
            errorMessage = '网络连接失败，请检查网络设置';
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });

        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1000);

        return Promise.reject(error);
      }
    } else {
      console.log('已经在刷新token，将请求加入队列');
      // 返回一个promise，将请求加入队列
      return new Promise(resolve => {
        requests.push(() => {
          resolve(uni.request(response.config));
        });
      });
    }
  }

  // 统一处理响应
  const res = response.data;

  // 如果是错误响应，处理错误
  if (response.statusCode >= 400) {
    // 获取错误消息
    let errorMessage = '';
    let detailedMessage = '';

    // 处理不同格式的错误响应
    if (typeof res === 'string') {
      errorMessage = res;
    } else if (res.message) {
      errorMessage = res.message;
      detailedMessage = res.detailedMessage || res.message;
    } else if (res.title) {
      errorMessage = res.title;
      detailedMessage = res.detail || res.title;
    } else if (res.error) {
      errorMessage = res.error;
      detailedMessage = res.error_description || res.error;
    } else {
      errorMessage = '请求失败';
    }

    console.error('收到错误响应:', {
      statusCode: response.statusCode,
      message: errorMessage,
      detailedMessage: detailedMessage,
      data: res
    });

    // 根据状态码自定义错误消息
    switch (response.statusCode) {
      case 400:
        if (!errorMessage.includes('无效') && !errorMessage.includes('参数')) {
          errorMessage = '请求参数无效: ' + errorMessage;
        }
        break;
      case 401:
        errorMessage = '未授权或登录已过期';
        break;
      case 403:
        errorMessage = '没有权限执行此操作';
        break;
      case 404:
        errorMessage = '请求的资源不存在';
        break;
      case 500:
        errorMessage = '服务器内部错误，请稍后再试';
        break;
    }

    // 对于登录错误，不显示错误提示，而是让登录页面自己处理
    if (!response.config.url.includes('/api/auth/login')) {
      // 显示错误提示，使用对话框而不是Toast，以显示更多信息
      if (detailedMessage && detailedMessage !== errorMessage) {
        uni.showModal({
          title: '操作失败',
          content: errorMessage + '\n\n' + detailedMessage,
          showCancel: false
        });
      } else {
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    }

    // 如果是401错误，跳转到登录页面
    if (response.statusCode === 401) {
      // 清除用户信息
      store.commit('user/CLEAR_USER');

      // 跳转到登录页面
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    }

    return Promise.reject({
      statusCode: response.statusCode,
      message: errorMessage,
      detailedMessage: detailedMessage,
      ...res
    });
  }

  // 返回数据
  console.log('响应拦截器处理完成，返回数据');
  return res;
}

/**
 * 发送请求
 * @param {Object} requestConfig 请求配置
 * @returns {Promise} Promise对象
 */
function sendRequest(requestConfig) {
  // 获取原始选项
  const options = requestConfig.originalOptions || {};

  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...requestConfig,
      success: async (res) => {
        // 隐藏加载中
        if (options.loading !== false) {
          store.commit('SET_LOADING', false);
        }

        console.log('收到响应:', {
          statusCode: res.statusCode,
          data: res.data
        });

        // 响应拦截
        try {
          const result = await responseInterceptor({
            data: res.data,
            statusCode: res.statusCode,
            header: res.header,
            config: requestConfig
          });
          resolve(result);
        } catch (error) {
          console.error('响应拦截器错误:', error);
          // 打印更详细的错误信息
          if (error.errors) {
            console.error('验证错误详情:', error.errors);
          }
          if (error.data) {
            console.error('错误数据详情:', error.data);
          }
          reject(error);
        }
      },
      fail: (err) => {
        // 隐藏加载中
        if (options.loading !== false) {
          store.commit('SET_LOADING', false);
        }

        console.error('请求失败:', err);

        // 判断错误类型
        let errorMessage = '网络请求失败';
        let detailedMessage = err.errMsg || '';

        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
            detailedMessage = '请确保您的网络连接稳定，并检查服务器是否可访问。';
          } else if (err.errMsg.includes('abort')) {
            errorMessage = '请求被中止';
          } else if (err.errMsg.includes('fail')) {
            // 判断是否是网络错误
            if (err.errMsg.includes('net::') || err.errMsg.includes('network')) {
              errorMessage = '网络连接失败，请检查您的网络设置';
              detailedMessage = '请确保您已连接到互联网，并检查服务器地址是否正确。';
            }
          }
        }

        // 如果是地图相关请求，给出更具体的提示
        if (options.url && options.url.includes('/api/map/')) {
          errorMessage = '地图服务请求失败，请稍后再试';
        }

        // 如果配置了不显示错误提示，则不显示
        if (options.showError !== false) {
          // 显示错误提示
          if (detailedMessage && detailedMessage !== errorMessage) {
            uni.showModal({
              title: '请求失败',
              content: errorMessage + '\n\n' + detailedMessage,
              confirmText: '确定',
              showCancel: false
            });
          } else {
            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            });
          }
        }

        // 将错误信息添加到错误对象中
        err.errorMessage = errorMessage;
        reject(err);

        // 如果是超时错误，尝试自动重试
        if (err.errMsg && err.errMsg.includes('timeout') && options.retry !== false) {
          console.log('请求超时，尝试自动重试...');

          // 延迟2秒后重试
          setTimeout(() => {
            // 设置不再重试，防止无限循环
            const retryOptions = { ...options, retry: false };
            request.request(retryOptions)
              .then(() => console.log('重试成功'))
              .catch(error => console.error('重试失败:', error));
          }, 2000);
        }
      }
    });
  });
}

/**
 * HTTP请求工具
 */
const request = {
  /**
   * GET请求
   * @param {String} url 请求地址
   * @param {Object} params 请求参数
   * @param {Object} options 请求配置
   * @returns {Promise} Promise对象
   */
  get(url, params = {}, options = {}) {
    // 添加debugger，用于调试500错误
    // if (url === '/api/store') {
    //   debugger;
    //   console.log('即将发送请求到 /api/store，参数:', params);
    // }

    // 如果已经包含查询参数，则不再添加
    if (url.includes('?')) {
      return this.request({
        url,
        method: 'GET',
        ...options
      });
    }

    // 简化参数处理，不做类型转换
    const queryParams = {};

    // 只有当params是对象且不是数组时才添加keyword参数
    if (typeof params === 'object' && !Array.isArray(params)) {
      // 不再默认添加 keyword=empty 参数
      // queryParams.keyword = 'empty';
    }

    console.log('request.js - get 方法 - 初始查询参数:', queryParams);
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        // 检查是否是对象，如果是对象则转为JSON字符串
        if (typeof params[key] === 'object' && params[key] !== null) {
          queryParams[key] = JSON.stringify(params[key]);
        } else {
          queryParams[key] = params[key];
        }
      }
    }

    console.log('request.js - get 方法 - 最终发送的GET请求参数:', queryParams);

    // 手动构建查询字符串
    let queryString = '';
    const queryParts = [];
    for (const key in queryParams) {
      queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`);
    }

    if (queryParts.length > 0) {
      queryString = '?' + queryParts.join('&');
    }

    // 直接在URL中添加查询参数，而不是在data中
    return this.request({
      url: url + queryString,
      method: 'GET',
      ...options
    });
  },

  /**
   * POST请求
   * @param {String} url 请求地址
   * @param {Object} data 请求数据
   * @param {Object} options 请求配置
   * @returns {Promise} Promise对象
   */
  post(url, data = {}, options = {}) {
    // 确保请求头中包含正确的 Content-Type
    const headers = options.headers || {};
    if (!headers['Content-Type']) {
      headers['Content-Type'] = 'application/json';
    }

    // 打印请求数据，特别是布尔值
    console.log('POST 请求数据:', JSON.stringify(data, null, 2));
    console.log('POST 请求URL:', url);

    // 特殊处理门店创建请求 - 现在已经在API层处理，这里不再需要
    if (url.includes('/api/store')) {
      console.log('检测到门店创建/更新请求，数据已在API层格式化');
    }

    // 确保布尔值正确传递，并处理日期字段
    const processedData = {};
    for (const key in data) {
      if (typeof data[key] === 'boolean') {
        processedData[key] = data[key];
        console.log(`处理布尔值 ${key}: ${data[key]}`);
      } else if (data[key] instanceof Date) {
        // 将日期转换为 ISO 格式字符串
        processedData[key] = data[key].toISOString();
        console.log(`处理日期值 ${key}: ${processedData[key]}`);
      } else if (key === 'manufactureDate' && typeof data[key] === 'string' && !data[key].includes('T')) {
        // 如果是日期字符串但不是 ISO 格式，转换为 ISO 格式
        processedData[key] = new Date(data[key]).toISOString();
        console.log(`处理日期字符串 ${key}: ${data[key]} -> ${processedData[key]}`);
      } else {
        processedData[key] = data[key];
      }
    }

    return this.request({
      url,
      method: 'POST',
      data: processedData,
      header: headers,  // 注意这里使用 header 而不是 headers
      ...options
    });
  },

  /**
   * PUT请求
   * @param {String} url 请求地址
   * @param {Object} data 请求数据
   * @param {Object} options 请求配置
   * @returns {Promise} Promise对象
   */
  put(url, data = {}, options = {}) {
    // 确保请求头中包含正确的 Content-Type
    const headers = options.headers || {};
    if (!headers['Content-Type']) {
      headers['Content-Type'] = 'application/json';
    }

    // 打印请求数据，特别是布尔值
    console.log('PUT 请求数据:', JSON.stringify(data, null, 2));
    console.log('PUT 请求URL:', url);

    // 特殊处理门店更新请求 - 现在已经在API层处理，这里不再需要
    if (url.includes('/api/store')) {
      console.log('检测到门店更新请求，数据已在API层格式化');
    }

    // 确保布尔值正确传递，并处理日期字段
    const processedData = {};
    for (const key in data) {
      if (typeof data[key] === 'boolean') {
        processedData[key] = data[key];
        console.log(`处理布尔值 ${key}: ${data[key]}`);
      } else if (data[key] instanceof Date) {
        // 将日期转换为 ISO 格式字符串
        processedData[key] = data[key].toISOString();
        console.log(`处理日期值 ${key}: ${processedData[key]}`);
      } else if (key === 'manufactureDate' && typeof data[key] === 'string' && !data[key].includes('T')) {
        // 如果是日期字符串但不是 ISO 格式，转换为 ISO 格式
        processedData[key] = new Date(data[key]).toISOString();
        console.log(`处理日期字符串 ${key}: ${data[key]} -> ${processedData[key]}`);
      } else {
        processedData[key] = data[key];
      }
    }

    return this.request({
      url,
      method: 'PUT',
      data: processedData,
      header: headers,  // 注意这里使用 header 而不是 headers
      ...options
    });
  },

  /**
   * DELETE请求
   * @param {String} url 请求地址
   * @param {Object} data 请求数据
   * @param {Object} options 请求配置
   * @returns {Promise} Promise对象
   */
  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  },

  /**
   * 上传文件
   * @param {String} url 请求地址
   * @param {Object} options 上传配置
   * @returns {Promise} Promise对象
   */
  upload(url, options = {}) {
    // 显示加载中
    if (options.loading !== false) {
      store.commit('SET_LOADING', true);
    }

    // 处理请求URL
    const requestUrl = /^(https?:)/.test(url) ? url : (baseURL + url);

    // 检查是否是H5环境且filePath是Blob或File对象
    // #ifdef H5
    if (options.filePath instanceof Blob) {
      console.log('检测到H5环境下的Blob/File上传');

      return new Promise((resolve, reject) => {
        // 创建FormData对象
        const formData = options.formData || new FormData();

        // 确保文件已添加到FormData中
        if (!formData.has(options.name || 'file')) {
          formData.append(options.name || 'file', options.filePath, options.filePath.name || `file_${Date.now()}.jpg`);
        }

        // 创建XMLHttpRequest对象
        const xhr = new XMLHttpRequest();
        xhr.open('POST', requestUrl, true);

        // 设置请求头
        xhr.setRequestHeader('Authorization', uni.getStorageSync('token') ? `Bearer ${uni.getStorageSync('token')}` : '');

        // 设置其他请求头
        if (options.header) {
          Object.keys(options.header).forEach(key => {
            xhr.setRequestHeader(key, options.header[key]);
          });
        }

        // 监听上传进度
        xhr.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percent = Math.floor(e.loaded / e.total * 100);
            console.log(`上传进度: ${percent}%`);
          }
        };

        // 监听请求完成
        xhr.onload = function() {
          // 隐藏加载中
          if (options.loading !== false) {
            store.commit('SET_LOADING', false);
          }

          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              console.error('解析响应失败:', e);
              reject({
                statusCode: xhr.status,
                message: '解析响应失败',
                detailedMessage: e.message
              });
            }
          } else {
            try {
              const errorData = JSON.parse(xhr.responseText);
              console.error('上传失败:', errorData);
              uni.showToast({
                title: errorData.message || '文件上传失败',
                icon: 'none',
                duration: 3000
              });
              reject(errorData);
            } catch (e) {
              console.error('解析错误响应失败:', e);
              reject({
                statusCode: xhr.status,
                message: '文件上传失败',
                detailedMessage: xhr.statusText
              });
            }
          }
        };

        // 监听请求错误
        xhr.onerror = function() {
          // 隐藏加载中
          if (options.loading !== false) {
            store.commit('SET_LOADING', false);
          }

          console.error('上传请求失败');
          uni.showToast({
            title: '文件上传失败，请检查网络连接',
            icon: 'none',
            duration: 3000
          });

          reject({
            statusCode: 0,
            message: '网络请求失败',
            detailedMessage: '无法连接到服务器'
          });
        };

        // 发送请求
        xhr.send(formData);
      });
    }
    // #endif

    // 非H5环境或filePath不是Blob对象，使用uni.uploadFile
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: requestUrl,
        filePath: options.filePath,
        name: options.name || 'file',
        formData: options.formData || {},
        header: {
          ...options.header,
          'Authorization': uni.getStorageSync('token') ? `Bearer ${uni.getStorageSync('token')}` : ''
        },
        success: (res) => {
          // 隐藏加载中
          if (options.loading !== false) {
            store.commit('SET_LOADING', false);
          }

          try {
            const data = JSON.parse(res.data);
            if (res.statusCode === 200) {
              resolve(data);
            } else {
              uni.showToast({
                title: data.message || '文件上传失败',
                icon: 'none',
                duration: 3000
              });
              reject(data);
            }
          } catch (error) {
            reject(error);
          }
        },
        fail: (err) => {
          // 隐藏加载中
          if (options.loading !== false) {
            store.commit('SET_LOADING', false);
          }

          console.error('上传失败:', err);
          uni.showToast({
            title: '文件上传失败，请检查网络连接',
            icon: 'none',
            duration: 3000
          });
          reject(err);
        },
        complete: () => {
          store.commit('SET_LOADING', false);
        }
      });
    });
  },

  /**
   * 通用请求方法
   * @param {Object} options 请求配置
   * @returns {Promise} Promise对象
   */
  request(options) {
    // 显示加载中
    if (options.loading !== false) {
      store.commit('SET_LOADING', true);
    }

    // 处理请求URL
    const isAbsoluteUrl = /^(https?:)/.test(options.url);
    const url = isAbsoluteUrl ? options.url : (baseURL + options.url);

    // // 添加debugger，用于调试500错误
    if (url.includes('/api/store') && !url.includes('/api/store/')) {
      console.log('发送请求到 /api/store，完整URL:', url);
    }

    console.log('发送请求到:', url);

    // 处理请求参数
    const config = {
      url,
      method: options.method || 'GET',
      header: {
        'Content-Type': options.headers?.['Content-Type'] || 'application/json',
        ...options.header
      },
      dataType: options.dataType || 'json',
      responseType: options.responseType || 'text',
      timeout: options.timeout || 60000 // 增加默认超时时间到60秒
    };

    // 对于非GET请求，将数据放在data中
    // 注意：GET请求的参数已经在get方法中处理并添加到URL中
    if (config.method !== 'GET') {
      config.data = options.data || {};
    }

    // 特别处理布尔值和字符串，确保它们正确传递
    if ((config.method === 'POST' || config.method === 'PUT') && config.data) {
      const processedData = {};
      for (const key in config.data) {
        if (typeof config.data[key] === 'boolean') {
          processedData[key] = config.data[key];
          console.log(`请求中的布尔值 ${key}: ${config.data[key]}`);
        } else if (typeof config.data[key] === 'string') {
          // 确保字符串不会被意外转换
          processedData[key] = config.data[key];
          console.log(`请求中的字符串 ${key}: ${config.data[key].length > 50 ? config.data[key].substring(0, 50) + '...' : config.data[key]}`);
        } else {
          processedData[key] = config.data[key];
        }
      }
      config.data = processedData;
    }

    console.log('请求配置:', {
      method: config.method,
      data: config.data,
      header: config.header
    });

    // 请求拦截
    try {
      // 保存原始选项
      config.originalOptions = options;

      // 检查是否是注册相关请求，如果是，直接跳过拦截器的登录检查
      const isAuthRequest = config.url.includes('/api/auth/') ||
                           config.url.includes('/api/sms/') ||
                           config.url.includes('/api/ocr/');

      if (isAuthRequest) {
        console.log('检测到认证相关请求，跳过登录检查:', config.url);
        // 对于认证相关请求，只处理token添加，不做登录检查
        if (uni.getStorageSync('token')) {
          config.header.Authorization = `Bearer ${uni.getStorageSync('token')}`;
        }
        return sendRequest(config);
      }

      // 对于其他请求，正常进行拦截处理
      const requestConfig = requestInterceptor(config);
      return sendRequest(requestConfig);
    } catch (error) {
      console.error('请求拦截器错误:', error);
      console.error('错误详情:', error.message);
      console.error('请求URL:', config.url);

      // 隐藏加载中
      if (options.loading !== false) {
        store.commit('SET_LOADING', false);
      }

      // 如果是认证相关请求但拦截器报错，可能是白名单配置问题
      if (config.url.includes('/api/auth/') || config.url.includes('/api/sms/')) {
        console.warn('认证相关请求被拦截器拒绝，可能需要更新白名单配置');
      }

      return Promise.reject(error);
    }
  }
};

/**
 * 获取API基础URL
 * @returns {String} API基础URL
 */
export function getBaseUrl() {
  return baseURL;
}

export default request;
