using System.ComponentModel.DataAnnotations;

namespace BatteryApi.DTOs.Store;

public class StoreDto
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public string Phone { get; set; }
    public string Contact { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string Status { get; set; }
    public string Description { get; set; }
    public string BusinessHours { get; set; }
    public string ImageUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public double? Distance { get; set; } // 距离当前位置的距离，单位：米

    // 软删除相关字段
    public DateTime? DeletedAt { get; set; }
    public string DeleteReason { get; set; }
    public string DeletedBy { get; set; }
    public bool IsDeleted { get; set; }
}

public class StoreCreateDto
{
    [Required(ErrorMessage = "门店名称不能为空")]
    public string Name { get; set; }

    [Required(ErrorMessage = "门店地址不能为空")]
    public string Address { get; set; }

    [Required(ErrorMessage = "联系电话不能为空")]
    public string Phone { get; set; }

    public string Contact { get; set; } = "";

    public double Latitude { get; set; }
    public double Longitude { get; set; }

    public string Status { get; set; } = "Active";

    public string Description { get; set; } = "";

    public string BusinessHours { get; set; } = "";

    public string ImageUrl { get; set; } = "";
}

public class StoreUpdateDto
{
    [Required(ErrorMessage = "门店名称不能为空")]
    public string Name { get; set; }

    [Required(ErrorMessage = "门店地址不能为空")]
    public string Address { get; set; }

    [Required(ErrorMessage = "联系电话不能为空")]
    public string Phone { get; set; }

    public string Contact { get; set; } = "";

    public double Latitude { get; set; }
    public double Longitude { get; set; }

    public string Status { get; set; } = "Active";

    public string Description { get; set; } = "";

    public string BusinessHours { get; set; } = "";

    public string ImageUrl { get; set; } = "";
}

public class StoreQueryParameters
{
    public string? Name { get; set; }
    public string? Status { get; set; }
    public string? Contact { get; set; } // 联系人查询参数
    public string? Phone { get; set; } // 添加电话查询参数
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? MaxDistance { get; set; } // 最大距离，单位：米
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}

public class StoreInventoryDto
{
    public int Id { get; set; }
    public int StoreId { get; set; }
    public int BatteryId { get; set; }
    public string BatteryModel { get; set; }
    public string BatteryManufacturer { get; set; }
    public double BatteryCapacity { get; set; }
    public double BatteryVoltage { get; set; }
    public int Quantity { get; set; }
    public int AvailableQuantity { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class StoreInventoryUpdateDto
{
    public int BatteryId { get; set; }
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public int AvailableQuantity { get; set; }
}
