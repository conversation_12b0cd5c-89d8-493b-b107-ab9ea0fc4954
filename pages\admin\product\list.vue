<template>
  <view class="product-admin-list-container">
    <view class="header">
      <view class="title">商品管理</view>
      <view class="header-actions">
        <view class="export-btn" @tap="exportProductData">
          <text class="iconfont icon-export"></text>
          <text>导出</text>
        </view>
        <view class="add-btn" @tap="navToAddProduct">
          <text class="iconfont icon-add"></text>
          <text>新增商品</text>
        </view>
      </view>
    </view>

    <!-- 分类 Tabs 控件 -->
    <scroll-view scroll-x class="category-tabs" :scroll-into-view="'tab-' + activeCategoryId">
      <view class="tab-item-wrapper">
        <view
          class="tab-item"
          :class="{active: activeCategoryId === 0}"
          @tap="switchCategory(0)"
          id="tab-0"
        >
          全部
        </view>
      </view>
      <view
        class="tab-item-wrapper"
        v-for="category in categoryList"
        :key="'cat-' + category.id"
      >
        <view
          class="tab-item"
          :class="{active: activeCategoryId === category.id}"
          @tap="switchCategory(category.id)"
          :id="'tab-' + category.id"
        >
          {{ category.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 统计信息卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-value">{{ productStats.total }}</view>
        <view class="stat-label">总商品数</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.available }}</view>
        <view class="stat-label">可用商品</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.rented }}</view>
        <view class="stat-label">已租赁</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.sold }}</view>
        <view class="stat-label">已售出</view>
      </view>
    </view>

    <view class="search-bar">
      <view class="search-input">
        <text class="iconfont icon-search"></text>
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索商品名称/规格"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <text class="clear-icon" v-if="searchKeyword" @tap="searchKeyword = ''">×</text>
      </view>
      <view class="filter-btn" @tap="showFilterPopup = true">
        <text class="iconfont icon-filter"></text>
        <text>筛选</text>
      </view>
    </view>

    <scroll-view
      scroll-y
      class="product-list"
      @scrolltolower="loadMore"
      @scroll="onScroll"
      :style="{height: listHeight}"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      scroll-with-animation
    >
      <view class="empty-tip" v-if="productList.length === 0 && !loading">
        <image class="empty-icon" src="/static/empty.png" mode="aspectFit"></image>
        <text>暂无商品数据</text>
      </view>

      <view
        class="product-item"
        v-for="(product, index) in productList"
        :key="'product-' + product.id + '-' + index"
      >
        <view class="product-info">
          <view class="product-info-left">
            <view class="product-code">编号: {{ product.productCode || `PRD-${product.id}` }}</view>
            <view class="product-name">{{ product.name }}</view>
            <view class="product-category" v-if="product.categoryName">
              <text>类别: {{ product.categoryName }}</text>
            </view>
            <text v-if="product.spec" class="product-spec">规格: {{ product.spec }}</text>
          </view>
          <view class="product-status">
            <text class="status-tag" :class="getStatusClass(product.status)">{{ getStatusText(product.status) }}</text>
          </view>
        </view>

        <view class="product-price">
          <view class="price-item">
            <text class="label">售价：</text>
            <text class="value">{{ $utils.formatAmount(parseFloat(product.price) || 0) }}</text>
          </view>
          <view class="price-item">
            <text class="label">日租金：</text>
            <text class="value">{{ $utils.formatAmount(parseFloat(product.rentPrice) || 0) }}</text>
          </view>
        </view>

        <view class="product-meta">
          <view class="meta-item">
            <text class="label">生产日期：</text>
            <text class="value">{{ formatDate(product.manufactureDate) }}</text>
          </view>
          <view class="meta-item" v-if="product.voltage && product.capacity">
            <text class="label">参数：</text>
            <text class="value">{{ product.voltage }}V {{ product.capacity }}Ah</text>
          </view>
          <view class="meta-item">
            <text class="label">剩余寿命：</text>
            <text class="value">{{ product.lifespan }}个月</text>
          </view>
        </view>

        <view class="product-actions">
          <button
            class="action-btn edit"
            @tap.stop="navToEdit(product.id)"
          >编辑</button>
          <button
            class="action-btn bms"
            @tap.stop="viewBMSStatus(product)"
            v-if="product.batteryId"
          >BMS状态</button>
          <button
            class="action-btn delete"
            @tap.stop="handleDelete(product.id)"
          >删除</button>
        </view>
      </view>

      <view class="loading-more" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>

      <view class="no-more" v-if="noMore && productList.length > 0">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  data() {
    return {
      searchKeyword: '',
      showFilterPopup: false,
      page: 1,
      pageSize: 10,
      loading: false,
      refreshing: false,
      noMore: false,
      listHeight: '100vh',
      activeCategoryId: 0,
      categoryList: [],
      productList: []
    }
  },
  computed: {
    // 商品统计信息
    productStats() {
      return {
        total: this.productList.length,
        available: this.productList.filter(item => item.status === 'Available').length,
        rented: this.productList.filter(item => item.status === 'Rented').length,
        sold: this.productList.filter(item => item.status === 'Sold').length,
        maintenance: this.productList.filter(item => item.status === 'Maintenance').length
      }
    }
  },
  onLoad() {
    this.loadCategories()
    this.loadData(true)
  },
  onReady() {
    this.$nextTick(() => {
      this.calculateListHeight()
    })
  },
  onShow() {
    this.loadData(true)
  },
  methods: {
    // 加载分类列表
    async loadCategories() {
      try {
        const response = await this.$api.battery.getBatteryCategories()
        if (response && response.code === 0 && Array.isArray(response.data)) {
          this.categoryList = response.data
        }
      } catch (error) {
        console.error('加载分类列表错误:', error)
      }
    },

    // 切换分类
    switchCategory(categoryId) {
      if (this.activeCategoryId === categoryId) return
      this.activeCategoryId = categoryId
      this.page = 1
      this.loadData(true)
    },

    // 加载数据
    async loadData(refresh = false) {
      if (refresh) {
        this.page = 1
        this.noMore = false
      }

      if (this.loading) return
      if (this.noMore && !refresh) return

      this.loading = true

      try {
        // 这里暂时使用电池API，后续会替换为商品API
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          categoryId: this.activeCategoryId || undefined,
          keyword: this.searchKeyword || undefined
        }

        const response = await this.$api.battery.getBatteryList(params)
        
        if (response && response.code === 0) {
          const newData = response.data || []
          
          if (refresh) {
            this.productList = newData
          } else {
            this.productList = [...this.productList, ...newData]
          }

          if (newData.length < this.pageSize) {
            this.noMore = true
          } else {
            this.page++
          }
        }
      } catch (error) {
        console.error('加载商品数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 导航到新增商品
    navToAddProduct() {
      uni.navigateTo({
        url: '/pages/admin/product/edit'
      })
    },

    // 导航到编辑商品
    navToEdit(id) {
      uni.navigateTo({
        url: `/pages/admin/product/edit?id=${id}`
      })
    },

    // 查看BMS状态
    viewBMSStatus(product) {
      if (product.batteryId) {
        uni.navigateTo({
          url: `/pages/admin/battery/bms-detail?id=${product.batteryId}`
        })
      } else {
        uni.showToast({
          title: '该商品未关联电池',
          icon: 'none'
        })
      }
    },

    // 删除商品
    async handleDelete(id) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个商品吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 这里暂时使用电池API，后续会替换为商品API
              await this.$api.battery.deleteBattery(id)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.loadData(true)
            } catch (error) {
              console.error('删除商品失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 导出商品数据
    exportProductData() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      })
    },

    // 搜索
    handleSearch() {
      this.loadData(true)
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadData(true)
    },

    // 加载更多
    loadMore() {
      if (!this.loading && !this.noMore) {
        this.loadData(false)
      }
    },

    // 滚动事件
    onScroll(e) {
      // 处理滚动事件
    },

    // 计算列表高度
    calculateListHeight() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.header').boundingClientRect()
      query.select('.category-tabs').boundingClientRect()
      query.select('.stats-cards').boundingClientRect()
      query.select('.search-bar').boundingClientRect()
      query.exec((res) => {
        const headerHeight = res[0]?.height || 0
        const tabsHeight = res[1]?.height || 0
        const statsHeight = res[2]?.height || 0
        const searchHeight = res[3]?.height || 0
        const totalHeight = headerHeight + tabsHeight + statsHeight + searchHeight + 40
        this.listHeight = `calc(100vh - ${totalHeight}px)`
      })
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        'Available': 'available',
        'Rented': 'rented',
        'Sold': 'sold',
        'Maintenance': 'maintenance'
      }
      return statusMap[status] || 'available'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'Available': '可用',
        'Rented': '已租赁',
        'Sold': '已售出',
        'Maintenance': '维修中'
      }
      return statusMap[status] || status || '未知'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '未设置'
      try {
        return new Date(date).toLocaleDateString('zh-CN')
      } catch (error) {
        return '无效日期'
      }
    }
  }
}
</script>

<style scoped>
/* 这里可以复用 battery/list.vue 的样式 */
@import url('../battery/list.vue');
</style>
