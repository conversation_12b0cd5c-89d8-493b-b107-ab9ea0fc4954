<template>
  <view class="product-admin-list-container">
    <view class="header">
      <view class="title">商品管理</view>
      <view class="header-actions">

        <view class="export-btn" @tap="exportProductData">
          <text class="iconfont icon-export"></text>
          <text>导出</text>
        </view>
        <view class="add-btn" @tap="navToAddWithCategory">
          <text class="iconfont icon-add"></text>
          <text>新增商品</text>
        </view>
      </view>
    </view>

    <!-- 分类 Tabs 控件 -->
    <scroll-view scroll-x class="category-tabs" :scroll-into-view="'tab-' + activeCategoryId">
      <view class="tab-item-wrapper">
        <view
          class="tab-item"
          :class="{active: activeCategoryId === 0}"
          @tap="switchCategory(0)"
          id="tab-0"
        >
          全部
        </view>
      </view>
      <view
        class="tab-item-wrapper"
        v-for="category in categoryList"
        :key="'cat-' + category.id"
      >
        <view
          class="tab-item"
          :class="{active: activeCategoryId === category.id}"
          @tap="switchCategory(category.id)"
          :id="'tab-' + category.id"
        >
          {{ category.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 添加统计信息卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-value">{{ productStats.total }}</view>
        <view class="stat-label">总商品数</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.available }}</view>
        <view class="stat-label">可用商品</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.rented }}</view>
        <view class="stat-label">已租赁</view>
      </view>
      <view class="stat-card">
        <view class="stat-value">{{ productStats.sold }}</view>
        <view class="stat-label">已售出</view>
      </view>
    </view>

    <view class="search-bar">
      <view class="search-input">
        <text class="iconfont icon-search"></text>
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索商品名称/规格"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <text class="clear-icon" v-if="searchKeyword" @tap="searchKeyword = ''">×</text>
      </view>
      <view class="filter-btn" @tap="showFilterPopup = true">
        <text class="iconfont icon-filter"></text>
        <text>筛选</text>
      </view>
    </view>

    <view class="filter-tags" v-if="activeFilters.length > 0">
      <view class="tag" v-for="(filter, index) in activeFilters" :key="'filter-' + index + '-' + (filter.id || filter.type || index)">
        {{ filter.label }}
        <text class="close" @tap="removeFilter(index)">×</text>
      </view>
      <view class="clear-all" @tap="clearAllFilters">清除全部</view>
    </view>

    <!-- 批量操作区 -->
    <view class="batch-action-bar" v-if="isSelectionMode">
      <view class="select-all-box">
        <checkbox
          :checked="isAllSelected"
          @tap="toggleSelectAll"
          color="#2979ff"
        />
        <text>全选</text>
      </view>
      <view class="batch-actions">
        <button
          class="batch-btn"
          @tap="handleBatchUpdateStatus"
          :disabled="selectedProducts.length === 0"
        >批量更新状态</button>
        <button
          class="batch-btn delete"
          @tap="handleBatchDelete"
          :disabled="selectedProducts.length === 0"
        >批量删除</button>
      </view>
      <view class="cancel-select" @tap="toggleSelectionMode">取消</view>
    </view>

    <scroll-view
      scroll-y
      class="product-list"
      @scrolltolower="loadMore"
      @scroll="onScroll"
      :style="{height: listHeight}"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      scroll-with-animation
    >
      <view class="empty-tip" v-if="productList.length === 0 && !loading">
        <image class="empty-icon" src="/static/empty.png" mode="aspectFit"></image>
        <text>暂无商品数据</text>
      </view>

      <view
        class="product-item"
        v-for="(product, index) in productList"
        :key="'product-' + product.id + '-' + index"
      >
        <!-- 增加选择框 -->
        <view class="select-box" v-if="isSelectionMode">
          <checkbox
            :checked="selectedProducts.includes(product.id)"
            @tap="toggleSelectProduct(product.id)"
            color="#2979ff"
          />
        </view>

        <view class="product-info">
          <view class="product-info-left">
            <view class="product-code">编号: {{ product.code || product.productCode || `PRD-${product.id}` }}</view>
            <view class="product-name" v-if="product.name">{{ product.name }}</view>
            <view class="product-category" v-if="product.categoryName">
              <text>类别: {{ product.categoryName }}</text>
            </view>
            <text v-if="product.spec" class="product-spec">规格: {{ product.spec }}</text>
          </view>
          <view class="product-status">
            <text class="status-tag" :class="getStatusClass(product.status)">{{ getStatusText(product.status) }}</text>
          </view>
        </view>

        <view class="product-price">
          <view class="price-item">
            <text class="label">售价：</text>
            <text class="value">{{ $utils.formatAmount(parseFloat(product.price) || 0) }}</text>
          </view>
          <view class="price-item">
            <text class="label">日租金：</text>
            <text class="value">{{ $utils.formatAmount(parseFloat(product.rentPrice) || 0) }}</text>
          </view>
        </view>

        <view class="product-meta">
          <view class="meta-item">
            <text class="label">生产日期：</text>
            <text class="value">{{ formatDate(product.manufactureDate) }}</text>
          </view>
          <view class="meta-item" v-if="product.voltage && product.capacity">
            <text class="label">参数：</text>
            <text class="value">{{ product.voltage }}V {{ product.capacity }}Ah</text>
          </view>
          <view class="meta-item">
            <text class="label">使用寿命：</text>
            <text class="value">{{ product.lifespan }}个月</text>
          </view>
        </view>

        <view class="product-actions">
          <button
            class="action-btn edit"
            @tap.stop="navToEdit(product.id)"
          >编辑</button>
          <button
            class="action-btn bms"
            @tap.stop="viewBMSStatus(product)"
            v-if="product.categoryName && product.categoryName.includes('电池')"
          >BMS状态</button>
          <button
            class="action-btn delete"
            @tap.stop="handleDelete(product.id)"
          >删除</button>
          <button
            class="action-btn select"
            @tap.stop="toggleSelectionMode"
            v-if="!isSelectionMode"
          >选择</button>
        </view>
      </view>

      <view class="loading-more" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>

      <view class="no-more" v-if="noMore && productList.length > 0">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom" @change="onPopupChange">
      <view class="filter-popup" v-if="showFilterPopup">
        <view class="popup-header">
          <text class="title">筛选条件</text>
          <text class="close" @tap="showFilterPopup = false">×</text>
        </view>

        <view class="filter-content">
          <view class="filter-section">
            <view class="section-title">商品规格</view>
            <view class="filter-options">
              <view
                class="option-item"
                v-for="(spec, index) in specList"
                :key="'spec-' + spec.id + '-' + index"
                :class="{active: filters.specIds.includes(spec.id)}"
                @tap="toggleSpecFilter(spec.id)"
              >
                {{ spec.name }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <view class="section-title">商品状态</view>
            <view class="filter-options">
              <view
                class="option-item"
                v-for="(status, index) in statusOptions"
                :key="'status-' + status.id + '-' + index"
                :class="{active: filters.statusIds.includes(status.id)}"
                @tap="toggleStatusFilter(status.id)"
              >
                {{ status.name }}
              </view>
            </view>
          </view>

          <view class="filter-section">
            <view class="section-title">价格区间</view>
            <view class="price-range">
              <input
                type="number"
                v-model="filters.minPrice"
                placeholder="最低价"
              />
              <text class="separator">-</text>
              <input
                type="number"
                v-model="filters.maxPrice"
                placeholder="最高价"
              />
            </view>
          </view>

          <!-- 新增剩余寿命筛选 -->
          <view class="filter-section">
            <view class="section-title">剩余寿命</view>
            <view class="filter-options">
              <view
                class="option-item"
                :class="{active: filters.lifeLevel === 'high'}"
                @tap="toggleLifeFilter('high')"
              >
                良好(>75%)
              </view>
              <view
                class="option-item"
                :class="{active: filters.lifeLevel === 'medium'}"
                @tap="toggleLifeFilter('medium')"
              >
                中等(50%-75%)
              </view>
              <view
                class="option-item"
                :class="{active: filters.lifeLevel === 'low'}"
                @tap="toggleLifeFilter('low')"
              >
                较低(&lt;50%)
              </view>
            </view>
          </view>

          <!-- 新增生产日期筛选 -->
          <view class="filter-section">
            <view class="section-title">生产日期</view>
            <view class="date-range">
              <picker
                mode="date"
                :value="filters.startDate"
                @change="onStartDateChange"
              >
                <view class="date-picker">
                  <text v-if="filters.startDate">{{ filters.startDate }}</text>
                  <text v-else class="placeholder">开始日期</text>
                </view>
              </picker>
              <text class="separator">-</text>
              <picker
                mode="date"
                :value="filters.endDate"
                @change="onEndDateChange"
              >
                <view class="date-picker">
                  <text v-if="filters.endDate">{{ filters.endDate }}</text>
                  <text v-else class="placeholder">结束日期</text>
                </view>
              </picker>
            </view>
          </view>
        </view>

        <view class="filter-actions">
          <button class="reset-btn" @tap="resetFilters">重置</button>
          <button class="confirm-btn" @tap="applyFilters">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 添加 uToast 组件 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 添加 uLoading-icon 组件 -->
    <u-loading-icon ref="uLoading" :show="false"></u-loading-icon>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  data() {
    return {
      searchKeyword: '',
      showFilterPopup: false,
      filters: {
        specIds: [],
        statusIds: [],
        minPrice: '',
        maxPrice: '',
        lifeLevel: '',
        startDate: '',
        endDate: ''
      },
      activeFilters: [],
      page: 1,
      pageSize: 10,
      loading: false,
      refreshing: false,
      noMore: false,
      listHeight: '100vh',
      statusOptions: [
        { id: 1, name: '可用' },
        { id: 2, name: '已租赁' },
        { id: 3, name: '已售出' },
        { id: 4, name: '维修中' }
      ],
      isSelectionMode: false,
      selectedProducts: [],
      loadMoreTimer: null, // 加载更多的定时器
      lastScrollPosition: 0, // 记录最后滚动位置
      scrollDirection: 'down', // 滚动方向，默认向下
      activeCategoryId: 0, // 当前选中的分类 ID，0 表示全部
      categoryList: [], // 分类列表
      categoryLoading: false // 分类加载状态
    }
  },
  computed: {
    ...mapState('product', ['productList', 'specList']),

    // 添加商品统计信息
    productStats() {
      return {
        total: this.productList.length,
        available: this.productList.filter(item => item.status === 1).length,
        rented: this.productList.filter(item => item.status === 2).length,
        sold: this.productList.filter(item => item.status === 3).length,
        maintenance: this.productList.filter(item => item.status === 4).length
      }
    },

    // 是否全选
    isAllSelected() {
      return this.productList.length > 0 && this.selectedProducts.length === this.productList.length
    }
  },
  onLoad() {
    // 加载分类列表
    this.loadCategories()
    // 加载商品数据
    this.loadData(true)
  },
  onReady() {
    // 在DOM渲染完成后再计算高度
    this.$nextTick(() => {
      this.calculateListHeight()
    })
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadData(true)
  },

  // 页面销毁时清理定时器
  onUnload() {
    console.log('页面销毁，清理定时器');
    if (this.loadMoreTimer) {
      clearTimeout(this.loadMoreTimer);
      this.loadMoreTimer = null;
    }
  },
  methods: {
    ...mapActions('product', ['getProductList', 'deleteProduct']),

    // 加载分类列表
    async loadCategories() {
      if (this.categoryLoading) return;

      this.categoryLoading = true;
      try {
        // 使用 ProductAPI 获取分类列表
        const response = await this.$api.product.getProductCategories();
        console.log('获取分类列表响应:', response);

        if (response && response.code === 0 && Array.isArray(response.data)) {
          this.categoryList = response.data;
          console.log('分类列表:', this.categoryList);
        } else {
          console.error('获取分类列表失败:', response);
          this.categoryList = [];
        }
      } catch (error) {
        console.error('加载分类列表错误:', error);
        this.categoryList = [];
        uni.showToast({
          title: '加载分类失败',
          icon: 'none'
        });
      } finally {
        this.categoryLoading = false;
      }
    },

    // 切换分类
    switchCategory(categoryId) {
      console.log('切换分类:', categoryId);
      if (this.activeCategoryId === categoryId) return;

      this.activeCategoryId = categoryId;
      // 重置页码并加载数据
      this.page = 1;
      this.loadData(true);
    },

    // 加载数据
    async loadData(refresh = false) {
      console.log('调用 loadData，刷新:', refresh, '当前页码:', this.page);

      if (refresh) {
        console.log('刷新数据，重置页码为1');
        this.page = 1;
        this.noMore = false;
      }

      // 防止重复加载
      if (this.loading) {
        console.log('正在加载中，不重复加载');
        return;
      }

      // 如果没有更多数据且不是刷新，则不加载
      if (this.noMore && !refresh) {
        console.log('没有更多数据且不是刷新，不加载');
        return;
      }

      this.loading = true;
      console.log('开始加载数据，页码:', this.page);

      try {
        // 构建请求参数
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          keyword: this.searchKeyword
        };

        // 添加分类筛选参数
        if (this.activeCategoryId > 0) {
          const category = this.categoryList.find(c => c.id === this.activeCategoryId);
          if (category) {
            params.categoryId = this.activeCategoryId;
            // 如果有 categoryCode，也添加到参数中
            if (category.code) {
              params.categoryCode = category.code;
            }
            console.log(`添加分类筛选: ID=${this.activeCategoryId}, Code=${category.code || '无'}`);
          }
        }

        // 添加筛选参数
        if (this.filters.specIds && this.filters.specIds.length > 0) {
          params.specIds = [...this.filters.specIds]; // 复制数组，避免引用问题
        }

        if (this.filters.statusIds && this.filters.statusIds.length > 0) {
          params.statusIds = [...this.filters.statusIds]; // 复制数组，避免引用问题
        }

        if (this.filters.minPrice) {
          params.minPrice = this.filters.minPrice;
        }

        if (this.filters.maxPrice) {
          params.maxPrice = this.filters.maxPrice;
        }

        if (this.filters.lifeLevel) {
          params.lifeLevel = this.filters.lifeLevel;
        }

        if (this.filters.startDate) {
          params.startDate = this.filters.startDate;
        }

        if (this.filters.endDate) {
          params.endDate = this.filters.endDate;
        }

        console.log('发送请求参数:', params);

        // 显示加载提示
        if (!this.refreshing) {
          uni.showLoading({
            title: '加载中...'
          });
        }

        // 调用获取商品列表接口
        const result = await this.getProductList(params);
        console.log('获取商品列表响应:', result);

        // 检查列表数据
        if (this.productList && this.productList.length > 0) {
          console.log('当前列表数据第一项:', this.productList[0]);
          console.log('当前列表数据类型:', this.productList.map(item => typeof item));
          console.log('当前列表数据字段:', this.productList.map(item => Object.keys(item)));
        } else {
          console.log('当前列表为空');
        }

        // 获取总数量，如果没有返回总数量，则使用当前列表长度
        const total = result.total || this.productList.length;
        console.log('总数量:', total, '当前列表长度:', this.productList.length);

        // 判断是否已加载完所有数据
        // 只有当当前列表长度大于等于总数量时，才设置 noMore 为 true
        if (this.productList.length >= total) {
          console.log('已加载完所有数据');
          this.noMore = true;
        } else {
          console.log('还有更多数据可加载');
          this.noMore = false;
          // 只有当成功加载数据且还有更多数据时，才增加页码
          this.page++;
          console.log('页码增加为:', this.page);
        }

        this.loading = false;

        if (this.refreshing) {
          this.refreshing = false;
        }

        // 隐藏加载提示
        uni.hideLoading();

        return result;
      } catch (error) {
        console.error('加载商品列表失败', error);
        this.loading = false;

        if (this.refreshing) {
          this.refreshing = false;
        }

        // 隐藏加载提示
        uni.hideLoading();

        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });

        return null;
      }
    },

    // 下拉刷新
    onRefresh() {
      console.log('触发下拉刷新');
      this.refreshing = true;
      this.loadData(true);
    },

    // 滚动事件处理
    onScroll(e) {
      // 获取当前滚动位置
      const scrollTop = e.detail.scrollTop;

      // 判断滚动方向
      if (scrollTop > this.lastScrollPosition) {
        this.scrollDirection = 'down';
      } else if (scrollTop < this.lastScrollPosition) {
        this.scrollDirection = 'up';
      }

      // 更新最后滚动位置
      this.lastScrollPosition = scrollTop;
    },

    // 加载更多
    loadMore() {
      // 防止重复触发
      if (this.loading || this.noMore) {
        console.log('正在加载或没有更多数据，不触发加载更多');
        return;
      }

      // 如果是向上滚动，不触发加载更多
      if (this.scrollDirection === 'up') {
        console.log('向上滚动，不触发加载更多');
        return;
      }

      // 使用节流防抖，避免频繁触发
      if (this.loadMoreTimer) {
        clearTimeout(this.loadMoreTimer);
      }

      this.loadMoreTimer = setTimeout(() => {
        console.log('触发加载更多，当前页码:', this.page, '滚动方向:', this.scrollDirection);

        // 只有向下滚动时才加载更多
        if (this.scrollDirection === 'down') {
          this.loadData();
        }
      }, 300); // 300毫秒的防抖时间
    },

    // 搜索
    handleSearch() {
      this.loadData(true)
    },

    // 弹窗状态变化
    onPopupChange(e) {
      if (!e.show) {
        this.showFilterPopup = false
      }
    },

    // 切换规格筛选
    toggleSpecFilter(id) {
      console.log('切换规格筛选:', id);
      const index = this.filters.specIds.indexOf(id)
      if (index > -1) {
        this.filters.specIds.splice(index, 1)
        console.log('移除规格筛选:', id, '当前规格筛选:', this.filters.specIds);
      } else {
        this.filters.specIds.push(id)
        console.log('添加规格筛选:', id, '当前规格筛选:', this.filters.specIds);
      }
    },

    // 切换状态筛选
    toggleStatusFilter(id) {
      console.log('切换状态筛选:', id);
      const index = this.filters.statusIds.indexOf(id)
      if (index > -1) {
        this.filters.statusIds.splice(index, 1)
        console.log('移除状态筛选:', id, '当前状态筛选:', this.filters.statusIds);
      } else {
        this.filters.statusIds.push(id)
        console.log('添加状态筛选:', id, '当前状态筛选:', this.filters.statusIds);
      }
    },

    // 切换剩余寿命筛选
    toggleLifeFilter(level) {
      console.log('切换剩余寿命筛选:', level, '当前值:', this.filters.lifeLevel);
      this.filters.lifeLevel = this.filters.lifeLevel === level ? '' : level
      console.log('切换后的剩余寿命筛选:', this.filters.lifeLevel);
    },

    // 选择生产日期开始
    onStartDateChange(e) {
      console.log('选择生产日期开始:', e.detail.value);
      this.filters.startDate = e.detail.value

      // 确保开始日期不晚于结束日期
      if (this.filters.endDate && this.filters.startDate > this.filters.endDate) {
        this.filters.endDate = this.filters.startDate
        console.log('自动调整结束日期为:', this.filters.endDate);
      }
    },

    // 选择生产日期结束
    onEndDateChange(e) {
      console.log('选择生产日期结束:', e.detail.value);
      this.filters.endDate = e.detail.value

      // 确保结束日期不早于开始日期
      if (this.filters.startDate && this.filters.endDate < this.filters.startDate) {
        this.filters.startDate = this.filters.endDate
        console.log('自动调整开始日期为:', this.filters.startDate);
      }
    },

    // 应用筛选
    applyFilters() {
      console.log('应用筛选，当前筛选条件:', this.filters);
      this.activeFilters = []

      // 添加规格筛选标签
      this.filters.specIds.forEach(id => {
        const spec = this.specList.find(item => item.id === id)
        if (spec) {
          this.activeFilters.push({
            type: 'spec',
            id,
            label: `规格: ${spec.name}`
          })
        }
      })

      // 添加状态筛选标签
      this.filters.statusIds.forEach(id => {
        const status = this.statusOptions.find(item => item.id === id)
        if (status) {
          this.activeFilters.push({
            type: 'status',
            id,
            label: `状态: ${status.name}`
          })
        }
      })

      // 添加价格筛选标签
      if (this.filters.minPrice || this.filters.maxPrice) {
        let priceLabel = '价格: '
        if (this.filters.minPrice && this.filters.maxPrice) {
          priceLabel += `${this.filters.minPrice}-${this.filters.maxPrice}元`
        } else if (this.filters.minPrice) {
          priceLabel += `≥${this.filters.minPrice}元`
        } else if (this.filters.maxPrice) {
          priceLabel += `≤${this.filters.maxPrice}元`
        }

        this.activeFilters.push({
          type: 'price',
          label: priceLabel
        })
      }

      // 添加剩余寿命筛选标签
      if (this.filters.lifeLevel) {
        let lifeLabel = '剩余寿命: '
        switch (this.filters.lifeLevel) {
          case 'high':
            lifeLabel += '良好(>75%)'
            break
          case 'medium':
            lifeLabel += '中等(50%-75%)'
            break
          case 'low':
            lifeLabel += '较低(<50%)'
            break
        }

        this.activeFilters.push({
          type: 'life',
          value: this.filters.lifeLevel,
          label: lifeLabel
        })
      }

      // 添加生产日期筛选标签
      if (this.filters.startDate || this.filters.endDate) {
        let dateLabel = '生产日期: '
        if (this.filters.startDate && this.filters.endDate) {
          dateLabel += `${this.filters.startDate} 至 ${this.filters.endDate}`
        } else if (this.filters.startDate) {
          dateLabel += `${this.filters.startDate} 起`
        } else if (this.filters.endDate) {
          dateLabel += `${this.filters.endDate} 前`
        }

        this.activeFilters.push({
          type: 'date',
          label: dateLabel
        })
      }

      this.showFilterPopup = false

      // 打印活动的筛选条件
      console.log('活动的筛选条件:', this.activeFilters);

      // 重置页码并加载数据
      this.page = 1;
      this.loadData(true)
    },

    // 移除筛选标签
    removeFilter(index) {
      console.log('移除筛选标签:', index, '当前标签:', this.activeFilters[index]);
      const filter = this.activeFilters[index]

      if (filter.type === 'spec') {
        const specIndex = this.filters.specIds.indexOf(filter.id)
        if (specIndex > -1) {
          this.filters.specIds.splice(specIndex, 1)
          console.log('移除规格筛选:', filter.id, '剩余规格筛选:', this.filters.specIds);
        }
      } else if (filter.type === 'status') {
        const statusIndex = this.filters.statusIds.indexOf(filter.id)
        if (statusIndex > -1) {
          this.filters.statusIds.splice(statusIndex, 1)
          console.log('移除状态筛选:', filter.id, '剩余状态筛选:', this.filters.statusIds);
        }
      } else if (filter.type === 'price') {
        this.filters.minPrice = ''
        this.filters.maxPrice = ''
        console.log('移除价格筛选');
      } else if (filter.type === 'life') {
        this.filters.lifeLevel = ''
        console.log('移除寿命筛选');
      } else if (filter.type === 'date') {
        this.filters.startDate = ''
        this.filters.endDate = ''
        console.log('移除日期筛选');
      }

      this.activeFilters.splice(index, 1)
      console.log('移除后的活动筛选条件:', this.activeFilters);

      // 重置页码并加载数据
      this.page = 1;
      this.loadData(true)
    },

    // 清除所有筛选
    clearAllFilters() {
      console.log('清除所有筛选条件');
      this.filters = {
        specIds: [],
        statusIds: [],
        minPrice: '',
        maxPrice: '',
        lifeLevel: '',
        startDate: '',
        endDate: ''
      }
      this.activeFilters = []

      // 重置页码并加载数据
      this.page = 1;
      this.loadData(true)
    },

    // 重置筛选
    resetFilters() {
      console.log('重置筛选条件');
      this.filters = {
        specIds: [],
        statusIds: [],
        minPrice: '',
        maxPrice: '',
        lifeLevel: '',
        startDate: '',
        endDate: ''
      }
      console.log('重置后的筛选条件:', this.filters);
    },

    // 计算列表高度
    calculateListHeight() {
      try {
        // 获取系统窗口高度作为基准
        const windowHeight = uni.getSystemInfoSync().windowHeight;
        this.listHeight = `${windowHeight}px`;

        // 使用延时确保DOM已完全渲染
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this);

          // 一次性查询所有需要的元素
          query.select('.header').boundingClientRect();
          query.select('.search-bar').boundingClientRect();

          if (this.activeFilters && this.activeFilters.length > 0) {
            query.select('.filter-tags').boundingClientRect();
          }

          // 一次性执行并处理结果
          query.exec(results => {
            if (!results || !results.length) return;

            let totalHeight = 0;
            // 累加所有元素的高度
            results.forEach(item => {
              if (item && item.height) {
                totalHeight += item.height;
              }
            });

            // 设置列表高度为窗口高度减去其他元素高度
            if (totalHeight > 0) {
              this.listHeight = `${windowHeight - totalHeight}px`;
            }
          });
        }, 100);
      } catch (error) {
        console.error('计算列表高度失败', error);
        // 出错时设置默认高度
        this.listHeight = `${uni.getSystemInfoSync().windowHeight}px`;
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 1: return 'status-available'
        case 2: return 'status-rented'
        case 3: return 'status-sold'
        case 4: return 'status-maintenance'
        default: return ''
      }
    },

    // 获取状态文本
    getStatusText(status) {
      // 如果 status 是字符串，尝试直接返回对应的中文
      if (typeof status === 'string') {
        const statusMap = {
          'Available': '可用',
          'Rented': '已租赁',
          'Sold': '已售出',
          'Maintenance': '维修中',
          'Defective': '维修中'
        };
        if (statusMap[status]) {
          return statusMap[status];
        }
      }

      // 如果 status 是数字，使用 statusOptions 查找
      const statusItem = this.statusOptions.find(item => item.id === status)
      return statusItem ? statusItem.name : '未知'
    },

    // 获取剩余寿命百分比
    getLifePercentage(remainingLife, lifespan) {
      return Math.round((remainingLife / (lifespan || 36)) * 100);
    },



    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr || dateStr === '未知') {
        return '未知';
      }

      try {
        // 处理特殊情况：如果是带有时区信息的日期字符串
        console.log('原始日期字符串:', dateStr);

        // 如果是带有时区信息的日期字符串，先提取日期部分
        if (typeof dateStr === 'string' && dateStr.includes('T')) {
          dateStr = dateStr.split('T')[0];
          console.log('提取日期部分后:', dateStr);
        }

        // 尝试将日期字符串转换为 Date 对象
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          console.log('日期无效:', dateStr);
          return dateStr; // 如果日期无效，返回原始字符串
        }

        // 格式化为 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        const formattedDate = `${year}-${month}-${day}`;
        console.log('格式化后的日期:', formattedDate);
        return formattedDate;
      } catch (e) {
        console.error('格式化日期出错:', e);
        return dateStr; // 出错时返回原始字符串
      }
    },

    // 导航到编辑页面
    navToEdit(id) {
      uni.navigateTo({
        url: `/pages/admin/product/edit?id=${id}`
      })
    },

    // 导航到新增商品页面，并传递当前选中的类别
    navToAddWithCategory() {
      // 如果有选中的类别（不是"全部"类别）
      if (this.activeCategoryId > 0) {
        // 查找当前选中的类别信息
        const selectedCategory = this.categoryList.find(category => category.id === this.activeCategoryId);
        if (selectedCategory) {
          console.log('新增商品时传递类别信息:', selectedCategory);
          // 跳转到新增页面，并传递类别信息
          uni.navigateTo({
            url: `/pages/admin/product/edit?categoryId=${selectedCategory.id}&categoryCode=${selectedCategory.code || ''}&categoryName=${encodeURIComponent(selectedCategory.name || '')}`
          });
          return;
        }
      }

      // 如果没有选中类别或找不到类别信息，正常跳转
      console.log('新增商品时没有传递类别信息');
      this.navToEdit(0);
    },

    // 删除商品
    handleDelete(id) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除该商品吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中',
              mask: true
            })

            try {
              await this.deleteProduct(id)

              uni.hideLoading()
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 刷新列表
              this.loadData(true)
            } catch (error) {
              uni.hideLoading()
              uni.showToast({
                title: error.message || '删除失败，请重试',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 切换选择模式
    toggleSelectionMode() {
      this.isSelectionMode = !this.isSelectionMode
      if (!this.isSelectionMode) {
        this.selectedProducts = []
      }
      // 重新计算高度
      this.$nextTick(() => {
        this.calculateListHeight()
      })
    },

    // 切换选择商品
    toggleSelectProduct(id) {
      const index = this.selectedProducts.indexOf(id)
      if (index > -1) {
        this.selectedProducts.splice(index, 1)
      } else {
        this.selectedProducts.push(id)
      }
    },

    // 切换全选
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedProducts = []
      } else {
        this.selectedProducts = this.productList.map(item => item.id)
      }
    },

    // 批量更新状态
    handleBatchUpdateStatus() {
      if (this.selectedProducts.length === 0) return

      uni.showActionSheet({
        itemList: this.statusOptions.map(item => item.name),
        success: (res) => {
          const statusId = this.statusOptions[res.tapIndex].id

          uni.showModal({
            title: '确认更新',
            content: `确定将所选商品状态更新为"${this.statusOptions[res.tapIndex].name}"吗？`,
            success: async (modalRes) => {
              if (modalRes.confirm) {
                uni.showLoading({
                  title: '更新中',
                  mask: true
                })

                try {
                  // 更新选中商品的状态逻辑
                  // 在实际项目中应该调用API批量更新
                  console.log(`批量更新${this.selectedProducts.length}个商品到状态: ${statusId}`)

                  // 模拟更新成功
                  setTimeout(() => {
                    uni.hideLoading()
                    uni.showToast({
                      title: '更新成功',
                      icon: 'success'
                    })

                    // 清空选中状态
                    this.selectedProducts = []
                    // 退出选择模式
                    this.isSelectionMode = false
                    // 刷新列表
                    this.loadData(true)
                  }, 1000)
                } catch (error) {
                  uni.hideLoading()
                  uni.showToast({
                    title: error.message || '更新失败，请重试',
                    icon: 'none'
                  })
                }
              }
            }
          })
        }
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedProducts.length === 0) return

      uni.showModal({
        title: '确认删除',
        content: `确定要删除所选的${this.selectedProducts.length}个商品吗？`,
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中',
              mask: true
            })

            try {
              // 批量删除商品逻辑
              console.log(`批量删除商品: ${this.selectedProducts.join(',')}`)

              // 使用 Promise.all 并行处理所有删除请求
              const deletePromises = this.selectedProducts.map(id => this.deleteProduct(id));
              await Promise.all(deletePromises);

              uni.hideLoading()
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              // 清空选中状态
              this.selectedProducts = []
              // 退出选择模式
              this.isSelectionMode = false
              // 刷新列表
              this.loadData(true)
            } catch (error) {
              console.error('批量删除失败:', error);
              uni.hideLoading()
              uni.showToast({
                title: error.message || '删除失败，请重试',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    // 导出商品数据
    exportProductData() {
      uni.showActionSheet({
        itemList: ['导出所有商品', '导出筛选结果', '导出选中商品'],
        success: (res) => {
          // 实际项目中应根据不同选项调用相应的导出API
          let message = ''
          switch (res.tapIndex) {
            case 0:
              message = '导出所有商品数据'
              break
            case 1:
              message = '导出当前筛选结果'
              break
            case 2:
              if (this.selectedProducts.length === 0) {
                uni.showToast({
                  title: '请先选择要导出的商品',
                  icon: 'none'
                })
                return
              }
              message = `导出${this.selectedProducts.length}个选中商品`
              break
          }

          uni.showLoading({
            title: '导出中',
            mask: true
          })

          // 模拟导出成功
          setTimeout(() => {
            uni.hideLoading()
            uni.showToast({
              title: '导出成功',
              icon: 'success'
            })
            console.log(message)
          }, 1500)
        }
      })
    },

    // 查看BMS状态
    viewBMSStatus(battery) {
      if (!battery.serialNumber) {
        uni.showToast({
          title: '该电池没有序列号，无法查看BMS状态',
          icon: 'none'
        })
        return
      }

      console.log('查看BMS状态，电池信息:', battery)

      // 跳转到BMS详情页面
      uni.navigateTo({
        url: `/pages/admin/battery/bms-detail?macId=${battery.serialNumber}`
      })
    },

     
  }
}
</script>

<style lang="scss" scoped>
.product-admin-list-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #2979ff;
  color: #fff;

  .title {
    font-size: 36rpx;
    font-weight: bold;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .bms-btn, .export-btn, .add-btn {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8rpx 20rpx;
    border-radius: 30rpx;
    margin-left: 20rpx;

    .iconfont {
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    text {
      font-size: 28rpx;
    }
  }

  .bms-btn {
    background-color: rgba(76, 175, 80, 0.3);
  }
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    height: 70rpx;
    background-color: #f5f5f5;
    border-radius: 35rpx;
    padding: 0 30rpx;
    margin-right: 20rpx;

    .iconfont {
      font-size: 32rpx;
      color: #999;
      margin-right: 10rpx;
    }

    input {
      flex: 1;
      height: 70rpx;
      font-size: 28rpx;
    }

    .clear-icon {
      font-size: 40rpx;
      color: #ccc;
      line-height: 1;
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;

    .iconfont {
      font-size: 32rpx;
      color: #666;
      margin-right: 5rpx;
    }

    text {
      font-size: 28rpx;
      color: #666;
    }
  }
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;

  .tag {
    display: flex;
    align-items: center;
    background-color: #f0f8ff;
    color: #2979ff;
    font-size: 24rpx;
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    margin: 10rpx;

    .close {
      margin-left: 10rpx;
      font-size: 28rpx;
    }
  }

  .clear-all {
    font-size: 24rpx;
    color: #999;
    padding: 6rpx 20rpx;
    margin: 10rpx;
  }
}

.product-list {
  padding: 20rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.product-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;

  &:active {
    background-color: #f8f8f8;
  }
}

.select-box {
  position: absolute;
  left: 20rpx;
  top: 20rpx;
  z-index: 1;
}

.product-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;

  .product-info-left {
    display: flex;
    flex-direction: column;
  }
}

.product-code {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.product-category {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.product-spec {
  font-size: 28rpx;
  color: #666;

  .product-model {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
  }
}

.status-tag {
  display: inline-block;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;

  &.status-available {
    color: #07c160;
    background-color: rgba(7, 193, 96, 0.1);
  }

  &.status-rented {
    color: #2979ff;
    background-color: rgba(41, 121, 255, 0.1);
  }

  &.status-sold {
    color: #ff9500;
    background-color: rgba(255, 149, 0, 0.1);
  }

  &.status-maintenance {
    color: #ff3b30;
    background-color: rgba(255, 59, 48, 0.1);
  }
}

.product-price {
  display: flex;
  margin-bottom: 20rpx;
  flex-wrap: wrap;

  .price-item {
    margin-right: 40rpx;
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;

    .label {
      font-size: 26rpx;
      color: #999;
      min-width: 80rpx;
    }

    .value {
      font-size: 28rpx;
      color: #ff6b00;
      font-weight: bold;
    }
  }
}

.product-meta {
  display: flex;
  margin-bottom: 20rpx;
  flex-wrap: wrap;

  .meta-item {
    margin-right: 40rpx;
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;

    .label {
      font-size: 26rpx;
      color: #999;
      min-width: 100rpx;
    }

    .value {
      font-size: 26rpx;
      color: #666;
    }
  }
}

.life-percentage {
  font-size: 26rpx;
  color: #999;
  margin-left: 10rpx;

  &.life-high {
    color: #4CAF50;
  }

  &.life-medium {
    color: #2196F3;
  }

  &.life-low {
    color: #FF9800;
  }

  &.life-critical {
    color: #F44336;
  }
}

.product-actions {
  display: flex;
  justify-content: flex-end;

  .action-btn {
    font-size: 26rpx;
    padding: 6rpx 30rpx;
    border-radius: 30rpx;
    margin-left: 20rpx;

    &.edit {
      color: #2979ff;
      background-color: rgba(41, 121, 255, 0.1);
      border: 1rpx solid #2979ff;
    }

    &.bms {
      color: #4caf50;
      background-color: rgba(76, 175, 80, 0.1);
      border: 1rpx solid #4caf50;
    }

    &.delete {
      color: #ff3b30;
      background-color: rgba(255, 59, 48, 0.1);
      border: 1rpx solid #ff3b30;
    }

    &.select {
      color: #999;
      background-color: rgba(153, 153, 153, 0.1);
      border: 1rpx solid #999;
    }
  }
}

.loading-more,
.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

.filter-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .close {
    font-size: 40rpx;
    color: #999;
    line-height: 1;
  }
}

.filter-content {
  padding: 20rpx 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.filter-options {
  display: flex;
  flex-wrap: wrap;

  .option-item {
    padding: 10rpx 30rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    font-size: 26rpx;
    color: #666;
    margin: 0 20rpx 20rpx 0;

    &.active {
      background-color: #e6f0ff;
      color: #2979ff;
    }
  }
}

.price-range {
  display: flex;
  align-items: center;

  input {
    width: 200rpx;
    height: 70rpx;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 26rpx;
  }

  .separator {
    margin: 0 20rpx;
    color: #999;
  }
}

.filter-actions {
  display: flex;
  padding: 20rpx 30rpx 50rpx;
  border-top: 1rpx solid #f0f0f0;

  button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    border-radius: 40rpx;
  }

  .reset-btn {
    color: #666;
    background-color: #f5f5f5;
    margin-right: 20rpx;
  }

  .confirm-btn {
    color: #fff;
    background-color: #2979ff;
  }
}

/* 分类 Tabs 样式 */
.category-tabs {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  height: 80rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 1;

  .tab-item-wrapper {
    display: inline-block;
    padding: 0 10rpx;
  }

  .tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 76rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #666;
    position: relative;
    transition: all 0.3s;

    &.active {
      color: #2979ff;
      font-weight: bold;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #2979ff;
        border-radius: 2rpx;
      }
    }
  }
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 10rpx;
}

.stat-card {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1rpx;
    background-color: #eee;
  }

  .stat-value {
    font-size: 40rpx;
    font-weight: bold;
    color: #2979ff;
    margin-bottom: 10rpx;
  }

  .stat-label {
    font-size: 24rpx;
    color: #999;
  }
}

/* 日期选择器样式 */
.date-range {
  display: flex;
  align-items: center;
  margin-top: 10rpx;

  .date-picker {
    flex: 1;
    height: 70rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    font-size: 26rpx;
    color: #333;

    .placeholder {
      color: #999;
    }
  }

  .separator {
    margin: 0 10rpx;
    color: #999;
  }
}

/* 批量操作栏样式 */
.batch-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .select-all-box {
    display: flex;
    align-items: center;

    text {
      font-size: 28rpx;
      margin-left: 10rpx;
    }
  }

  .batch-actions {
    display: flex;
  }

  .batch-btn {
    font-size: 26rpx;
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    margin-left: 15rpx;
    background-color: rgba(41, 121, 255, 0.1);
    color: #2979ff;

    &.delete {
      background-color: rgba(255, 59, 48, 0.1);
      color: #ff3b30;
    }

    &:disabled {
      opacity: 0.5;
    }
  }

  .cancel-select {
    font-size: 28rpx;
    color: #666;
  }
}
</style>