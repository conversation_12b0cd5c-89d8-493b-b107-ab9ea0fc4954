<template>
  <view class="battery-edit-container">
    <view class="header">
      <view class="title">{{ isEdit ? '编辑商品' : '新增商品' }}</view>
    </view>

    <view class="form-section">


      <view class="form-item">
        <text class="label">商品类别</text>
        <picker
          @change="onCategoryChange"
          :value="categoryIndex"
          :range="categoryList"
          range-key="name"
        >
          <view class="picker-value">
            <text v-if="form.categoryName">{{ form.categoryName }}</text>
            <text v-else class="placeholder">请选择商品类别</text>
            <text class="iconfont icon-right"></text>
          </view>
        </picker>
      </view>
      <!-- 当是电池类别时，显示MAC ID输入框 -->
      <view class="form-item" v-if="isBatteryCategory && !isEdit">
        <text class="label">MAC ID</text>
        <view class="mac-input-group">
          <input
            type="text"
            v-model="macId"
            placeholder="请输入电池设备的MAC ID"
          />
          <!-- <button
            class="test-btn"
            @tap="testBMSConnection"
            :disabled="!macId"
          >测试连接</button>
          <button
            class="test-btn"
            @tap="testBMSParse"
            style="margin-left: 10rpx; background-color: #ff9800;"
          >测试解析</button> -->
          <button
            class="test-btn"
            @tap="showBMSConfig"
            :disabled="!macId"
            style="margin-left: 10rpx; background-color: #52c41a;line-height: 88rpx;height: 88rpx;"
          >查看BMS</button>
        </view>
      </view>


      <view class="form-item">
        <text class="label">商品规格</text>
        <picker
          @change="onSpecChange"
          :value="specIndex"
          :range="categorySpecList"
          range-key="name"
          :disabled="!form.categoryCode || categorySpecList.length === 0"
        >
          <view class="picker-value" :class="{'disabled': !form.categoryCode || categorySpecList.length === 0}">
            <text v-if="form.spec">{{ form.spec }}</text>
            <text v-else-if="!form.categoryCode" class="placeholder">请先选择商品类别</text>
            <text v-else-if="categorySpecList.length === 0" class="placeholder">此类别没有可用规格</text>
            <text v-else class="placeholder">请选择商品规格</text>
            <text class="iconfont icon-right"></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">商品状态</text>
        <picker
          @change="onStatusChange"
          :value="statusIndex"
          :range="statusList"
          range-key="name"
        >
          <view class="picker-value">
            <text v-if="form.status">{{ getStatusName(form.status) }}</text>
            <text v-else class="placeholder">请选择商品状态</text>
            <text class="iconfont icon-right"></text>
          </view>
        </picker>
      </view>



      <view class="form-item">
        <text class="label">售价(元)</text>
        <input
          type="digit"
          v-model="form.price"
          placeholder="请输入售价"
        />
      </view>

      <view class="form-item">
        <text class="label">日租金(元)</text>
        <input
          type="digit"
          v-model="form.rentPrice"
          placeholder="请输入日租金"
        />
      </view>

      <view class="form-item">
        <text class="label">生产日期</text>
        <picker
          mode="date"
          :value="form.manufactureDate"
          @change="onDateChange"
        >
          <view class="picker-value">
            <text v-if="form.manufactureDate">{{ form.manufactureDate }}</text>
            <text v-else class="placeholder">请选择生产日期</text>
            <text class="iconfont icon-right"></text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">商品寿命(月)</text>
        <input
          type="number"
          v-model="form.lifespan"
          placeholder="请输入商品寿命"
        />
      </view>



     



      <!-- 商品主图片上传（多图支持） -->
      <view class="form-item">
        <MultiImageUploadEdit
          title="商品主图片"
          :max-count="9"
          image-type="main"
          :product-id="isEdit ? batteryId : 0"
          v-model="form.mainImages"
          :show-main-button="true"
          @change="onMainImagesChange"
          @main-change="onMainImageChange"
          @remove="onImageRemove"
        />
        <!-- 新增模式提示 -->
        <view class="upload-tip" v-if="!isEdit && form.mainImages.length > 0">
          <text class="tip-text">💡 图片将在保存电池信息后自动上传</text>
        </view>
      </view>

      <view class="form-item">
        <text class="label">商品描述</text>
        <textarea
          v-model="form.description"
          placeholder="请输入商品描述"
            class="textarea"></textarea>
      </view>

      <!-- 服务信息 -->
      <view class="form-section-title">服务信息</view>
      <view class="service-list">
        <view class="service-item" v-for="(service, index) in form.services" :key="index">
          <view class="service-header">
            <text class="service-title">服务项 {{index + 1}}</text>
            <text class="service-delete" @tap="removeService(index)">删除</text>
          </view>
          <view class="form-item">
            <text class="label">服务名称</text>
            <input
              type="text"
              v-model="service.name"
              placeholder="请输入服务名称"
            />
          </view>
          <view class="form-item">
            <text class="label">服务描述</text>
            <input
              type="text"
              v-model="service.description"
              placeholder="请输入服务描述"
            />
          </view>
        </view>
        <view class="add-item" @tap="addService">
          <text class="add-icon">+</text>
          <text class="add-text">添加服务项</text>
        </view>
      </view>

      <!-- 安装费用 -->
      <view class="form-section-title">安装费用</view>
      <view class="fee-list">
        <view class="fee-item" v-for="(fee, index) in form.installationFees" :key="index">
          <view class="fee-header">
            <text class="fee-title">费用项 {{index + 1}}</text>
            <text class="fee-delete" @tap="removeFee(index)">删除</text>
          </view>
          <view class="form-item">
            <text class="label">费用名称</text>
            <input
              type="text"
              v-model="fee.name"
              placeholder="请输入费用名称"
            />
          </view>
          <view class="form-item">
            <text class="label">费用金额</text>
            <input
              type="digit"
              v-model="fee.price"
              placeholder="请输入费用金额"
            />
          </view>
          <view class="form-item">
            <text class="label">费用描述</text>
            <input
              type="text"
              v-model="fee.description"
              placeholder="请输入费用描述"
            />
          </view>
        </view>
        <view class="add-item" @tap="addFee">
          <text class="add-icon">+</text>
          <text class="add-text">添加费用项</text>
        </view>
      </view>
    </view>

    <view class="btn-section">
      <button class="cancel-btn" @tap="navBack">取消</button>
      <button class="submit-btn" @tap="handleSubmit">保存</button>
    </view>

    <!-- 添加 uToast 组件 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 添加 uLoading-icon 组件 -->
    <u-loading-icon ref="uLoading" :show="false"></u-loading-icon>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import MultiImageUploadEdit from '../../../components/MultiImageUploadEdit.vue'

export default {
  components: {
    MultiImageUploadEdit
  },
  data() {
    return {
      batteryId: 0,
      isEdit: false,
      batteryData: {}, // 存储原始电池数据
      specIndex: 0,
      statusIndex: 0,
      categoryIndex: 0,
      categoryList: [], // 电池类别列表
      categorySpecs: {}, // 各类别对应的规格列表
      categorySpecList: [], // 当前类别的规格列表
      statusList: [ // 状态列表
        { id: 1, name: '可用', value: 'Available' },
        { id: 2, name: '已租出', value: 'Rented' },
        { id: 3, name: '已售出', value: 'Sold' },
        { id: 4, name: '维修中', value: 'Maintenance' }
      ],
      macId: '', // MAC ID
      bmsLoading: false, // BMS加载状态
      bmsData: null, // 从BMS读取的详细数据
      // 图片上传相关
      uploadingImages: {
        main: false,
        detail: false,
        spec: false
      }, // 图片上传状态
      pendingImages: {
        main: null,
        detail: null,
        spec: null
      }, // 待上传的图片路径
      form: {
        spec: '',
        status: 1, // 默认为可用状态
        price: '',
        rentPrice: '',
        manufactureDate: '',
        lifespan: '',
        description: '',
        categoryId: null,
        categoryCode: '', // 默认为空，确保MAC ID默认隐藏
        categoryName: '', // 默认为空，确保MAC ID默认隐藏
        mainImages: [], // 商品主图片列表（新增多图支持）
        services: [], // 服务信息
        installationFees: [], // 安装费用
        removedImages: [] // 已删除的图片列表
      },
      loading: false
    }
  },
  computed: {
    ...mapState('battery', ['specList']),

    // 判断当前是否是电池类别
    isBatteryCategory() {
      // 默认隐藏MAC ID，必须明确选择了电池类别才显示

      // 优先使用类别代码判断
      if (this.form.categoryCode) {
        return this.form.categoryCode === '01';
      }

      // 如果没有类别代码，使用类别名称判断
      if (this.form.categoryName) {
        return this.form.categoryName === '电池' ||
               this.form.categoryName.includes('电池') ||
               this.form.categoryName.toLowerCase().includes('battery');
      }

      // 默认返回false，确保MAC ID默认隐藏
      // 用户必须明确选择电池类别才会显示MAC ID输入框
      return false;
    }
  },
  onLoad(options) {
    console.log('页面加载参数:', options);

    // 检查 ID 参数是否有效
    if (options.id && options.id !== 'undefined' && options.id !== 'null') {
      const id = parseInt(options.id);
      if (!isNaN(id) && id > 0) {
        this.batteryId = id;
        this.isEdit = true;
        console.log('编辑模式, ID:', this.batteryId);
        this.loadBatteryDetail();
      } else {
        console.warn('无效的 ID 参数:', options.id);
        this.handleInvalidId();
      }
    } else {
      console.log('新建模式');
      // 设置默认生产日期为当天
      this.form.manufactureDate = new Date().toISOString().split('T')[0];
      // 设置默认寿命为36个月
      this.form.lifespan = 36;

      // 处理传递的类别参数
      if (options.categoryId) {
        const categoryId = parseInt(options.categoryId);
        if (!isNaN(categoryId) && categoryId > 0) {
          console.log('接收到类别ID参数:', categoryId);
          this.form.categoryId = categoryId;

          // 如果有类别代码和名称，也设置到表单中
          if (options.categoryCode) {
            this.form.categoryCode = options.categoryCode;
            console.log('接收到类别代码参数:', options.categoryCode);
          }

          if (options.categoryName) {
            this.form.categoryName = decodeURIComponent(options.categoryName);
            console.log('接收到类别名称参数:', this.form.categoryName);
          }
        }
      }
    }

    this.loadSpecList();
  },
  methods: {
    ...mapActions('battery', ['getBatteryDetail', 'getSpecList', 'saveBattery', 'saveBatteryWithFiles', 'getBatteryCategories']),

    // 加载商品规格列表
    async loadSpecList() {
      try {
        await this.getSpecList();
        // 加载电池类别列表
        await this.loadCategoryList();
      } catch (error) {
        console.error('加载商品规格失败', error);
        uni.showToast({
          title: '加载商品规格失败',
          icon: 'none'
        });
      }
    },

    // 加载类别对应的规格列表
    async loadCategorySpecs(categoryCode) {
      console.log('开始加载类别规格列表, 类别代码:', categoryCode);

      if (!categoryCode) {
        console.warn('类别代码为空，无法加载规格列表');
        return;
      }

      // 强制刷新规格列表，确保数据最新
      // 如果已经加载过该类别的规格列表，直接使用缓存数据
      if (this.categorySpecs[categoryCode] && this.categorySpecs[categoryCode].length > 0) {
        console.log(`使用缓存的 ${categoryCode} 类别规格列表:`, this.categorySpecs[categoryCode]);

        // 将缓存的规格列表设置为当前类别的规格列表
        // 不使用计算属性，直接设置数据
        this.$set(this, 'categorySpecList', this.categorySpecs[categoryCode]);
        console.log('设置当前规格列表:', this.categorySpecList);

        // 如果已经设置了表单的规格信息，尝试更新规格索引
        if (this.form.spec && this.isEdit) {
          this.updateSpecIndex();
        }

        return;
      }

      uni.showLoading({
        title: '加载规格中...',
        mask: true
      });

      try {
        // 使用 API 获取该类别的规格列表
        const batteryAPI = (await import('@/api/battery')).default;
        const response = await batteryAPI.getSpecsByCategoryId(this.form.categoryId);
        console.log(`获取分类ID ${this.form.categoryId} 的规格列表响应:`, response);

        if (response && response.code === 0 && Array.isArray(response.data)) {
          // 将规格列表保存到 categorySpecs 中
          this.$set(this.categorySpecs, categoryCode, response.data);
          console.log(`已加载 ${categoryCode} 类别的规格列表:`, response.data);

          // 设置当前类别的规格列表
          // 不使用计算属性，直接设置数据
          this.$set(this, 'categorySpecList', response.data);
          console.log('设置当前规格列表:', this.categorySpecList);

          // 如果已经设置了表单的规格信息，尝试更新规格索引
          if (this.form.spec && this.isEdit) {
            this.updateSpecIndex();
          }

          // 强制触发视图更新
          this.$forceUpdate();
        } else {
          console.warn(`获取 ${categoryCode} 类别的规格列表失败，使用空数组`);
          this.$set(this.categorySpecs, categoryCode, []);
          this.$set(this, 'categorySpecList', []);
          this.$forceUpdate();
        }
      } catch (error) {
        console.error(`加载 ${categoryCode} 类别的规格列表错误:`, error);
        this.$set(this.categorySpecs, categoryCode, []);
        this.categorySpecList = [];

        uni.showToast({
          title: `加载规格失败: ${error.message || '未知错误'}`,
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 加载电池类别列表
    async loadCategoryList() {
      if (this.loading) {
        console.log('正在加载类别列表，跳过重复加载');
        return;
      }

      console.log('开始加载类别列表');
      this.loading = true;

      try {
        const categories = await this.getBatteryCategories();
        console.log('获取电池类别列表原始数据:', categories);

        if (!categories || categories.length === 0) {
          console.warn('获取到的类别列表为空');
          this.loading = false;
          return;
        }

        // 处理类别列表
        this.categoryList = categories.map(item => ({
          id: item.id,
          code: item.code,
          name: item.name
        }));

        console.log('处理后的类别列表:', this.categoryList);
        this.loading = false;

        // 如果已经设置了表单的类别信息，尝试更新类别索引
        if ((this.form.categoryCode || this.form.categoryId || this.form.categoryName)) {
          console.log('尝试更新类别索引，当前类别信息:', {
            id: this.form.categoryId,
            code: this.form.categoryCode,
            name: this.form.categoryName
          });

          // 尝试不同的匹配方式
          let categoryIndex = -1;

          // 如果有类别代码，优先使用代码查找
          if (this.form.categoryCode) {
            categoryIndex = this.categoryList.findIndex(item => item.code === this.form.categoryCode);
            console.log('通过类别代码查找索引:', categoryIndex);
          }

          // 如果没找到，尝试使用 categoryId 查找
          if (categoryIndex === -1 && this.form.categoryId) {
            categoryIndex = this.categoryList.findIndex(item => item.id === this.form.categoryId);
            console.log('通过类别ID查找索引:', categoryIndex);
          }

          // 如果还是没找到，尝试使用类别名称查找
          if (categoryIndex === -1 && this.form.categoryName) {
            categoryIndex = this.categoryList.findIndex(item => item.name === this.form.categoryName);
            console.log('通过类别名称查找索引:', categoryIndex);
          }

          if (categoryIndex !== -1) {
            this.categoryIndex = categoryIndex;
            const category = this.categoryList[categoryIndex];
            console.log('更新类别索引:', this.categoryIndex, '类别:', category);

            // 更新表单中的类别信息，确保一致性
            this.form.categoryId = category.id;
            this.form.categoryCode = category.code;
            this.form.categoryName = category.name;

            // 加载该类别的规格列表
            await this.loadCategorySpecs(category.code);
          } else {
            console.warn('找不到匹配的类别:', {
              id: this.form.categoryId,
              code: this.form.categoryCode,
              name: this.form.categoryName
            });
          }
        }
      } catch (error) {
        console.error('加载电池类别列表失败', error);
        this.loading = false;
        uni.showToast({
          title: '加载电池类别列表失败',
          icon: 'none'
        });
      }
    },

    // 加载商品详情
    async loadBatteryDetail() {
      console.log('开始加载电池详情, ID:', this.batteryId);

      if (!this.batteryId || this.batteryId <= 0) {
        console.error('无效的电池 ID:', this.batteryId);
        uni.showToast({
          title: '无效的商品 ID',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '加载中',
        mask: true
      });

      try {
        // 先加载类别列表，确保类别数据可用
        if (this.categoryList.length === 0) {
          console.log('类别列表为空，先加载类别列表');
          await this.loadCategoryList();
        }

        const battery = await this.getBatteryDetail(this.batteryId);
        console.log('获取到电池详情:', battery);

        // 检查返回的数据是否有效
        if (!battery || !battery.id) {
          console.error('获取到的电池详情无效:', battery);
          uni.hideLoading();
          uni.showToast({
            title: '获取商品详情失败',
            icon: 'none'
          });
          return;
        }

        // 保存原始数据用于显示
        this.batteryData = { ...battery };

        // 处理日期格式
        let manufactureDate = battery.manufactureDate;
        if (manufactureDate) {
          // 如果是ISO格式，提取日期部分
          if (manufactureDate.includes('T')) {
            manufactureDate = manufactureDate.split('T')[0];
          }
        } else {
          manufactureDate = new Date().toISOString().split('T')[0];
        }

        this.form = {
          spec: battery.spec || battery.model || '',
          status: this._mapStatusFromApi(battery.status) || 1,
          price: parseFloat(battery.price) || 0,
          rentPrice: parseFloat(battery.rentPrice) || 0,
          manufactureDate: manufactureDate,
          lifespan: parseInt(battery.lifespan) || 36,
          description: battery.description || battery.notes || '',
          categoryId: battery.categoryId || null,
          categoryCode: battery.categoryCode || '',
          categoryName: battery.categoryName || '',
        
          mainImages: [], // 将在后面加载
          services: [],   // 将在后面加载
          installationFees: [], // 将在后面加载
          removedImages: []
        };

        // 不处理图片数据，直接初始化为空数组，让用户重新上传
        console.log('跳过图片数据处理，用户需要重新上传图片');
        this.form.mainImages = [];

        // 处理服务数据
        if (battery.services && Array.isArray(battery.services)) {
          this.form.services = battery.services;
        } else {
          this.form.services = [];
        }

        // 处理安装费用数据
        if (battery.installationFees && Array.isArray(battery.installationFees)) {
          this.form.installationFees = battery.installationFees;
        } else {
          this.form.installationFees = [];
        }

        // 输出调试信息
        console.log('加载电池详情，寿命字段:', battery.lifespan, '表单寿命字段:', this.form.lifespan);
        console.log('表单数据:', this.form);
        console.log('类别列表:', this.categoryList);

        // 更新状态索引
        this.updateStatusIndex();

        // 设置类别索引
        if (this.form.categoryCode && this.categoryList.length > 0) {
          // 尝试不同的匹配方式
          let categoryIndex = this.categoryList.findIndex(item => item.code === this.form.categoryCode);

          // 如果没找到，尝试使用 categoryId 查找
          if (categoryIndex === -1 && this.form.categoryId) {
            categoryIndex = this.categoryList.findIndex(item => item.id === this.form.categoryId);
          }

          // 如果还是没找到，尝试使用类别名称查找
          if (categoryIndex === -1 && this.form.categoryName) {
            categoryIndex = this.categoryList.findIndex(item => item.name === this.form.categoryName);
          }

          if (categoryIndex !== -1) {
            this.categoryIndex = categoryIndex;
            const category = this.categoryList[categoryIndex];
            console.log('设置类别索引:', this.categoryIndex, '类别:', category);

            // 更新表单中的类别信息，确保一致性
            this.form.categoryId = category.id;
            this.form.categoryCode = category.code;
            this.form.categoryName = category.name;

            // 加载该类别的规格列表
            await this.loadCategorySpecs(category.code);
            console.log('加载类别规格列表完成，规格列表:', this.categorySpecList);

            // 如果规格列表加载成功，使用 updateSpecIndex 方法更新规格索引
            if (this.categorySpecList && this.categorySpecList.length > 0) {
              // 先等待一下，确保规格列表已经加载完成
              setTimeout(() => {
                this.updateSpecIndex();
                // 强制触发视图更新
                this.$forceUpdate();
              }, 100);
            } else {
              console.warn('类别规格列表为空');
            }
          } else {
            console.warn('找不到匹配的类别:', this.form.categoryCode, '可用类别:', this.categoryList);
          }
        } else {
          console.warn('电池没有类别代码或类别列表为空');
        }



        uni.hideLoading();
      } catch (error) {
        console.error('加载电池详情出错:', error);
        uni.hideLoading();
        uni.showToast({
          title: '加载商品详情失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        });

        // 如果加载失败，切换到新建模式
        this.handleInvalidId();
      }
    },

    // 选择商品类别
    async onCategoryChange(e) {
      const index = e.detail.value;
      this.categoryIndex = index;
      const category = this.categoryList[index];

      console.log('选择类别:', category);
      console.log('是否为电池类别:', this.isBatteryCategory);

      // 清空之前的规格选择，避免重复验证出错
      this.form.spec = '';

      this.form.categoryId = category.id;
      this.form.categoryCode = category.code;
      this.form.categoryName = category.name;

      console.log('更新后是否为电池类别:', this.isBatteryCategory);

      // 重置规格选择
      this.form.spec = '';
      this.specIndex = 0;

      // 清空售价和日租金
      this.form.price = '';
      this.form.rentPrice = '';

      // 如果切换到非电池类别，清空MAC ID
      if (!this.isBatteryCategory) {
        this.macId = '';
        console.log('切换到非电池类别，清空MAC ID');
      }

      // 清空当前规格列表
      this.$set(this, 'categorySpecList', []);

      // 加载该类别的规格列表
      await this.loadCategorySpecs(category.code);

      console.log('选择类别后的可选规格:', this.categorySpecList);

      // 如果规格列表不为空，选择第一个规格
      if (this.categorySpecList && this.categorySpecList.length > 0) {
        this.specIndex = 0;
        const spec = this.categorySpecList[0];
        this.form.spec = spec.name;

        // 如果规格中包含价格信息，自动填充售价
        if (spec.price) {
          this.form.price = spec.price;
        }

        // 如果规格中包含租金信息，自动填充日租金
        if (spec.rentPrice) {
          this.form.rentPrice = spec.rentPrice;
        }

        console.log('自动选择第一个规格:', this.form.spec);
      }

      // 强制触发视图更新
      this.$forceUpdate();
    },

    // 检查商品重复
    async checkProductDuplicate(categoryId, spec) {
      try {
        const batteryAPI = (await import('@/api/battery')).default;
        const response = await batteryAPI.checkDuplicate({
          categoryId: categoryId,
          spec: spec
        });
        
        return response?.data?.isDuplicate || false;
      } catch (error) {
        console.error('检查商品重复失败:', error);
        return false;
      }
    },

    // 选择商品规格
    async onSpecChange(e) {
      console.log('规格选择事件:', e);
      console.log('当前规格列表:', this.categorySpecList);

      if (!this.form.categoryCode || !this.categorySpecList || this.categorySpecList.length === 0) {
        uni.showToast({
          title: '请先选择商品类别',
          icon: 'none'
        });
        return;
      }

      const index = parseInt(e.detail.value);
      console.log('选择的规格索引:', index);

      if (isNaN(index) || index < 0 || index >= this.categorySpecList.length) {
        console.warn('无效的规格索引:', index);
        return;
      }

      this.specIndex = index;

      const selectedSpec = this.categorySpecList[index];
      if (selectedSpec) {
        // 检查是否存在重复商品
        const isDuplicate = await this.checkProductDuplicate(
          this.form.categoryId,
          selectedSpec.name
        );

        if (isDuplicate && !this.isEdit) {
          uni.showToast({
            title: '该类别下已存在此规格商品',
            icon: 'none',
            duration: 2000
          });
          // 清空规格选择
          this.form.spec = '';
          this.specIndex = 0;
          return;
        }

        this.form.spec = selectedSpec.name;
        console.log('选择规格:', this.form.spec);

        // 如果规格中包含价格信息，自动填充售价
        if (selectedSpec.price) {
          this.form.price = selectedSpec.price;
          console.log('自动填充售价:', this.form.price);
        }

        // 如果规格中包含租金信息，自动填充日租金
        if (selectedSpec.rentPrice) {
          this.form.rentPrice = selectedSpec.rentPrice;
          console.log('自动填充日租金:', this.form.rentPrice);
        }

        // 强制触发视图更新
        this.$forceUpdate();
      } else {
        console.warn('找不到选择的规格:', index);
      }
    },



    // 选择生产日期
    onDateChange(e) {
      this.form.manufactureDate = e.detail.value
    },


    // 读取BMS信息
    async readBMSInfo() {
      if (!this.macId || this.macId.trim() === '') {
        uni.showToast({
          title: '请先输入MAC ID',
          icon: 'none'
        });
        return;
      }

      if (this.bmsLoading) {
        return;
      }

      this.bmsLoading = true;
      uni.showLoading({
        title: '读取BMS信息中...',
        mask: true
      });

      try {
        // 导入BMS API
        const BMSAPI = (await import('@/api/bms')).default;

        // 调用生成电池编码接口
        const response = await BMSAPI.generateBatteryCode(this.macId.trim());
        console.log('BMS响应:', response);

        if (response && response.code === 200 && response.data) {
          const batteryInfo = response.data;

          // 存储完整的BMS数据
          this.bmsData = batteryInfo;
          console.log('存储完整BMS数据:', this.bmsData);

          // 填充基本表单数据
          this.form.voltage = this.extractNumericValue(batteryInfo.totalVoltage) || this.form.voltage;
          this.form.capacity = batteryInfo.capacity || this.form.capacity;
          this.form.cycleCount = batteryInfo.cycleCount || this.form.cycleCount;

          // 构建详细描述
          let description = [];

          if (batteryInfo.batteryType) {
            description.push(`电池类型：${batteryInfo.batteryType}`);
          }

         

          if (batteryInfo.firmware) {
            description.push(`固件版本：${batteryInfo.firmware}`);
          }

          if (batteryInfo.nominalCapacity) {
            description.push(`标称容量：${batteryInfo.nominalCapacity}`);
          }

          if (batteryInfo.actualCapacity) {
            description.push(`实际容量：${batteryInfo.actualCapacity}`);
          }

          // 添加状态信息
          if (batteryInfo.chargeStateDescription) {
            description.push(`充电状态：${batteryInfo.chargeStateDescription}`);
          }

          if (batteryInfo.connectStateDescription) {
            description.push(`连接状态：${batteryInfo.connectStateDescription}`);
          }

          // 添加温度信息
          if (batteryInfo.maxTemp !== undefined && batteryInfo.minTemp !== undefined) {
            description.push(`温度范围：${batteryInfo.minTemp}°C - ${batteryInfo.maxTemp}°C`);
          }

          // 添加电压信息
          if (batteryInfo.maxVoltage && batteryInfo.minVoltage) {
            description.push(`电压范围：${batteryInfo.minVoltage.toFixed(3)}V - ${batteryInfo.maxVoltage.toFixed(3)}V`);
          }

          // 添加故障和报警信息
          if (batteryInfo.faultState && batteryInfo.faultState.length > 0) {
            description.push(`故障状态：${batteryInfo.faultState.join('、')}`);
          }

          if (batteryInfo.alarmState && batteryInfo.alarmState.length > 0) {
            description.push(`报警状态：${batteryInfo.alarmState.join('、')}`);
          }

          this.form.description = description.join('；');

          uni.showToast({
            title: '成功读取BMS信息',
            icon: 'success'
          });

          console.log('已填充表单数据:', this.form);
          console.log('完整BMS数据:', batteryInfo);
        } else {
          throw new Error(response?.message || '读取BMS信息失败');
        }
      } catch (error) {
        console.error('读取BMS信息失败:', error);
        uni.showToast({
          title: error.message || '读取BMS信息失败，请检查MAC ID是否正确',
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.bmsLoading = false;
        uni.hideLoading();
      }
    },

    // 测试BMS连接
    async testBMSConnection() {
      if (!this.macId || this.macId.trim() === '') {
        uni.showToast({
          title: '请先输入MAC ID',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '测试连接中...',
        mask: true
      });

      try {
        // 导入BMS API
        const BMSAPI = (await import('@/api/bms')).default;

        // 调用获取电池状态接口测试连接
        const response = await BMSAPI.getBatteryStatus(this.macId.trim());
        console.log('BMS连接测试响应:', response);

        if (response && response.code === 200) {
          uni.showToast({
            title: 'BMS连接正常',
            icon: 'success'
          });
        } else {
          throw new Error(response?.message || 'BMS连接失败');
        }
      } catch (error) {
        console.error('BMS连接测试失败:', error);
        uni.showToast({
          title: error.message || 'BMS连接失败，请检查MAC ID是否正确',
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 测试BMS数据解析
    async testBMSParse() {
      uni.showLoading({
        title: '测试解析中...',
        mask: true
      });

      try {
        // 导入BMS API
        const BMSAPI = (await import('@/api/bms')).default;

        // 调用测试解析接口
        const response = await BMSAPI.testParse();
        console.log('BMS解析测试响应:', response);

        if (response && response.code === 200) {
          // 使用解析出的数据填充表单
          const batteryInfo = response.data;

          // 存储完整的BMS数据
          this.bmsData = batteryInfo;
          console.log('测试解析存储BMS数据:', this.bmsData);

          // 填充基本表单数据
          this.form.voltage = this.extractNumericValue(batteryInfo.totalVoltage) || this.form.voltage;
          this.form.capacity = batteryInfo.capacity || this.form.capacity;
          this.form.cycleCount = batteryInfo.cycleCount || this.form.cycleCount;

          // 构建详细描述
          let description = [];

          if (batteryInfo.batteryType) {
            description.push(`电池类型：${batteryInfo.batteryType}`);
          }

        

          if (batteryInfo.firmware) {
            description.push(`固件版本：${batteryInfo.firmware}`);
          }

          if (batteryInfo.nominalCapacity) {
            description.push(`标称容量：${batteryInfo.nominalCapacity}`);
          }

          if (batteryInfo.actualCapacity) {
            description.push(`实际容量：${batteryInfo.actualCapacity}`);
          }

          // 添加状态信息
          if (batteryInfo.chargeStateDescription) {
            description.push(`充电状态：${batteryInfo.chargeStateDescription}`);
          }

          if (batteryInfo.connectStateDescription) {
            description.push(`连接状态：${batteryInfo.connectStateDescription}`);
          }

          // 添加温度信息
          if (batteryInfo.maxTemp !== undefined && batteryInfo.minTemp !== undefined) {
            description.push(`温度范围：${batteryInfo.minTemp}°C - ${batteryInfo.maxTemp}°C`);
          }

          // 添加电压信息
          if (batteryInfo.maxVoltage && batteryInfo.minVoltage) {
            description.push(`电压范围：${batteryInfo.minVoltage.toFixed(3)}V - ${batteryInfo.maxVoltage.toFixed(3)}V`);
          }

          // 添加故障和报警信息
          if (batteryInfo.faultState && batteryInfo.faultState.length > 0) {
            description.push(`故障状态：${batteryInfo.faultState.join('、')}`);
          }

          if (batteryInfo.alarmState && batteryInfo.alarmState.length > 0) {
            description.push(`报警状态：${batteryInfo.alarmState.join('、')}`);
          }

          this.form.description = description.join('；');

          uni.showToast({
            title: 'BMS数据解析测试成功',
            icon: 'success'
          });

          console.log('已填充表单数据:', this.form);
          console.log('完整BMS数据:', batteryInfo);
        } else {
          throw new Error(response?.message || 'BMS数据解析测试失败');
        }
      } catch (error) {
        console.error('BMS数据解析测试失败:', error);
        uni.showToast({
          title: error.message || 'BMS数据解析测试失败',
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 跳转到BMS信息页面
    showBMSConfig() {
      if (!this.macId || this.macId.trim() === '') {
        uni.showToast({
          title: '请先输入MAC ID',
          icon: 'none'
        });
        return;
      }

      console.log('跳转到BMS信息页面，MAC ID:', this.macId);
      uni.navigateTo({
        url: `/pages/admin/product/bms-config?macId=${this.macId}`
      });
    },

    // 更新规格索引
    updateSpecIndex() {
      console.log('尝试更新规格索引，当前规格:', this.form.spec);
      console.log('当前规格列表:', this.categorySpecList);

      if (!this.categorySpecList || this.categorySpecList.length === 0) {
        console.warn('规格列表为空');
        return;
      }

      if (!this.form.spec) {
        console.warn('当前规格为空，尝试设置默认规格');
        // 如果当前规格为空，但有可用的规格列表，选择第一个规格
        if (this.categorySpecList.length > 0) {
          this.specIndex = 0;
          const spec = this.categorySpecList[0];
          this.form.spec = spec.name;
          console.log('设置默认规格:', this.form.spec);
          return;
        }
        return;
      }

      // 尝试精确匹配
      let specIndex = -1;

      // 先检查当前索引是否有效
      if (this.specIndex >= 0 && this.specIndex < this.categorySpecList.length) {
        const currentSpec = this.categorySpecList[this.specIndex];
        if (currentSpec && currentSpec.name === this.form.spec) {
          console.log('当前索引已经正确，无需更新');
          return;
        }
      }

      // 尝试精确匹配
      specIndex = this.categorySpecList.findIndex(item => item.name === this.form.spec);
      console.log('精确匹配结果:', specIndex);

      // 如果没找到，尝试模糊匹配
      if (specIndex === -1) {
        for (let i = 0; i < this.categorySpecList.length; i++) {
          const spec = this.categorySpecList[i];
          if (this.form.spec.includes(spec.name) || spec.name.includes(this.form.spec)) {
            specIndex = i;
            console.log('模糊匹配成功，索引:', i, '规格:', spec.name);
            break;
          }
        }
      }

      if (specIndex !== -1) {
        this.specIndex = specIndex;
        const spec = this.categorySpecList[specIndex];
        console.log('更新规格索引:', this.specIndex, '规格:', spec);

        // 更新表单中的规格信息，确保一致性
        this.form.spec = spec.name;

        // 强制触发视图更新
        this.$forceUpdate();
      } else {
        console.warn('找不到匹配的规格:', this.form.spec, '可用规格:', this.categorySpecList);

        // 如果找不到匹配的规格，但有可用的规格列表，选择第一个规格
        if (this.categorySpecList.length > 0) {
          this.specIndex = 0;
          const spec = this.categorySpecList[0];
          this.form.spec = spec.name;
          console.log('设置默认规格:', this.form.spec);
        }
      }
    },


    // 验证表单
    async validateForm() {
      // 检查类别
      if (!this.form.categoryCode) {
        uni.showToast({
          title: '请选择商品类别',
          icon: 'none'
        })
        return false
      }

      if (!this.form.spec) {
        uni.showToast({
          title: '请选择商品规格',
          icon: 'none'
        })
        return false
      }

      // 检查商品重复
      try {
        const isDuplicate = await this.checkProductDuplicate(
          this.form.categoryId,
          this.form.spec
        );
        
        if (isDuplicate && !this.isEdit) {
          uni.showToast({
            title: '该类别下已存在相同规格的商品',
            icon: 'none',
            duration: 2000
          });
          return false;
        }
      } catch (error) {
        console.error('重复检查失败:', error);
      }

      if (this.form.status === undefined) {
        uni.showToast({
          title: '请选择商品状态',
          icon: 'none'
        })
        return false
      }

      if (!this.form.price || parseFloat(this.form.price) <= 0) {
        uni.showToast({
          title: '请输入有效的售价',
          icon: 'none'
        })
        return false
      }

      if (!this.form.rentPrice || parseFloat(this.form.rentPrice) <= 0) {
        uni.showToast({
          title: '请输入有效的日租金',
          icon: 'none'
        })
        return false
      }

      if (!this.form.manufactureDate) {
        uni.showToast({
          title: '请选择生产日期',
          icon: 'none'
        })
        return false
      }

      // 如果 lifespan 不存在或者不是有效数字，设置为默认值 36
      if (this.form.lifespan === undefined || this.form.lifespan === null || this.form.lifespan === '') {
        this.form.lifespan = 36;
      } else {
        // 确保 lifespan 是数字
        this.form.lifespan = parseInt(this.form.lifespan) || 36;
      }

      // 如果 lifespan 是负数，设置为正数
      if (this.form.lifespan < 0) {
        this.form.lifespan = Math.abs(this.form.lifespan);
      }

      return true
    },

    // 提交表单（使用 FormData 方式）
    async handleSubmitWithFiles() {
      console.log('提交表单（FormData），当前数据:', this.form);

      if (!this.validateForm()) {
        console.log('表单验证失败');
        return;
      }

      uni.showLoading({
        title: '保存中',
        mask: true
      });

      try {
        // 创建 FormData
        const formData = new FormData();

        // 添加基本字段
        if (this.isEdit) {
          formData.append('id', this.batteryId.toString());
        }
        formData.append('spec', this.form.spec || '默认型号');
        formData.append('status', this._mapStatusToApi(this.form.status) || 'Available');
        formData.append('price', (parseFloat(this.form.price) || 0).toString());
        formData.append('rentPrice', this.form.rentPrice ? this.form.rentPrice.toString() : '0');
        formData.append('manufactureDate', this.form.manufactureDate || new Date().toISOString().split('T')[0]);
        formData.append('lifespan', (parseInt(this.form.lifespan) || 36).toString());
        formData.append('description', this.form.description || '');
        formData.append('categoryId', (parseInt(this.form.categoryId) || 0).toString());
        formData.append('voltage', '0'); // 默认值
        formData.append('capacity', '0'); // 默认值
        formData.append('imageType', 'main');

        // 直接添加图片文件到 mainImages 字段（不解析，直接传递 IFormFile）
        if (this.form.mainImages && this.form.mainImages.length > 0) {
          console.log(`📎 准备添加 ${this.form.mainImages.length} 个图片文件`);
          for (let i = 0; i < this.form.mainImages.length; i++) {
            const image = this.form.mainImages[i];
            if (image.file) {
              console.log(`📎 添加图片文件到 mainImages: ${image.file.name}, 大小: ${image.file.size} bytes`);
              formData.append('mainImages', image.file);
            } else if (image.tempPath) {
              // 如果没有 file 对象但有 tempPath，尝试从路径创建 File 对象
              console.log(`📎 尝试从路径创建文件对象: ${image.tempPath}`);
              try {
                const fileObj = await this.createFileFromPath(image.tempPath, image.name);
                if (fileObj) {
                  console.log(`📎 成功创建文件对象: ${fileObj.name}, 大小: ${fileObj.size} bytes`);
                  formData.append('mainImages', fileObj);
                }
              } catch (error) {
                console.error('创建文件对象失败:', error);
              }
            }
          }
        } else {
          console.log('📎 没有图片文件需要上传');
        }

        console.log('发送保存请求（FormData）');

        const result = await this.saveBatteryWithFiles(formData);
        console.log('保存商品响应（FormData）:', result);

        // 获取商品ID（新增时从返回结果中获取，编辑时使用现有ID）
        let productId = this.isEdit ? this.batteryId : result?.data?.id || result?.id;
        console.log('获取到商品ID:', productId);

        // 保存服务和安装费用
        if (productId) {
          console.log('开始保存服务和安装费用...');

          uni.showLoading({
            title: '保存服务信息中...',
            mask: true
          });

          try {
            // 过滤有效的服务数据
            const validServices = this.form.services ?
              this.form.services.filter(service => service.name && service.name.trim()) : [];

            // 过滤有效的安装费用数据
            const validInstallationFees = this.form.installationFees ?
              this.form.installationFees.filter(fee => fee.name && fee.name.trim() && fee.price >= 0) : [];

            console.log('有效服务数据:', validServices);
            console.log('有效安装费用数据:', validInstallationFees);

            // 调用保存服务和安装费用的API
            if (validServices.length > 0 || validInstallationFees.length > 0) {
              await this.saveBatteryServicesAndFees(productId, validServices, validInstallationFees);
              console.log('服务和安装费用保存完成');
            }
          } catch (serviceError) {
            console.error('保存服务和安装费用失败:', serviceError);
            uni.showToast({
              title: '服务信息保存失败: ' + (serviceError.message || '未知错误'),
              icon: 'none',
              duration: 3000
            });
            // 服务保存失败不影响主流程，继续执行
          }
        }

        uni.hideLoading();
        uni.showToast({
          title: this.isEdit ? '更新成功' : '添加成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (error) {
        console.error('保存电池失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 提交表单（原有方式）
    async handleSubmit() {
      console.log('提交表单，当前数据:', this.form);

      if (!this.validateForm()) {
        console.log('表单验证失败');
        return;
      }

      uni.showLoading({
        title: '保存中',
        mask: true
      });

      try {
        // 从规格中提取电压和容量
        let voltage = 0;
        let capacity = 0;

        if (this.form.spec) {
          const specMatch = this.form.spec.match(/(\d+)V(\d+)Ah/);
          if (specMatch) {
            voltage = parseFloat(specMatch[1]);
            capacity = parseFloat(specMatch[2]);
            console.log('从规格中提取电压和容量:', voltage, capacity);
          }
        }

        // 构建请求数据 - 完全匹配后端 CreateBatteryRequest 格式
        const batteryData = {
          // 如果是编辑模式，包含ID
          ...(this.isEdit && { id: this.batteryId }),

          // 必填字段
          spec: this.form.spec || '默认型号',
          status: this._mapStatusToApi(this.form.status) || 'Available', // 映射为API状态
          price: parseFloat(this.form.price) || 0,
          rentPrice: this.form.rentPrice ? this.form.rentPrice.toString() : '0',
          manufactureDate: this.form.manufactureDate || new Date().toISOString().split('T')[0],
          lifespan: parseInt(this.form.lifespan) || 36,
          description: this.form.description || '',
          categoryId: parseInt(this.form.categoryId) || null,
          categoryCode: this.form.categoryCode || '',
          categoryName: this.form.categoryName || '',

        

          // 图片数据处理 - 转换为 BatteryImageRequest 格式
          mainImages: this.form.mainImages ? this.form.mainImages.map(img => {
            console.log('🖼️ 处理图片数据:', img);
            return {
              id: img.id || 0,
              tempPath: img.tempPath || '',
              url: img.url || '',
              relativePath: img.relativePath || '',
              name: img.name || '',
              size: img.size || 0,
              status: img.status || 'pending',
              isMain: img.isMain || false,
              isNew: img.isNew !== false, // 默认为 true
              imageType: img.imageType || 'main'
            };
          }) : [],
          services: this.form.services || [],
          installationFees: this.form.installationFees || [],
          removedImages: []
        };

        // 移除了BMS数据处理，简化商品创建流程

        // 确保 manufactureDate 是有效的日期
        if (!batteryData.manufactureDate) {
          // 如果没有制造日期，使用当前日期
          batteryData.manufactureDate = new Date().toISOString();
          console.log('没有制造日期，使用当前日期:', batteryData.manufactureDate);
        } else if (typeof batteryData.manufactureDate === 'string') {
          if (!batteryData.manufactureDate.includes('T')) {
            // 将 YYYY-MM-DD 格式转换为 ISO 格式
            try {
              const date = new Date(batteryData.manufactureDate);
              if (isNaN(date.getTime())) {
                // 如果日期无效，使用当前日期
                batteryData.manufactureDate = new Date().toISOString();
                console.log('制造日期无效，使用当前日期:', batteryData.manufactureDate);
              } else {
                batteryData.manufactureDate = date.toISOString();
                console.log('将制造日期转换为 ISO 格式:', batteryData.manufactureDate);
              }
            } catch (e) {
              // 如果转换失败，使用当前日期
              batteryData.manufactureDate = new Date().toISOString();
              console.log('制造日期转换失败，使用当前日期:', batteryData.manufactureDate);
            }
          }
        } else if (batteryData.manufactureDate instanceof Date) {
          // 如果是 Date 对象，转换为 ISO 格式
          batteryData.manufactureDate = batteryData.manufactureDate.toISOString();
          console.log('将 Date 对象转换为 ISO 格式:', batteryData.manufactureDate);
        }

        // 验证必填字段
        if (!batteryData.categoryId || batteryData.categoryId <= 0) {
          uni.hideLoading();
          uni.showToast({
            title: '请选择电池分类',
            icon: 'none'
          });
          return;
        }

        if (!batteryData.spec || batteryData.spec.trim() === '') {
          uni.hideLoading();
          uni.showToast({
            title: '请输入电池规格',
            icon: 'none'
          });
          return;
        }

        console.log('发送保存请求，数据:', batteryData);

        const result = await this.saveBattery(batteryData);
        console.log('保存商品响应:', result);

        // 获取商品ID（新增时从返回结果中获取，编辑时使用现有ID）
        let productId = this.isEdit ? this.batteryId : result?.data?.id || result?.id;
        console.log('获取到商品ID:', productId);

        // 保存服务和安装费用
        if (productId) {
          console.log('开始保存服务和安装费用...');

          uni.showLoading({
            title: '保存服务信息中...',
            mask: true
          });

          try {
            // 过滤有效的服务数据
            const validServices = this.form.services ?
              this.form.services.filter(service => service.name && service.name.trim()) : [];

            // 过滤有效的安装费用数据
            const validInstallationFees = this.form.installationFees ?
              this.form.installationFees.filter(fee => fee.name && fee.name.trim() && fee.price >= 0) : [];

            console.log('有效服务数据:', validServices);
            console.log('有效安装费用数据:', validInstallationFees);

            // 调用保存服务和安装费用的API
            if (validServices.length > 0 || validInstallationFees.length > 0) {
              await this.saveBatteryServicesAndFees(productId, validServices, validInstallationFees);
              console.log('服务和安装费用保存完成');
            }
          } catch (serviceError) {
            console.error('保存服务和安装费用失败:', serviceError);
            uni.showToast({
              title: '服务信息保存失败: ' + (serviceError.message || '未知错误'),
              icon: 'none',
              duration: 3000
            });
            // 服务保存失败不影响主流程，继续执行
          }
        }

        // 处理多图上传
        if (productId && this.hasNewImages()) {
          console.log('检测到新图片，开始上传...');

          uni.showLoading({
            title: '上传图片中...',
            mask: true
          });

          try {
            await this.uploadNewImages(productId);
            console.log('多图上传完成');

            // 上传完成后更新主图URL
            const mainImage = this.form.mainImages.find(img => img.isMain);
            if (mainImage && mainImage.url) {
              this.form.imageUrl = mainImage.url;
            }
          } catch (uploadError) {
            console.error('多图上传失败:', uploadError);
            uni.showToast({
              title: '图片上传失败: ' + (uploadError.message || '未知错误'),
              icon: 'none',
              duration: 3000
            });
            return; // 上传失败则不继续保存
          }
        }

        // 处理删除的图片
        if (productId && this.form.removedImages.length > 0) {
          console.log('检测到删除的图片，开始删除...');
          try {
            await this.deleteRemovedImages();
            console.log('删除图片完成');
          } catch (deleteError) {
            console.error('删除图片失败:', deleteError);
            // 删除失败不影响保存流程
          }
        }

        uni.hideLoading();
        uni.showToast({
          title: this.isEdit ? '更新成功' : '添加成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (error) {
        console.error('保存电池失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 处理无效的 ID 参数
    handleInvalidId() {


      // 切换到新建模式
      this.isEdit = false;
      this.batteryId = 0;

      // 设置默认生产日期为当天
      this.form.manufactureDate = new Date().toISOString().split('T')[0];
      // 设置默认寿命为36个月
      this.form.lifespan = 6;
    },

    // 返回上一页
    navBack() {
      uni.navigateBack()
    },

    // 添加服务项
    addService() {
      if (!this.form.services) {
        this.form.services = [];
      }

      this.form.services.push({
        name: '',
        description: ''
      });
    },

    // 删除服务项
    removeService(index) {
      if (this.form.services && this.form.services.length > index) {
        this.form.services.splice(index, 1);
      }
    },

    // 添加费用项
    addFee() {
      if (!this.form.installationFees) {
        this.form.installationFees = [];
      }

      this.form.installationFees.push({
        name: '',
        price: 0,
        description: ''
      });
    },

    // 删除费用项
    removeFee(index) {
      if (this.form.installationFees && this.form.installationFees.length > index) {
        this.form.installationFees.splice(index, 1);
      }
    },

    // 从字符串中提取数值
    extractNumericValue(str) {
      if (!str) return '';

      // 使用正则表达式提取数字（包括小数）
      const match = str.match(/(\d+\.?\d*)/);
      return match ? parseFloat(match[1]) : '';
    },

    // 将API状态映射为前端状态
    _mapStatusFromApi(apiStatus) {
      const statusMap = {
        'Available': 1,
        'Rented': 2,
        'Sold': 3,
        'Maintenance': 4
      };
      return statusMap[apiStatus] || 1;
    },

    // 将前端状态映射为API状态
    _mapStatusToApi(frontendStatus) {
      const statusMap = {
        1: 'Available',
        2: 'Rented',
        3: 'Sold',
        4: 'Maintenance'
      };
      return statusMap[frontendStatus] || 'Available';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'Available': '可用',
        'Rented': '已租出',
        'Sold': '已售出',
        'Maintenance': '维修中'
      };
      return statusMap[status] || status || '未知';
    },

    // 获取状态名称（根据前端状态ID）
    getStatusName(statusId) {
      const status = this.statusList.find(s => s.id === statusId);
      return status ? status.name : '未知';
    },

    // 状态选择变化
    onStatusChange(e) {
      const index = e.detail.value;
      const selectedStatus = this.statusList[index];

      if (selectedStatus) {
        this.form.status = selectedStatus.id;
        this.statusIndex = index;
        console.log('选择状态:', selectedStatus);
      }
    },

    // 更新状态索引
    updateStatusIndex() {
      if (this.form.status) {
        const index = this.statusList.findIndex(status => status.id === this.form.status);
        if (index !== -1) {
          this.statusIndex = index;
          console.log('更新状态索引:', index, this.statusList[index]);
        }
      }
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '未设置';

      try {
        const date = new Date(dateTime);
        if (isNaN(date.getTime())) return '无效日期';

        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '格式错误';
      }
    },

    // ========== 多图上传相关方法 ==========

    // 主图片列表变化
    onMainImagesChange(images) {
      console.log('主图片列表变化:', images);

      // 避免无限循环，只在数据真正变化时更新
      if (JSON.stringify(images) !== JSON.stringify(this.form.mainImages)) {
        this.form.mainImages = [...images];

        // 更新兼容性字段（第一张图片作为主图）
        const mainImage = images.find(img => img.isMain) || images[0];
        this.form.imageUrl = mainImage ? (mainImage.url || mainImage.tempPath) : '';
      }
    },

    // 主图变化
    onMainImageChange(mainImage) {
      console.log('主图变化:', mainImage);
      this.form.imageUrl = mainImage ? (mainImage.url || mainImage.tempPath) : '';
    },

    // 图片删除
    onImageRemove(removedImage) {
      console.log('图片删除:', removedImage);

      // 如果是已上传的图片，添加到删除列表
      if (removedImage.url && !removedImage.isNew) {
        this.form.removedImages.push(removedImage);
      }
    },



    // 检查是否有新图片需要上传
    hasNewImages() {
      return this.form.mainImages.some(img => img.isNew && img.tempPath);
    },

    // 上传新图片
    async uploadNewImages(productId) {
      // 验证商品ID
      if (!productId || productId <= 0) {
        throw new Error('无效的商品ID，无法上传图片');
      }

      const newImages = this.form.mainImages.filter(img => img.isNew && img.tempPath);

      if (newImages.length === 0) {
        console.log('没有新图片需要上传');
        return;
      }

      console.log('开始上传新图片:', newImages);
      console.log('商品ID:', productId);

      for (let i = 0; i < newImages.length; i++) {
        const image = newImages[i];
        try {
          // 验证图片路径
          if (!image.tempPath || image.tempPath.trim() === '') {
            throw new Error(`图片 ${i + 1} 的临时路径为空`);
          }

          console.log(`开始上传图片 ${i + 1}:`, {
            tempPath: image.tempPath,
            productId: productId,
            isMain: image.isMain
          });

          // 更新上传状态
          image.status = 'uploading';

          // 使用专门的商品图片上传API
          const uploadResult = await this.uploadProductImage(image.tempPath, productId);

          if (uploadResult && uploadResult.code === 200 && uploadResult.data) {
            // 更新图片信息
            image.url = uploadResult.data.imageUrl;
            image.relativePath = uploadResult.data.relativePath || ''; // 添加相对路径
            image.id = uploadResult.data.id;
            image.status = 'success';
            image.isNew = false;

            console.log(`图片 ${i + 1} 上传成功:`, uploadResult.data);

            // 如果是主图，设置为主图
            if (image.isMain && uploadResult.data.id) {
              try {
                await this.setMainImage(productId, uploadResult.data.id);
                console.log(`图片 ${i + 1} 已设置为主图`);
              } catch (mainError) {
                console.error(`设置主图失败:`, mainError);
                // 设置主图失败不影响上传流程
              }
            }
          } else {
            throw new Error(uploadResult?.message || '上传失败');
          }
        } catch (error) {
          console.error(`图片 ${i + 1} 上传失败:`, error);
          image.status = 'error';
          throw error;
        }
      }
    },

    // 上传商品图片（使用正确的API）
    async uploadProductImage(tempFilePath, productId) {
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: `${baseUrl}/api/batteryimage/upload`,
          filePath: tempFilePath,
          name: 'file',
          formData: {
            productId: productId.toString(),
            imageType: 'main'
          },
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            console.log('商品图片上传响应:', res);
            try {
              const result = JSON.parse(res.data);
              console.log('解析后的响应:', result);
              resolve(result);
            } catch (e) {
              console.error('解析响应失败:', e, res.data);
              reject(new Error('服务器响应格式错误'));
            }
          },
          fail: (err) => {
            console.error('上传文件失败:', err);
            reject(new Error('网络错误，上传失败'));
          }
        });
      });
    },

    // 设置主图
    async setMainImage(productId, imageId) {
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      console.log('设置主图请求:', {
        url: `${baseUrl}/api/productimage/product/${productId}/main/${imageId}`,
        productId: productId,
        imageId: imageId
      });

      const response = await uni.request({
        url: `${baseUrl}/api/productimage/product/${productId}/main/${imageId}`,
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('设置主图响应:', response);

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data;
      } else {
        throw new Error(response.data?.message || '设置主图失败');
      }
    },

    // 上传单张图片到多选上传API（保留兼容性）
    async uploadSingleImageToMultiAPI(tempFilePath, productId, imageType) {
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: `${baseUrl}/api/multiimageupload/upload-multiple`,
          filePath: tempFilePath,
          name: 'files',
          formData: {
            productId: productId,
            imageType: imageType,
            autoCompress: 'true',
            generateThumbnail: 'false'
          },
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            try {
              const result = JSON.parse(res.data);
              if (result.code === 200 && result.data.results.length > 0) {
                resolve(result.data.results[0]);
              } else {
                reject(new Error(result.message || '上传失败'));
              }
            } catch (e) {
              reject(new Error('解析响应失败'));
            }
          },
          fail: () => {
            reject(new Error('网络错误'));
          }
        });
      });
    },

    // 删除已移除的图片
    async deleteRemovedImages() {
      if (this.form.removedImages.length === 0) {
        return;
      }

      console.log('开始删除图片:', this.form.removedImages);

      for (const removedImage of this.form.removedImages) {
        try {
          await this.deleteImageFromServer(removedImage.url);
          console.log('删除图片成功:', removedImage.url);
        } catch (error) {
          console.error('删除图片失败:', removedImage.url, error);
          // 继续删除其他图片，不中断流程
        }
      }

      // 清空删除列表
      this.form.removedImages = [];
    },

    // 从服务器删除图片
    async deleteImageFromServer(imageUrl) {
      if (!imageUrl) {
        return;
      }

      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      console.log('删除图片请求:', {
        url: `${baseUrl}/api/productimage/delete`,
        imageUrl: imageUrl
      });

      const response = await uni.request({
        url: `${baseUrl}/api/productimage/delete`,
        method: 'DELETE',
        data: {
          imageUrl: imageUrl,
          hardDelete: false
        },
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('删除图片响应:', response);

      if (response.statusCode === 200 && response.data.code === 200) {
        return response.data;
      } else {
        throw new Error(response.data?.message || '删除失败');
      }
    },

    // ========== 图片上传相关方法 ==========

    // 从文件路径创建 File 对象（H5 环境）
    async createFileFromPath(filePath, fileName) {
      try {
        // 在 H5 环境下，如果是 blob URL，可以直接使用
        if (filePath.startsWith('blob:')) {
          const response = await fetch(filePath);
          const blob = await response.blob();
          return new File([blob], fileName, { type: blob.type });
        }

        // 其他情况暂时返回 null，让后端处理路径
        console.log('无法从路径创建 File 对象，将使用路径上传');
        return null;
      } catch (error) {
        console.error('创建 File 对象失败:', error);
        return null;
      }
    },

    // 检查是否有待上传的图片
    hasPendingImages() {
      return this.pendingImages.main || this.pendingImages.detail || this.pendingImages.spec;
    },

    // 上传待上传的图片
    async uploadPendingImages(productId) {
      const uploadPromises = [];

      if (this.pendingImages.main) {
        uploadPromises.push(this.uploadImageWithProductId(this.pendingImages.main, 'main', productId));
      }

      if (this.pendingImages.detail) {
        uploadPromises.push(this.uploadImageWithProductId(this.pendingImages.detail, 'detail', productId));
      }

      if (this.pendingImages.spec) {
        uploadPromises.push(this.uploadImageWithProductId(this.pendingImages.spec, 'spec', productId));
      }

      const results = await Promise.all(uploadPromises);
      console.log('所有图片上传完成:', results);

      // 清空待上传图片
      this.pendingImages = {
        main: null,
        detail: null,
        spec: null
      };

      return results;
    },

    // 使用商品ID上传图片
    async uploadImageWithProductId(tempFilePath, imageType, productId) {
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      let uploadUrl = '';
      switch (imageType) {
        case 'main':
          uploadUrl = `${baseUrl}/api/batteryimage/upload-main`;
          break;
        case 'detail':
          uploadUrl = `${baseUrl}/api/batteryimage/upload-detail`;
          break;
        case 'spec':
          uploadUrl = `${baseUrl}/api/batteryimage/upload-spec`;
          break;
        default:
          throw new Error('无效的图片类型');
      }

      const uploadResult = await this.uploadFileWithProductId(tempFilePath, uploadUrl, token, productId);
      console.log(`${imageType}图片上传结果:`, uploadResult);

      if (uploadResult && uploadResult.code === 200 && uploadResult.data) {
        // 更新表单中的图片URL
        switch (imageType) {
          case 'main':
            this.form.imageUrl = uploadResult.data.imageUrl;
            break;
          case 'detail':
            this.form.detailImageUrl = uploadResult.data.imageUrl;
            break;
      
        }
        return uploadResult.data;
      } else {
        throw new Error(uploadResult?.message || '上传失败');
      }
    },

    // 选择主图片
    async selectMainImage() {
      await this.selectAndUploadImage('main');
    },

    // 选择详情图片
    async selectDetailImage() {
      await this.selectAndUploadImage('detail');
    },

    // 选择规格图片
    async selectSpecImage() {
      await this.selectAndUploadImage('spec');
    },

    // 选择并暂存图片的通用方法
    async selectAndUploadImage(imageType) {
      try {
        // 选择图片
        const result = await this.chooseImage();
        if (!result || !result.tempFilePaths || result.tempFilePaths.length === 0) {
          return;
        }

        const tempFilePath = result.tempFilePaths[0];
        console.log(`选择${imageType}图片:`, tempFilePath);

        // 暂存图片路径，等商品保存后再上传
        this.pendingImages[imageType] = tempFilePath;

        // 显示预览图片
        switch (imageType) {
          case 'main':
            this.form.imageUrl = tempFilePath;
            break;
          case 'detail':
            this.form.detailImageUrl = tempFilePath;
            break;
         
        }

        uni.showToast({
          title: '图片选择成功，将在保存时上传',
          icon: 'success'
        });
      } catch (error) {
        console.error(`选择${imageType}图片失败:`, error);
        uni.showToast({
          title: error.message || '选择图片失败',
          icon: 'none'
        });
      }
    },

    // 选择图片
    chooseImage() {
      return new Promise((resolve, reject) => {
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            console.log('选择图片成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('选择图片失败:', err);
            reject(new Error('选择图片失败'));
          }
        });
      });
    },

    // 上传图片到服务器
    async uploadImageToServer(tempFilePath, imageType) {
      // 设置上传状态
      this.uploadingImages[imageType] = true;

      uni.showLoading({
        title: '上传中...',
        mask: true
      });

      try {
        // 获取用户token
        const token = uni.getStorageSync('token');
        if (!token) {
          throw new Error('请先登录');
        }

        // 构建上传URL
        const baseUrl = process.env.NODE_ENV === 'development'
          ? 'http://localhost:5242'
          : window.location.origin;

        let uploadUrl = '';
        switch (imageType) {
          case 'main':
            uploadUrl = `${baseUrl}/api/batteryimage/upload-main`;
            break;
          case 'detail':
            uploadUrl = `${baseUrl}/api/batteryimage/upload-detail`;
            break;
          case 'spec':
            uploadUrl = `${baseUrl}/api/batteryimage/upload-spec`;
            break;
          default:
            throw new Error('无效的图片类型');
        }

        // 上传文件
        const uploadResult = await this.uploadFile(tempFilePath, uploadUrl, token);
        console.log(`${imageType}图片上传结果:`, uploadResult);

        if (uploadResult && uploadResult.code === 200 && uploadResult.data) {
          // 更新表单中的图片URL
          switch (imageType) {
            case 'main':
              this.form.imageUrl = uploadResult.data.imageUrl;
              break;
            case 'detail':
              this.form.detailImageUrl = uploadResult.data.imageUrl;
              break;
         
          }

          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(uploadResult?.message || '上传失败');
        }
      } catch (error) {
        console.error(`上传${imageType}图片失败:`, error);
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      } finally {
        this.uploadingImages[imageType] = false;
        uni.hideLoading();
      }
    },

    // 上传文件（带商品ID）
    uploadFileWithProductId(filePath, url, token, productId) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: url,
          filePath: filePath,
          name: 'file',
          formData: {
            productId: productId
          },
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            console.log('上传文件响应:', res);
            try {
              const result = JSON.parse(res.data);
              resolve(result);
            } catch (e) {
              console.error('解析上传响应失败:', e);
              reject(new Error('服务器响应格式错误'));
            }
          },
          fail: (err) => {
            console.error('上传文件失败:', err);
            reject(new Error('网络错误，上传失败'));
          }
        });
      });
    },

    // 上传文件（旧版本，保留兼容性）
    uploadFile(filePath, url, token) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: url,
          filePath: filePath,
          name: 'file',
          formData: {
            // 只有在编辑模式且batteryId是有效数字时才传递batteryId
            ...(this.isEdit && this.batteryId && !isNaN(parseInt(this.batteryId)) && parseInt(this.batteryId) > 0
              ? { batteryId: parseInt(this.batteryId) }
              : {}),
            // 如果是电池类别且有MAC ID，传递MAC ID用于组织图片存储路径
            ...(this.isBatteryCategory && this.macId && this.macId.trim() !== ''
              ? { macId: this.macId.trim() }
              : {})
          },
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            console.log('上传文件响应:', res);
            try {
              const result = JSON.parse(res.data);
              resolve(result);
            } catch (e) {
              console.error('解析上传响应失败:', e);
              reject(new Error('服务器响应格式错误'));
            }
          },
          fail: (err) => {
            console.error('上传文件失败:', err);
            reject(new Error('网络错误，上传失败'));
          }
        });
      });
    },

    // 删除主图片
    async deleteMainImage() {
      await this.deleteImage('main', this.form.imageUrl);
    },

    // 删除详情图片
    async deleteDetailImage() {
      await this.deleteImage('detail', this.form.detailImageUrl);
    },

  

    // 删除图片的通用方法
    async deleteImage(imageType, imageUrl) {
      if (!imageUrl) {
        return;
      }

      try {
        const result = await this.showConfirmDialog('确认删除', '确定要删除这张图片吗？');
        if (!result.confirm) {
          return;
        }

        uni.showLoading({
          title: '删除中...',
          mask: true
        });

        // 导入图片API
        const BatteryImageAPI = (await import('@/api/batteryImage')).default;

        // 调用删除接口
        const response = await BatteryImageAPI.deleteImage(imageUrl);
        console.log(`删除${imageType}图片响应:`, response);

        if (response && response.code === 200) {
          // 清空表单中的图片URL
          switch (imageType) {
            case 'main':
              this.form.imageUrl = '';
              break;
            case 'detail':
              this.form.detailImageUrl = '';
              break;
            
              break;
          }

          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } else {
          throw new Error(response?.message || '删除失败');
        }
      } catch (error) {
        console.error(`删除${imageType}图片失败:`, error);
        uni.showToast({
          title: error.message || '删除失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) {
        return;
      }

      const displayUrl = this.getImageDisplayUrl(imageUrl);
      uni.previewImage({
        urls: [displayUrl],
        current: displayUrl
      });
    },

    // 获取图片显示URL
    getImageDisplayUrl(imagePath) {
      if (!imagePath) {
        return '';
      }

      // 如果是blob URL，直接返回（这是浏览器生成的临时URL）
      if (imagePath.startsWith('blob:')) {
        return imagePath;
      }

      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath;
      }

      // 如果是临时路径，直接返回
      if (imagePath.startsWith('wxfile://') || imagePath.startsWith('file://')) {
        return imagePath;
      }

      // 如果是data URL，直接返回
      if (imagePath.startsWith('data:')) {
        return imagePath;
      }

      // 如果是相对路径，拼接基础URL
      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      // 确保路径以 / 开头
      const path = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;

      return `${baseUrl}${path}`;
    },

    // 显示确认对话框
    showConfirmDialog(title, content) {
      return new Promise((resolve) => {
        uni.showModal({
          title: title,
          content: content,
          success: (res) => {
            resolve(res);
          },
          fail: () => {
            resolve({ confirm: false });
          }
        });
      });
    },

    // ========== 服务和安装费用管理方法 ==========

    // 保存商品服务和安装费用
    async saveBatteryServicesAndFees(batteryId, services, installationFees) {
      try {
        console.log('开始保存服务和安装费用:', { batteryId, services, installationFees });

        const results = {};

        // 保存服务
        if (services && services.length > 0) {
          console.log('保存商品服务:', services);
          results.services = await this.batchUpdateBatteryServices(batteryId, services);
        }

        // 保存安装费用
        if (installationFees && installationFees.length > 0) {
          console.log('保存安装费用:', installationFees);
          results.installationFees = await this.batchUpdateBatteryInstallationFees(batteryId, installationFees);
        }

        console.log('服务和安装费用保存完成:', results);
        return results;
      } catch (error) {
        console.error('保存服务和安装费用失败:', error);
        throw error;
      }
    },

    // 批量更新商品服务
    async batchUpdateBatteryServices(batteryId, services) {
      try {
        // 1. 获取现有服务
        const existingServices = await this.getBatteryServices(batteryId);

        // 2. 删除现有服务
        if (existingServices && existingServices.length > 0) {
          const deletePromises = existingServices.map(service =>
            this.deleteBatteryService(service.id)
          );
          await Promise.all(deletePromises);
        }

        // 3. 创建新服务
        if (services && services.length > 0) {
          const createPromises = services.map(service =>
            this.createBatteryService(batteryId, service)
          );
          return await Promise.all(createPromises);
        }

        return [];
      } catch (error) {
        console.error('批量更新服务失败:', error);
        throw error;
      }
    },

    // 批量更新商品安装费用
    async batchUpdateBatteryInstallationFees(batteryId, fees) {
      try {
        // 1. 获取现有安装费用
        const existingFees = await this.getBatteryInstallationFees(batteryId);

        // 2. 删除现有安装费用
        if (existingFees && existingFees.length > 0) {
          const deletePromises = existingFees.map(fee =>
            this.deleteBatteryInstallationFee(fee.id)
          );
          await Promise.all(deletePromises);
        }

        // 3. 创建新安装费用
        if (fees && fees.length > 0) {
          const createPromises = fees.map(fee =>
            this.createBatteryInstallationFee(batteryId, fee)
          );
          return await Promise.all(createPromises);
        }

        return [];
      } catch (error) {
        console.error('批量更新安装费用失败:', error);
        throw error;
      }
    },

    // API调用方法
    async getBatteryServices(batteryId) {
      return await this.callAPI('GET', `/batteries/${batteryId}/services`);
    },

    async createBatteryService(batteryId, data) {
      return await this.callAPI('POST', `/batteries/${batteryId}/services`, data);
    },

    async deleteBatteryService(id) {
      return await this.callAPI('DELETE', `/batteries/services/${id}`);
    },

    async getBatteryInstallationFees(batteryId) {
      return await this.callAPI('GET', `/batteries/${batteryId}/installation-fees`);
    },

    async createBatteryInstallationFee(batteryId, data) {
      return await this.callAPI('POST', `/batteries/${batteryId}/installation-fees`, data);
    },

    async deleteBatteryInstallationFee(id) {
      return await this.callAPI('DELETE', `/batteries/installation-fees/${id}`);
    },



    // 通用API调用方法
    async callAPI(method, url, data = null) {
      const token = uni.getStorageSync('token');
      if (!token) {
        throw new Error('请先登录');
      }

      const baseUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:5242'
        : window.location.origin;

      const requestOptions = {
        url: `${baseUrl}/api${url}`,
        method: method,
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        requestOptions.data = data;
      }

      console.log('API调用:', requestOptions);

      const response = await uni.request(requestOptions);

      console.log('API响应:', response);

      if (response.statusCode === 200) {
        return response.data.data || response.data;
      } else {
        throw new Error(response.data.message || `API调用失败: ${response.statusCode}`);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.battery-edit-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

.header {
  background-color: #2979ff;
  padding: 20rpx 30rpx;
  color: #fff;
  margin-bottom: 20rpx;

  .title {
    font-size: 36rpx;
    font-weight: bold;
  }
}

.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 0 20rpx 40rpx;
}

.form-item {
  margin-bottom: 30rpx;

  .label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 15rpx;
    display: block;
  }

  input {
    width: 100%;
    height: 80rpx;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;

    &:disabled {
      background-color: #f5f5f5;
      color: #999;
    }
  }

  .picker-value {
    height: 80rpx;
    line-height: 80rpx;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.disabled {
      background-color: #f5f5f5;
      border-color: #eee;
      color: #999;
    }

    .placeholder {
      color: #999;
    }

    .iconfont {
      font-size: 24rpx;
      color: #999;
    }
  }

  .textarea {
    width: 100%;
    height: 160rpx;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
  }

  .code-input-group, .mac-input-group {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .generate-btn, .test-btn {
    margin-left: 10rpx;
    font-size: 24rpx;
    padding: 0 20rpx;
    height: 60rpx;
    line-height: 60rpx;
    background-color: #f0f0f0;
    color: #333;
    border-radius: 6rpx;
    flex-shrink: 0;

    &:disabled {
      background-color: #e0e0e0;
      color: #999;
    }
  }

  .generate-btn {
    background-color: #2979ff;
    color: #fff;

    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }

  .test-btn {
    background-color: #4caf50;
    color: #fff;

    &:disabled {
      background-color: #ccc;
      color: #999;
    }
  }

  // 图片上传组件样式
  .image-upload-container {
    width: 100%;
    border: 2rpx dashed #ddd;
    border-radius: 8rpx;
    overflow: hidden;
  }

  .image-preview {
    position: relative;
    width: 100%;

    .preview-image {
      width: 100%;
      height: 300rpx;
      background-color: #f5f5f5;
    }

    .image-actions {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20rpx;
      gap: 20rpx;

      .action-btn {
        padding: 10rpx 20rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        border: none;
        color: #fff;

        &.change-btn {
          background-color: #2979ff;
        }

        &.delete-btn {
          background-color: #ff5722;
        }
      }
    }
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300rpx;
    background-color: #fafafa;
    color: #999;
    text-align: center;
    padding: 40rpx;

    .upload-icon {
      font-size: 80rpx;
      margin-bottom: 20rpx;
    }

    .upload-text {
      font-size: 28rpx;
      margin-bottom: 10rpx;
      color: #666;
    }

    .upload-tip {
      font-size: 24rpx;
      color: #999;
      line-height: 1.4;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }
}

.form-section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 40rpx 0 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #2979ff;
}

.service-list, .fee-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.service-item, .fee-item {
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.service-header, .fee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-title, .fee-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.service-delete, .fee-delete {
  font-size: 24rpx;
  color: #ff5722;
  padding: 6rpx 12rpx;
}

.add-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.add-icon {
  font-size: 32rpx;
  color: #2979ff;
  margin-right: 10rpx;
}

.add-text {
  font-size: 28rpx;
  color: #2979ff;
}

.upload-tip {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #e3f2fd;
  border-radius: 8rpx;
  border-left: 4rpx solid #2196f3;
}

.tip-text {
  font-size: 24rpx;
  color: #1976d2;
  line-height: 1.5;
}

.btn-section {
  display: flex;
  padding: 0 20rpx;

  button {
    flex: 1;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
    border-radius: 45rpx;
  }

  .cancel-btn {
    color: #666;
    background-color: #f5f5f5;
    margin-right: 20rpx;
  }

  .submit-btn {
    color: #fff;
    background-color: #2979ff;
  }
}
</style>