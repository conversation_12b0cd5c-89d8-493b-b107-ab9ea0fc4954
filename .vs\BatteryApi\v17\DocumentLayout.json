{"Version": 1, "WorkspaceRootPath": "D:\\Code\\BatteryApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{897FABC8-069A-4EB4-AEA6-AC1A39A893EE}|BatteryApi\\BatteryApi.csproj|d:\\code\\batteryapi\\batteryapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{897FABC8-069A-4EB4-AEA6-AC1A39A893EE}|BatteryApi\\BatteryApi.csproj|solutionrelative:batteryapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Code\\BatteryApi\\BatteryApi\\appsettings.json", "RelativeDocumentMoniker": "BatteryApi\\appsettings.json", "ToolTip": "D:\\Code\\BatteryApi\\BatteryApi\\appsettings.json", "RelativeToolTip": "BatteryApi\\appsettings.json", "ViewState": "AgIAAAwAAAAAAAAAAAAAABgAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-01T04:16:24.462Z", "EditorCaption": ""}]}]}]}