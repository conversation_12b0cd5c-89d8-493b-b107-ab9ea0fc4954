<template>
  <view class="uni-map">
    <map
      id="map"
      class="map"
      :latitude="latitude"
      :longitude="longitude"
      :markers="markers"
      :scale="scale"
      :show-location="showLocation"
      @regionchange="onRegionChange"
      @tap="onMapTap"
    ></map>
    <!-- 中心点标记 -->
    <view class="center-marker" v-if="showCenterMarker">
      <view class="marker-pin"></view>
    </view>
    <!-- 当前位置按钮 -->
    <view v-if="showLocationButton" class="location-btn" @click="moveToCurrentLocation">
      <u-icon name="map" size="30" color="#2979ff"></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniMap',
  props: {
    // 默认纬度
    latitude: {
      type: Number,
      required: true
    },
    // 默认经度
    longitude: {
      type: Number,
      required: true
    },
    // 标记点
    markers: {
      type: Array,
      default: () => []
    },
    // 缩放级别
    scale: {
      type: Number,
      default: 14
    },
    // 是否显示定位点
    showLocation: {
      type: Boolean,
      default: true
    },
    // 是否显示中心标记
    showCenterMarker: {
      type: <PERSON>olean,
      default: false
    },
    // 是否显示定位按钮
    showLocationButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      mapContext: null
    };
  },
  mounted() {
    this.mapContext = uni.createMapContext('map', this);
  },
  methods: {
    // 移动到当前位置
    moveToCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: res => {
          this.mapContext.moveToLocation({
            latitude: res.latitude,
            longitude: res.longitude
          });
          this.$emit('location-change', {
            latitude: res.latitude,
            longitude: res.longitude
          });
        },
        fail: err => {
          uni.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none'
          });
        }
      });
    },
    // 地图区域改变事件
    onRegionChange(e) {
      this.$emit('region-change', e);
      if (e.type === 'end' && this.mapContext) {
        this.mapContext.getCenterLocation({
          success: res => {
            this.$emit('center-change', {
              latitude: res.latitude,
              longitude: res.longitude
            });
          }
        });
      }
    },
    // 地图点击事件
    onMapTap(e) {
      this.$emit('map-tap', e);
    }
  }
};
</script>

<style lang="scss" scoped>
.uni-map {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
}

.center-marker {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  pointer-events: none;
}

.marker-pin {
  width: 40rpx;
  height: 40rpx;
  background-color: #ffffff;
  border: 4rpx solid #2979ff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);

  &::after {
    content: '';
    position: absolute;
    bottom: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10rpx solid transparent;
    border-right: 10rpx solid transparent;
    border-top: 20rpx solid #2979ff;
  }
}

.location-btn {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
</style>