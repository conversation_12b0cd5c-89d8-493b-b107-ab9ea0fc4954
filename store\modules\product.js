// 商品模块的状态管理
import productAPI from '@/api/product'

// 状态映射函数
function mapStatusFromApi(status) {
  const statusMap = {
    'Available': '可用',
    'Rented': '已租出',
    'Sold': '已售出',
    'Maintenance': '维护中',
    'Damaged': '损坏',
    'Retired': '已退役'
  };
  return statusMap[status] || status || '未知';
}

// 计算剩余寿命的辅助函数
function calculateRemainingLife(manufactureDate, lifespan) {
  // 默认寿命为36个月
  const defaultLifespan = 36;

  // 使用提供的寿命或默认值
  const totalLifespan = lifespan || defaultLifespan;

  if (!manufactureDate) {
    return totalLifespan; // 如果没有生产日期，返回完整寿命
  }

  try {
    // 将生产日期转换为 Date 对象
    const mfgDate = new Date(manufactureDate);
    const now = new Date();

    // 检查生产日期是否有效
    if (isNaN(mfgDate.getTime())) {
      console.warn('生产日期无效:', manufactureDate);
      return totalLifespan;
    }

    // 计算生产日期到现在的月数
    const ageInMonths = Math.floor((now - mfgDate) / (30 * 24 * 60 * 60 * 1000));

    // 计算剩余寿命
    const remaining = Math.max(0, totalLifespan - ageInMonths);

    console.log(`生产日期: ${manufactureDate}, 寿命: ${totalLifespan} 月, 已使用: ${ageInMonths} 月, 剩余: ${remaining} 月`);

    return remaining;
  } catch (error) {
    console.error('计算剩余寿命出错:', error);
    return totalLifespan; // 出错时返回完整寿命
  }
}

const state = {
  // 商品列表
  productList: [],
  // 商品规格列表
  specList: [],
  // 当前选中的商品
  currentProduct: null,
  // 商品详情加载状态
  detailLoading: false,
  // 商品类别列表
  categoryList: [],
  // 当前类别商品列表
  categoryProducts: [],
  // 类别商品列表加载状态
  categoryLoading: false,
  // 类别商品列表总数
  categoryProductTotal: 0,
  // 类别商品列表当前页
  categoryProductPage: 1,
  // 类别商品列表总页数
  categoryProductTotalPages: 0
}

const mutations = {
  // 设置商品列表
  SET_PRODUCT_LIST(state, list) {
    state.productList = list
  },
  // 设置商品规格列表
  SET_SPEC_LIST(state, list) {
    state.specList = list
  },
  // 设置当前选中的商品
  SET_CURRENT_PRODUCT(state, product) {
    state.currentProduct = product
  },
  // 设置商品详情加载状态
  SET_DETAIL_LOADING(state, status) {
    state.detailLoading = status
  },
  // 设置商品类别列表
  SET_CATEGORY_LIST(state, list) {
    state.categoryList = list
  },
  // 设置当前类别商品列表
  SET_CATEGORY_PRODUCTS(state, list) {
    state.categoryProducts = list
  },
  // 设置类别商品列表加载状态
  SET_CATEGORY_LOADING(state, status) {
    state.categoryLoading = status
  },
  // 设置类别商品列表总数
  SET_CATEGORY_PRODUCT_TOTAL(state, total) {
    state.categoryProductTotal = total
  },
  // 设置类别商品列表当前页
  SET_CATEGORY_PRODUCT_PAGE(state, page) {
    state.categoryProductPage = page
  },
  // 设置类别商品列表总页数
  SET_CATEGORY_PRODUCT_TOTAL_PAGES(state, totalPages) {
    state.categoryProductTotalPages = totalPages
  }
}

const actions = {
  // 获取商品列表
  getProductList({ commit, state }, params = {}) {
    console.log('获取商品列表', params);

    // 判断是否是加载更多
    const isLoadMore = params.page && params.page > 1;
    console.log('是否是加载更多:', isLoadMore);

    return new Promise((resolve, reject) => {
      productAPI.getProducts(params)
        .then(response => {
          console.log('获取商品列表原始响应:', JSON.stringify(response));
          console.log('响应类型:', typeof response, '响应结构:', response ? Object.keys(response) : 'null');

          let items = [];
          let total = 0;

          try {
            // 处理响应数据
            if (response && response.data && response.data.products && Array.isArray(response.data.products)) {
              console.log('使用 response.data.products，长度:', response.data.products.length);

              // 将 API 返回的数据转换为前端需要的格式
              items = response.data.products.map(item => {
                // 处理价格字段，确保它们是数字
                let price = 0;
                let rentPrice = 0;

                // 尝试将价格转换为数字
                if (item.price !== undefined && item.price !== null) {
                  price = parseFloat(item.price);
                  if (isNaN(price)) price = 0;
                }

                if (item.rentPrice !== undefined && item.rentPrice !== null) {
                  rentPrice = parseFloat(item.rentPrice);
                  if (isNaN(rentPrice)) rentPrice = 0;
                }

                // 处理生产日期
                let manufactureDate = '未知';
                if (item.manufactureDate) {
                  try {
                    // 如果是带有时区信息的日期字符串，先提取日期部分
                    let dateStr = item.manufactureDate;
                    if (typeof dateStr === 'string' && dateStr.includes('T')) {
                      dateStr = dateStr.split('T')[0];
                    }

                    // 尝试将日期字符串转换为 Date 对象
                    const date = new Date(dateStr);
                    if (!isNaN(date.getTime())) {
                      // 如果是有效的日期，直接使用提取的日期部分
                      manufactureDate = dateStr;
                      console.log('store中原始生产日期:', item.manufactureDate, '格式化后:', manufactureDate);
                    }
                  } catch (e) {
                    console.error('处理生产日期出错:', e);
                  }
                }

                return {
                  id: item.id,
                  code: item.productCode || item.code || `PRD-${item.id}`,
                  name: item.name || '',
                  spec: item.spec || '未知规格',
                  categoryCode: item.categoryCode || '',
                  categoryId: item.categoryId,
                  categoryName: item.categoryName || '',
                  status: mapStatusFromApi(item.status),
                  price: price,
                  rentPrice: rentPrice,
                  manufactureDate: manufactureDate,
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  voltage: item.voltage || '',
                  capacity: item.capacity || '',
                  cycleCount: item.cycleCount || '',
                  chargeTime: item.chargeTime || ''
                };
              });

              total = response.data.totalItems || items.length;
            }
            // 如果响应是数组，直接使用
            else if (Array.isArray(response)) {
              console.log('响应是数组，长度:', response.length);
              items = response;
              total = response.length;
            }
            // 如果响应有 data 属性且是数组
            else if (response && response.data && Array.isArray(response.data)) {
              console.log('使用 response.data 数组，长度:', response.data.length);
              items = response.data;
              total = items.length;
            }

            console.log('处理后的商品列表:', items, '总数量:', total);

            if (items.length > 0) {
              // 如果是加载更多，则将新数据追加到现有数据中
              if (isLoadMore) {
                const combinedList = [...state.productList, ...items];
                console.log('合并后的列表长度:', combinedList.length);
                commit('SET_PRODUCT_LIST', combinedList);
              } else {
                // 如果是首次加载或刷新，直接替换现有数据
                console.log('设置新列表，长度:', items.length);
                commit('SET_PRODUCT_LIST', items);
              }

              // 返回包含列表和总数量的对象
              resolve({ list: items, total });
            } else {
              console.warn('商品列表数据格式不正确或为空:', response);

              // 如果不是加载更多，则清空列表
              if (!isLoadMore) {
                commit('SET_PRODUCT_LIST', []);
              }

              resolve({ list: [], total: 0 });
            }
          } catch (error) {
            console.error('处理商品列表数据时出错:', error);
            // 如果处理出错，尝试直接使用原始数据
            if (response) {
              if (response.data && response.data.products && Array.isArray(response.data.products)) {
                items = response.data.products;
                total = response.data.totalItems || items.length;
                commit('SET_PRODUCT_LIST', items);
                resolve({ list: items, total });
              } else if (Array.isArray(response)) {
                items = response;
                total = response.length;
                commit('SET_PRODUCT_LIST', items);
                resolve({ list: items, total });
              } else {
                commit('SET_PRODUCT_LIST', []);
                resolve({ list: [], total: 0 });
              }
            }
          }
        })
        .catch(error => {
          console.error('获取商品列表失败', error);
          reject(error);
        });
    });
  },

  // 删除商品
  deleteProduct({ commit, state }, productId) {
    return new Promise((resolve, reject) => {
      productAPI.deleteProduct(productId)
        .then(response => {
          console.log('删除商品响应:', response);
          
          // 从列表中移除已删除的商品
          const updatedList = state.productList.filter(item => item.id !== productId);
          commit('SET_PRODUCT_LIST', updatedList);
          
          resolve(response);
        })
        .catch(error => {
          console.error('删除商品失败', error);
          reject(error);
        });
    });
  }
}

const getters = {
  // 获取商品总数
  productCount: state => state.productList.length,
  
  // 根据状态获取商品数量
  getProductCountByStatus: state => status => {
    return state.productList.filter(product => product.status === status).length;
  },
  
  // 根据ID获取商品
  getProductById: state => id => {
    return state.productList.find(product => product.id === id);
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
