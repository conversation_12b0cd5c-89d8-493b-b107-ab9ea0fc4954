// 商品模块的状态管理
import productAPI from '@/api/product'

// 状态映射函数
function mapStatusFromApi(status) {
  const statusMap = {
    'Available': '可用',
    'Rented': '已租出',
    'Sold': '已售出',
    'Maintenance': '维护中',
    'Damaged': '损坏',
    'Retired': '已退役'
  };
  return statusMap[status] || status || '未知';
}

// 计算剩余寿命的辅助函数
function calculateRemainingLife(manufactureDate, lifespan) {
  // 默认寿命为36个月
  const defaultLifespan = 36;

  // 使用提供的寿命或默认值
  const totalLifespan = lifespan || defaultLifespan;

  if (!manufactureDate) {
    return totalLifespan; // 如果没有生产日期，返回完整寿命
  }

  try {
    // 将生产日期转换为 Date 对象
    const mfgDate = new Date(manufactureDate);
    const now = new Date();

    // 检查生产日期是否有效
    if (isNaN(mfgDate.getTime())) {
      console.warn('生产日期无效:', manufactureDate);
      return totalLifespan;
    }

    // 计算生产日期到现在的月数
    const ageInMonths = Math.floor((now - mfgDate) / (30 * 24 * 60 * 60 * 1000));

    // 计算剩余寿命
    const remaining = Math.max(0, totalLifespan - ageInMonths);

    console.log(`生产日期: ${manufactureDate}, 寿命: ${totalLifespan} 月, 已使用: ${ageInMonths} 月, 剩余: ${remaining} 月`);

    return remaining;
  } catch (error) {
    console.error('计算剩余寿命出错:', error);
    return totalLifespan; // 出错时返回完整寿命
  }
}

const state = {
  // 商品列表
  productList: [],
  // 商品规格列表
  specList: [],
  // 当前选中的商品
  currentProduct: null,
  // 商品详情加载状态
  detailLoading: false,
  // 商品类别列表
  categoryList: [],
  // 当前类别商品列表
  categoryProducts: [],
  // 类别商品列表加载状态
  categoryLoading: false,
  // 类别商品列表总数
  categoryProductTotal: 0,
  // 类别商品列表当前页
  categoryProductPage: 1,
  // 类别商品列表总页数
  categoryProductTotalPages: 0
}

const mutations = {
  // 设置商品列表
  SET_PRODUCT_LIST(state, list) {
    state.productList = list
  },
  // 设置商品规格列表
  SET_SPEC_LIST(state, list) {
    state.specList = list
  },
  // 设置当前选中的商品
  SET_CURRENT_PRODUCT(state, product) {
    state.currentProduct = product
  },
  // 设置商品详情加载状态
  SET_DETAIL_LOADING(state, status) {
    state.detailLoading = status
  },
  // 设置商品类别列表
  SET_CATEGORY_LIST(state, list) {
    state.categoryList = list
  },
  // 设置当前类别商品列表
  SET_CATEGORY_PRODUCTS(state, list) {
    state.categoryProducts = list
  },
  // 设置类别商品列表加载状态
  SET_CATEGORY_LOADING(state, status) {
    state.categoryLoading = status
  },
  // 设置类别商品列表总数
  SET_CATEGORY_PRODUCT_TOTAL(state, total) {
    state.categoryProductTotal = total
  },
  // 设置类别商品列表当前页
  SET_CATEGORY_PRODUCT_PAGE(state, page) {
    state.categoryProductPage = page
  },
  // 设置类别商品列表总页数
  SET_CATEGORY_PRODUCT_TOTAL_PAGES(state, totalPages) {
    state.categoryProductTotalPages = totalPages
  }
}

const actions = {
  // 获取商品列表
  getProductList({ commit, state }, params = {}) {
    console.log('获取商品列表', params);

    // 判断是否是加载更多
    const isLoadMore = params.page && params.page > 1;
    console.log('是否是加载更多:', isLoadMore);

    return new Promise((resolve, reject) => {
      productAPI.getProducts(params)
        .then(response => {
          console.log('获取商品列表原始响应:', JSON.stringify(response));
          console.log('响应类型:', typeof response, '响应结构:', response ? Object.keys(response) : 'null');

          let items = [];
          let total = 0;

          try {
            // 处理响应数据
            if (response && response.data && response.data.products && Array.isArray(response.data.products)) {
              console.log('使用 response.data.products，长度:', response.data.products.length);

              // 将 API 返回的数据转换为前端需要的格式
              items = response.data.products.map(item => {
                // 处理价格字段，确保它们是数字
                let price = 0;
                let rentPrice = 0;

                // 尝试将价格转换为数字
                if (item.price !== undefined && item.price !== null) {
                  price = parseFloat(item.price);
                  if (isNaN(price)) price = 0;
                }

                if (item.rentPrice !== undefined && item.rentPrice !== null) {
                  rentPrice = parseFloat(item.rentPrice);
                  if (isNaN(rentPrice)) rentPrice = 0;
                }

                // 处理生产日期
                let manufactureDate = '未知';
                if (item.manufactureDate) {
                  try {
                    // 如果是带有时区信息的日期字符串，先提取日期部分
                    let dateStr = item.manufactureDate;
                    if (typeof dateStr === 'string' && dateStr.includes('T')) {
                      dateStr = dateStr.split('T')[0];
                    }

                    // 尝试将日期字符串转换为 Date 对象
                    const date = new Date(dateStr);
                    if (!isNaN(date.getTime())) {
                      // 如果是有效的日期，直接使用提取的日期部分
                      manufactureDate = dateStr;
                      console.log('store中原始生产日期:', item.manufactureDate, '格式化后:', manufactureDate);
                    }
                  } catch (e) {
                    console.error('处理生产日期出错:', e);
                  }
                }

                return {
                  id: item.id,
                  code: item.productCode || item.code || `PRD-${item.id}`,
                  name: item.name || '',
                  spec: item.spec || '未知规格',
                  categoryCode: item.categoryCode || '',
                  categoryId: item.categoryId,
                  categoryName: item.categoryName || '',
                  status: mapStatusFromApi(item.status),
                  price: price,
                  rentPrice: rentPrice,
                  manufactureDate: manufactureDate,
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  voltage: item.voltage || '',
                  capacity: item.capacity || '',
                  cycleCount: item.cycleCount || '',
                  chargeTime: item.chargeTime || ''
                };
              });

              total = response.data.totalItems || items.length;
            }
            // 如果响应是数组，直接使用
            else if (Array.isArray(response)) {
              console.log('响应是数组，长度:', response.length);
              items = response;
              total = response.length;
            }
            // 如果响应有 data 属性且是数组
            else if (response && response.data && Array.isArray(response.data)) {
              console.log('使用 response.data 数组，长度:', response.data.length);
              items = response.data;
              total = items.length;
            }

            console.log('处理后的商品列表:', items, '总数量:', total);

            if (items.length > 0) {
              // 如果是加载更多，则将新数据追加到现有数据中
              if (isLoadMore) {
                const combinedList = [...state.productList, ...items];
                console.log('合并后的列表长度:', combinedList.length);
                commit('SET_PRODUCT_LIST', combinedList);
              } else {
                // 如果是首次加载或刷新，直接替换现有数据
                console.log('设置新列表，长度:', items.length);
                commit('SET_PRODUCT_LIST', items);
              }

              // 返回包含列表和总数量的对象
              resolve({ list: items, total });
            } else {
              console.warn('商品列表数据格式不正确或为空:', response);

              // 如果不是加载更多，则清空列表
              if (!isLoadMore) {
                commit('SET_PRODUCT_LIST', []);
              }

              resolve({ list: [], total: 0 });
            }
          } catch (error) {
            console.error('处理商品列表数据时出错:', error);
            // 如果处理出错，尝试直接使用原始数据
            if (response) {
              if (response.data && response.data.products && Array.isArray(response.data.products)) {
                items = response.data.products;
                total = response.data.totalItems || items.length;
                commit('SET_PRODUCT_LIST', items);
                resolve({ list: items, total });
              } else if (Array.isArray(response)) {
                items = response;
                total = response.length;
                commit('SET_PRODUCT_LIST', items);
                resolve({ list: items, total });
              } else {
                commit('SET_PRODUCT_LIST', []);
                resolve({ list: [], total: 0 });
              }
            }
          }
        })
        .catch(error => {
          console.error('获取商品列表失败', error);
          reject(error);
        });
    });
  },

  // 获取商品分类列表
  getCategoryList({ commit }, params = {}) {
    console.log('获取商品分类列表', params);

    return new Promise((resolve, reject) => {
      productAPI.getProductCategories(params)
        .then(response => {
          console.log('获取商品分类列表响应:', response);

          let categories = [];

          if (response && response.data && Array.isArray(response.data)) {
            categories = response.data;
          } else if (Array.isArray(response)) {
            categories = response;
          }

          commit('SET_CATEGORY_LIST', categories);
          resolve(response);
        })
        .catch(error => {
          console.error('获取商品分类列表失败', error);
          reject(error);
        });
    });
  },

  // 获取商品规格列表
  getSpecList({ commit, dispatch }) {
    return dispatch('getProductSpecs');
  },

  // 获取商品规格列表 (新方法，用于商品进销存管理)
  getProductSpecs({ commit, rootGetters }) {
    return new Promise((resolve, reject) => {
      // 检查用户是否已登录
      const isLoggedIn = rootGetters['user/isLoggedIn'];
      if (!isLoggedIn) {
        console.log('用户未登录，使用默认商品规格列表');

        // 使用默认数据
        const defaultSpecList = [
          {
            id: 1,
            name: '48V20Ah',
            categoryId: 1,
            spec: '48V20Ah',
            price: 1500,
            rentPrice: 50
          },
          {
            id: 2,
            name: '60V20Ah',
            categoryId: 1,
            spec: '60V20Ah',
            price: 1800,
            rentPrice: 60
          },
          {
            id: 3,
            name: '72V20Ah',
            categoryId: 1,
            spec: '72V20Ah',
            price: 2100,
            rentPrice: 70
          },
          {
            id: 4,
            name: '48V30Ah',
            categoryId: 1,
            spec: '48V30Ah',
            price: 1700,
            rentPrice: 55
          }
        ];

        commit('SET_SPEC_LIST', defaultSpecList);
        return resolve(defaultSpecList);
      }

      // 调用真实API获取商品规格列表
      productAPI.getProductSpecs()
        .then(response => {
          console.log('获取商品规格列表响应:', response);

          // 处理响应数据
          let specList = [];

          if (response && response.code === 0 && Array.isArray(response.data)) {
            specList = response.data;
          } else if (Array.isArray(response)) {
            specList = response;
          } else if (response && Array.isArray(response.data)) {
            specList = response.data;
          } else {
            console.warn('商品规格数据格式不正确:', response);

            // 使用模拟数据
            specList = [
              {
                id: 1,
                name: '48V20Ah',
                categoryId: 1,
                spec: '48V20Ah',
                price: 1500,
                rentPrice: 50
              },
              {
                id: 2,
                name: '60V20Ah',
                categoryId: 1,
                spec: '60V20Ah',
                price: 1800,
                rentPrice: 60
              },
              {
                id: 3,
                name: '72V20Ah',
                categoryId: 1,
                spec: '72V20Ah',
                price: 2100,
                rentPrice: 70
              },
              {
                id: 4,
                name: '48V30Ah',
                categoryId: 1,
                spec: '48V30Ah',
                price: 1700,
                rentPrice: 55
              }
            ];
          }

          commit('SET_SPEC_LIST', specList);
          resolve(specList);
        })
        .catch(error => {
          console.error('获取商品规格列表失败', error);

          // 出错时使用模拟数据
          const mockData = [
            {
              id: 1,
              name: '48V20Ah',
              categoryId: 1,
              spec: '48V20Ah',
              price: 1500,
              rentPrice: 50
            },
            {
              id: 2,
              name: '60V20Ah',
              categoryId: 1,
              spec: '60V20Ah',
              price: 1800,
              rentPrice: 60
            },
            {
              id: 3,
              name: '72V20Ah',
              categoryId: 1,
              spec: '72V20Ah',
              price: 2100,
              rentPrice: 70
            },
            {
              id: 4,
              name: '48V30Ah',
              categoryId: 1,
              spec: '48V30Ah',
              price: 1700,
              rentPrice: 55
            }
          ];

          commit('SET_SPEC_LIST', mockData);
          resolve(mockData);
        });
    });
  },

  // 根据ID获取商品详情
  getProductDetail({ commit }, id) {
    console.log('开始获取商品详情, ID:', id);

    // 检查 ID 是否有效
    if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
      console.error('无效的商品 ID:', id);
      return Promise.reject(new Error('无效的商品 ID'));
    }

    commit('SET_DETAIL_LOADING', true);

    return new Promise((resolve, reject) => {
      productAPI.getProductDetail(parseInt(id))
        .then(response => {
          console.log('获取商品详情响应:', response);

          // 防止 response 为 undefined
          if (!response) {
            throw new Error('获取商品详情失败，返回数据为空');
          }

          // 处理响应数据
          let productData = null;

          if (response.code === 0 && response.data) {
            productData = response.data;
          } else if (response.data) {
            // 兼容直接返回数据的情况
            productData = response.data;
          } else if (typeof response === 'object' && response !== null) {
            // 兼容直接返回对象的情况
            productData = response;
          }

          // 检查返回的数据是否有效
          if (!productData || !productData.id) {
            console.warn('商品详情数据无效:', productData);
            throw new Error('获取商品详情失败，返回数据无效');
          }

          // 添加额外的详情信息
          const storeInfo = {
            id: 1,
            name: '北京海淀门店',
            address: '北京市海淀区中关村大街1号',
            phone: '010-12345678',
            businessHours: '09:00-18:00',
            latitude: 39.9789,
            longitude: 116.3088
          };

          // 确保 productData 是一个对象
          productData = typeof productData === 'object' && productData !== null ? productData : {};

          const productWithDetails = {
            ...productData,
            storeInfo,
            hardware: {
              voltage: productData.voltage || 0,
              current: 5.2,
              temperature: 25,
              cycles: productData.cycleCount || 0,
              health: productData.healthPercentage || 95
            }
          };

          commit('SET_CURRENT_PRODUCT', productWithDetails);
          commit('SET_DETAIL_LOADING', false);
          resolve(productWithDetails);
        })
        .catch(error => {
          console.error('获取商品详情失败', error);
          commit('SET_DETAIL_LOADING', false);
          reject(error);
        });
    });
  },

  // 保存商品（支持 IFormFile 方式）
  async saveProductWithFiles({ commit, state }, formData) {
    console.log('调用 saveProductWithFiles action');

    try {
      // 检查是否为编辑模式（通过 FormData 中的 ID 判断）
      const productId = formData.get('id');
      const isEdit = productId && parseInt(productId) > 0;
      let result;

      if (isEdit) {
        // 更新已有商品
        console.log('更新商品（FormData），ID:', productId);
        result = await productAPI.updateProductWithFiles(productId, formData);
      } else {
        // 创建新商品
        console.log('创建新商品（FormData）');
        result = await productAPI.createProductWithFiles(formData);
      }

      console.log('商品保存成功（FormData）:', result);
      return result;
    } catch (error) {
      console.error('保存商品失败（FormData）:', error);
      throw error;
    }
  },

  // 保存商品 (新增或更新)
  async saveProduct({ commit, state }, productData) {
    console.log('调用 saveProduct action，数据:', productData);

    try {
      const isEdit = productData.id > 0;
      let result;

      if (isEdit) {
        // 更新已有商品
        console.log('更新商品，ID:', productData.id);

        // 调用API更新商品
        const response = await productAPI.updateProduct(productData.id, productData);
        console.log('更新商品响应:', response);

        if (response.code === 0 && response.data) {
          const updatedProduct = response.data;

          // 查找当前列表中是否存在该商品
          const productIndex = state.productList.findIndex(item => item.id === updatedProduct.id);

          if (productIndex !== -1) {
            // 如果存在，更新列表中的商品
            const newList = [...state.productList];
            newList[productIndex] = updatedProduct;
            commit('SET_PRODUCT_LIST', newList);
          } else {
            // 如果不存在，添加到列表中
            commit('SET_PRODUCT_LIST', [...state.productList, updatedProduct]);
          }

          // 更新当前商品
          if (state.currentProduct && state.currentProduct.id === updatedProduct.id) {
            commit('SET_CURRENT_PRODUCT', updatedProduct);
          }

          result = updatedProduct;
        } else {
          throw new Error(response.message || '更新商品失败');
        }
      } else {
        // 新增商品
        console.log('新增商品');

        // 调用API创建商品
        const response = await productAPI.createProduct(productData);
        console.log('创建商品响应:', response);

        if (response.code === 0 && response.data) {
          const newProduct = response.data;

          // 更新state
          commit('SET_PRODUCT_LIST', [...state.productList, newProduct]);

          result = newProduct;
        } else {
          throw new Error(response.message || '创建商品失败');
        }
      }

      return result;
    } catch (error) {
      console.error('保存商品失败:', error);
      throw new Error('保存商品失败: ' + (error.message || '未知错误'));
    }
  },

  // 获取商品分类列表（兼容电池API）
  getProductCategories({ commit, dispatch }, params = {}) {
    return dispatch('getCategoryList', params);
  },

  // 删除商品
  deleteProduct({ commit, state }, productId) {
    return new Promise((resolve, reject) => {
      productAPI.deleteProduct(productId)
        .then(response => {
          console.log('删除商品响应:', response);

          // 从列表中移除已删除的商品
          const updatedList = state.productList.filter(item => item.id !== productId);
          commit('SET_PRODUCT_LIST', updatedList);

          resolve(response);
        })
        .catch(error => {
          console.error('删除商品失败', error);
          reject(error);
        });
    });
  }
}

const getters = {
  // 获取商品总数
  productCount: state => state.productList.length,

  // 根据状态获取商品数量
  getProductCountByStatus: state => status => {
    return state.productList.filter(product => product.status === status).length;
  },

  // 根据ID获取商品
  getProductById: state => id => {
    return state.productList.find(product => product.id === id);
  },

  // 兼容电池模块的getters
  // 获取可用商品列表（兼容电池API）
  availableProducts: state => state.productList.filter(item => item.status === 'Available' || item.status === '可用' || item.status === 1),

  // 根据规格获取商品列表（兼容电池API）
  getProductsBySpec: state => spec => state.productList.filter(item => item.spec === spec || item.model === spec),

  // 获取商品规格列表（兼容电池API）
  specList: state => state.specList,

  // 获取当前选中的商品（兼容电池API）
  currentProduct: state => state.currentProduct,

  // 获取商品详情加载状态（兼容电池API）
  detailLoading: state => state.detailLoading,

  // 获取商品类别列表（兼容电池API）
  categoryList: state => state.categoryList,

  // 获取当前类别商品列表（兼容电池API）
  categoryProducts: state => state.categoryProducts,

  // 获取类别商品列表加载状态（兼容电池API）
  categoryLoading: state => state.categoryLoading,

  // 获取类别商品列表总数（兼容电池API）
  categoryProductTotal: state => state.categoryProductTotal,

  // 获取类别商品列表当前页（兼容电池API）
  categoryProductPage: state => state.categoryProductPage,

  // 获取类别商品列表总页数（兼容电池API）
  categoryProductTotalPages: state => state.categoryProductTotalPages,

  // 根据类别编码获取类别信息（兼容电池API）
  getCategoryByCode: state => code => state.categoryList.find(item => item.code === code),

  // 兼容电池模块的别名getters
  batteryList: state => state.productList,
  availableBatteries: state => state.productList.filter(item => item.status === 'Available' || item.status === '可用' || item.status === 1),
  getBatteriesBySpec: state => spec => state.productList.filter(item => item.spec === spec || item.model === spec),
  currentBattery: state => state.currentProduct,
  categoryBatteries: state => state.categoryProducts,
  categoryBatteryTotal: state => state.categoryProductTotal,
  categoryBatteryPage: state => state.categoryProductPage,
  categoryBatteryTotalPages: state => state.categoryProductTotalPages
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
