<template>
  <view class="battery-list-container" :style="{'padding-bottom': '100rpx'}">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search
        v-model="searchKeyword"
        placeholder="搜索电池型号、规格"
        :show-action="false"
        @search="onSearch"
      ></u-search>
    </view>

    <!-- 电池列表 -->
    <scroll-view
      scroll-y
      class="battery-list"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshList"
    >
      <!-- 空状态 -->
      <view class="empty-tip" v-if="filteredBatteries.length === 0 && !loading">
        <image src="/static/battery-default.svg" mode="aspectFit" class="empty-icon"></image>
        <text>暂无电池数据</text>
      </view>

      <!-- 电池列表项 -->
      <view 
        class="battery-item"
        v-for="battery in filteredBatteries"
        :key="battery.id"
        @tap="navigateToDetail(battery.id)"
      >
        <!-- 状态标签 -->
        <view class="status-tag" :class="getBatteryStatusClass(battery.status)">
          {{ getBatteryStatusText(battery.status) }}
        </view>

        <view class="battery-content">
          <!-- 左侧图片 -->
          <image 
            class="battery-image" 
            src="/static/battery-default.svg"
            mode="aspectFit"
          ></image>

          <!-- 右侧信息 -->
          <view class="battery-info">
            <view class="battery-code">{{ battery.code }}</view>
            <view class="battery-spec">规格：{{ battery.spec }}</view>
            <view class="battery-price">
              <text class="label">租金：</text>
              <text class="price">¥{{ battery.rentPrice }}/天</text>
              <text class="label ml-20">售价：</text>
              <text class="price">¥{{ battery.price }}</text>
            </view>
            <view class="battery-life">
              <text class="label">剩余寿命：</text>
              <text class="value">{{ battery.remainingLife }}个月</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" v-if="loading">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>

      <!-- 无更多数据 -->
      <view class="no-more" v-if="noMore && filteredBatteries.length > 0">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  data() {
    return {
      searchKeyword: '',
      page: 1,
      pageSize: 10,
      loading: false,
      refreshing: false,
      noMore: false
    }
  },

  computed: {
    ...mapState('battery', ['batteryList']),

    // 根据筛选条件和搜索关键词过滤电池列表
    filteredBatteries() {
      let result = [...this.batteryList]

      // 应用搜索关键词
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(battery => 
          battery.code.toLowerCase().includes(keyword) ||
          battery.spec.toLowerCase().includes(keyword)
        )
      }

      return result
    }
  },

  onLoad() {
    this.initData();
    // 页面加载时显示tabBar
    uni.showTabBar();
  },

  onShow() {
    // 每次页面显示时都确保tabBar显示
    uni.showTabBar();
  },

  methods: {
    ...mapActions('battery', ['getBatteryList']),

    // 初始化数据
    async initData() {
      this.loading = true;
      try {
        await this.getBatteryList();
      } catch (error) {
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
      this.loading = false;
    },

    // 搜索处理
    onSearch() {
      this.page = 1;
      this.noMore = false;
      this.refreshList();
    },

    // 刷新列表
    async refreshList() {
      this.refreshing = true;
      await this.initData();
      this.refreshing = false;
      uni.stopPullDownRefresh();
    },

    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return;
      this.page++;
      this.getBatteryList();
    },

    // 获取电池状态样式类
    getBatteryStatusClass(status) {
      const statusMap = {
        1: 'status-available',
        2: 'status-rented',
        3: 'status-sold',
        4: 'status-maintenance'
      }
      return statusMap[status] || ''
    },

    // 获取电池状态文本
    getBatteryStatusText(status) {
      const statusMap = {
        1: '可用',
        2: '已租赁',
        3: '已售出',
        4: '维修中'
      }
      return statusMap[status] || '未知'
    },

    // 跳转到详情页
    navigateToDetail(batteryId) {
      uni.navigateTo({
        url: `/pages/buy/battery/detail?id=${batteryId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.battery-list-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: env(safe-area-inset-bottom);
}

.search-bar {
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.battery-list {
  height: calc(100vh - 120rpx);
  padding-bottom: 100rpx;
}

.battery-item {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  
  .status-tag {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 4rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #fff;
    z-index: 1;
    
    &.status-available {
      background-color: #2979ff;
    }
    &.status-rented {
      background-color: #ff9500;
    }
    &.status-sold {
      background-color: #8e8e93;
    }
    &.status-maintenance {
      background-color: #ff3b30;
    }
  }
}

.battery-content {
  display: flex;
  padding: 30rpx;
  
  .battery-image {
    width: 200rpx;
    height: 200rpx;
    margin-right: 30rpx;
  }
  
  .battery-info {
    flex: 1;
    
    .battery-code {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 15rpx;
    }
    
    .battery-spec {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 15rpx;
    }
    
    .battery-price {
      margin-bottom: 15rpx;
      
      .label {
        font-size: 26rpx;
        color: #999;
      }
      
      .price {
        font-size: 30rpx;
        color: #ff3b30;
        font-weight: bold;
      }
      
      .ml-20 {
        margin-left: 20rpx;
      }
    }
    
    .battery-life {
      font-size: 26rpx;
      
      .label {
        color: #999;
      }
      
      .value {
        color: #666;
      }
    }
  }
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

.loading-more, .no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
  }
}
</style>