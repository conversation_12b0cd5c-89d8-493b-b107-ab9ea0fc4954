<template>
  <view class="page-wrapper">
    <!-- 页面内容插槽 -->
    <slot></slot>

    <!-- 全局 Toast 组件 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 全局 Loading 组件 -->
    <custom-loading-page ref="uLoading" :isLoadingProp="isLoading" :loadingContent="loadingMessage"></custom-loading-page>
  </view>
</template>

<script>
import CustomLoadingPage from './custom-loading-page.vue';

export default {
  name: 'PageWrapper',
  components: {
    CustomLoadingPage
  },
  props: {
    // 是否显示加载中
    pageLoadingProp: {
      type: Boolean,
      default: false
    },
    // 加载提示文本
    pageLoadingTextProp: {
      type: String,
      default: '加载中...'
    },
    // 兼容旧的属性名
    loading: {
      type: Boolean,
      default: false
    },
    // 兼容旧的属性名
    loadingText: {
      type: String,
      default: '加载中...'
    }
  },
  data() {
    return {
      // 内部状态，避免与 props 冲突
      pageLoading: false,
      pageLoadingText: '加载中...'
    }
  },
  computed: {
    // 计算属性，用于传递给自定义加载页面组件
    isLoading() {
      return this.pageLoadingProp || this.loading || this.pageLoading;
    },
    // 计算属性，用于传递给自定义加载页面组件
    loadingMessage() {
      return this.pageLoadingTextProp || this.loadingText || this.pageLoadingText;
    }
  },
  watch: {
    // 监听 props 的变化
    pageLoadingProp: {
      immediate: true,
      handler(val) {
        this.pageLoading = val;
      }
    },
    pageLoadingTextProp: {
      immediate: true,
      handler(val) {
        this.pageLoadingText = val;
      }
    }
  },
  methods: {
    // 显示加载
    showLoading(text) {
      this.pageLoading = true;
      if (text) {
        this.pageLoadingText = text;
      }
      // 调用自定义加载页面组件的方法
      if (this.$refs.uLoading) {
        this.$refs.uLoading.showLoadingPage();
      }
    },
    // 隐藏加载
    hideLoading() {
      this.pageLoading = false;
      // 调用自定义加载页面组件的方法
      if (this.$refs.uLoading) {
        this.$refs.uLoading.hideLoadingPage();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-wrapper {
  width: 100%;
  min-height: 100vh;
  position: relative;
}
</style>
