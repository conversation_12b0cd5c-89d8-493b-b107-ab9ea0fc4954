import request from '@/utils/request'

/**
 * 商品图片管理 API
 */
export default {
  /**
   * 获取商品的所有图片
   * @param {number} productId 商品ID
   * @param {boolean} includeDeleted 是否包含已删除的图片
   * @returns {Promise} Promise对象
   */
  getProductImages(productId, includeDeleted = false) {
    return request.get(`/api/productimage/product/${productId}`, {
      params: { includeDeleted }
    });
  },

  /**
   * 根据类型获取商品图片
   * @param {number} productId 商品ID
   * @param {string} imageType 图片类型
   * @param {boolean} includeDeleted 是否包含已删除的图片
   * @returns {Promise} Promise对象
   */
  getProductImagesByType(productId, imageType, includeDeleted = false) {
    return request.get(`/api/productimage/product/${productId}/type/${imageType}`, {
      params: { includeDeleted }
    });
  },

  /**
   * 获取商品主图
   * @param {number} productId 商品ID
   * @returns {Promise} Promise对象
   */
  getMainImage(productId) {
    return request.get(`/api/productimage/product/${productId}/main`);
  },

  /**
   * 设置主图
   * @param {number} productId 商品ID
   * @param {number} imageId 图片ID
   * @returns {Promise} Promise对象
   */
  setMainImage(productId, imageId) {
    return request.put(`/api/productimage/product/${productId}/main/${imageId}`);
  },

  /**
   * 更新图片信息
   * @param {number} imageId 图片ID
   * @param {Object} data 更新数据
   * @returns {Promise} Promise对象
   */
  updateProductImage(imageId, data) {
    return request.put(`/api/productimage/${imageId}`, data);
  },

  /**
   * 删除图片
   * @param {number} imageId 图片ID
   * @param {boolean} hardDelete 是否硬删除
   * @returns {Promise} Promise对象
   */
  deleteProductImage(imageId, hardDelete = false) {
    return request.delete(`/api/productimage/${imageId}`, {
      params: { hardDelete }
    });
  },

  /**
   * 更新图片排序
   * @param {Object} imageOrders 图片ID和排序的对象
   * @returns {Promise} Promise对象
   */
  updateImageOrders(imageOrders) {
    return request.put('/api/productimage/sort', { imageOrders });
  },

  /**
   * 删除商品的所有图片
   * @param {number} productId 商品ID
   * @param {boolean} hardDelete 是否硬删除
   * @returns {Promise} Promise对象
   */
  deleteAllProductImages(productId, hardDelete = false) {
    return request.delete(`/api/productimage/product/${productId}/all`, {
      params: { hardDelete }
    });
  },

  /**
   * 获取所有图片类型
   * @returns {Promise} Promise对象
   */
  getImageTypes() {
    return request.get('/api/productimage/types');
  }
}
