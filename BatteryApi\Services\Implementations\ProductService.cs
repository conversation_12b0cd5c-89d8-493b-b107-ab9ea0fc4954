using SqlSugar;
using BatteryApi.Models;
using BatteryApi.DTOs.Product;
using BatteryApi.DTOs.Common;
using BatteryApi.Services.Interfaces;

namespace BatteryApi.Services.Implementations;

/// <summary>
/// 商品服务实现
/// </summary>
public class ProductService : IProductService
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<ProductService> _logger;
    private readonly IWebHostEnvironment _environment;

    public ProductService(ISqlSugarClient db, ILogger<ProductService> logger, IWebHostEnvironment environment)
    {
        _db = db;
        _logger = logger;
        _environment = environment;
    }

    /// <summary>
    /// 创建商品
    /// </summary>
    public async Task<ProductDto> CreateProductAsync(CreateProductRequest request)
    {
        try
        {
            Console.WriteLine($"🛍️ 开始创建商品: {request.Name}");

            // 检查商品是否重复
            var isDuplicate = await CheckProductDuplicateAsync(request.CategoryId, request.Spec);
            if (isDuplicate)
            {
                throw new Exception($"商品规格 '{request.Spec}' 在该分类下已存在");
            }

            // 生成商品编号
            var productCode = await GenerateProductCodeAsync();

            // 创建商品实体
            var product = new Product
            {
                ProductCode = productCode,
                Name = request.Name,
                Spec = request.Spec,
                Status = request.Status,
                Price = request.Price,
                RentPrice = request.RentPrice,
                ManufactureDate = request.ManufactureDate ?? DateTime.Now,
                Lifespan = request.Lifespan,
                Description = request.Description,
                CategoryId = request.CategoryId,
                CategoryCode = request.CategoryCode,
                CategoryName = request.CategoryName,
                Voltage = decimal.TryParse(request.Voltage, out var voltage) ? voltage : null,
                Capacity = decimal.TryParse(request.Capacity, out var capacity) ? capacity : null,
                CycleCount = request.CycleCount,
                ChargeTime = request.ChargeTime,
                BatteryId = request.BatteryId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 插入商品
            var newId = await _db.Insertable(product).ExecuteReturnIdentityAsync();
            Console.WriteLine($"🛍️ 商品创建成功，ID: {newId}");

            // 处理图片
            if (request.MainImagesFiles?.Any() == true)
            {
                Console.WriteLine($"🖼️ 使用 mainImagesFiles IFormFile 方式处理 {request.MainImagesFiles.Count} 张图片");
                await ProcessProductImagesFromFilesAsync(newId, request.MainImagesFiles, "main");
            }
            else if (request.MainImages?.Any() == true)
            {
                Console.WriteLine($"🖼️ 使用 JSON 方式处理 {request.MainImages.Count} 张图片");
                await ProcessProductImagesAsync(newId, request.MainImages);
            }

            // 处理服务
            if (request.Services?.Any() == true)
            {
                await ProcessProductServicesAsync(newId, request.Services);
            }

            // 处理安装费用
            if (request.InstallationFees?.Any() == true)
            {
                await ProcessProductInstallationFeesAsync(newId, request.InstallationFees);
            }

            // 获取完整的商品信息返回
            return await GetProductByIdAsync(newId) ?? throw new Exception("创建商品后获取详情失败");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建商品失败");
            throw;
        }
    }

    /// <summary>
    /// 更新商品
    /// </summary>
    public async Task<ProductDto> UpdateProductAsync(int id, UpdateProductRequest request)
    {
        try
        {
            Console.WriteLine($"🛍️ 开始更新商品，ID: {id}");

            // 检查商品是否存在
            var existingProduct = await _db.Queryable<Product>().FirstAsync(x => x.Id == id);
            if (existingProduct == null)
            {
                throw new Exception("商品不存在");
            }

            // 检查商品是否重复（排除当前商品）
            var isDuplicate = await CheckProductDuplicateAsync(request.CategoryId, request.Spec, id);
            if (isDuplicate)
            {
                throw new Exception($"商品规格 '{request.Spec}' 在该分类下已存在");
            }

            // 更新商品信息
            existingProduct.Name = request.Name;
            existingProduct.Spec = request.Spec;
            existingProduct.Status = request.Status;
            existingProduct.Price = request.Price;
            existingProduct.RentPrice = request.RentPrice;
            existingProduct.ManufactureDate = request.ManufactureDate ?? existingProduct.ManufactureDate;
            existingProduct.Lifespan = request.Lifespan;
            existingProduct.Description = request.Description;
            existingProduct.CategoryId = request.CategoryId;
            existingProduct.CategoryCode = request.CategoryCode;
            existingProduct.CategoryName = request.CategoryName;
            existingProduct.Voltage = decimal.TryParse(request.Voltage, out var voltage) ? voltage : null;
            existingProduct.Capacity = decimal.TryParse(request.Capacity, out var capacity) ? capacity : null;
            existingProduct.CycleCount = request.CycleCount;
            existingProduct.ChargeTime = request.ChargeTime;
            existingProduct.BatteryId = request.BatteryId;
            existingProduct.UpdatedAt = DateTime.Now;

            // 更新商品
            await _db.Updateable(existingProduct).ExecuteCommandAsync();
            Console.WriteLine($"🛍️ 商品更新成功，ID: {id}");

            // 处理图片
            if (request.MainImagesFiles?.Any() == true)
            {
                Console.WriteLine($"🖼️ 使用 mainImagesFiles IFormFile 方式处理 {request.MainImagesFiles.Count} 张图片");
                await ProcessProductImagesFromFilesAsync(id, request.MainImagesFiles, "main");
            }
            else if (request.MainImages?.Any() == true)
            {
                Console.WriteLine($"🖼️ 使用 JSON 方式处理 {request.MainImages.Count} 张图片");
                await ProcessProductImagesAsync(id, request.MainImages);
            }

            // 处理服务
            if (request.Services?.Any() == true)
            {
                await ProcessProductServicesAsync(id, request.Services);
            }

            // 处理安装费用
            if (request.InstallationFees?.Any() == true)
            {
                await ProcessProductInstallationFeesAsync(id, request.InstallationFees);
            }

            // 获取完整的商品信息返回
            return await GetProductByIdAsync(id) ?? throw new Exception("更新商品后获取详情失败");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新商品失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取商品
    /// </summary>
    public async Task<ProductDto?> GetProductByIdAsync(int id)
    {
        try
        {
            var product = await _db.Queryable<Product>()
                .Includes(x => x.Category)
                .Includes(x => x.Images)
                .Includes(x => x.Services)
                .Includes(x => x.InstallationFees)
                .FirstAsync(x => x.Id == id);

            if (product == null) return null;

            return MapToDto(product);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品详情失败");
            throw;
        }
    }

    /// <summary>
    /// 获取商品列表
    /// </summary>
    public async Task<PagedResult<ProductDto>> GetProductsAsync(int page, int pageSize, int? categoryId = null, string? keyword = null, string? status = null)
    {
        try
        {
            var query = _db.Queryable<Product>()
                .Includes(x => x.Category);

            // 分类筛选
            if (categoryId.HasValue && categoryId.Value > 0)
            {
                query = query.Where(x => x.CategoryId == categoryId.Value);
            }

            // 关键词搜索
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where(x => x.Name.Contains(keyword) || x.Spec.Contains(keyword) || x.ProductCode.Contains(keyword));
            }

            // 状态筛选
            if (!string.IsNullOrWhiteSpace(status))
            {
                query = query.Where(x => x.Status == status);
            }

            // 获取总数
            var total = await query.CountAsync();

            // 分页查询
            var products = await query
                .OrderByDescending(x => x.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var productDtos = products.Select(MapToDto).ToList();

            return new PagedResult<ProductDto>
            {
                Items = productDtos,
                Total = total,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)total / pageSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取商品列表失败");
            throw;
        }
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    public async Task<bool> DeleteProductAsync(int id)
    {
        try
        {
            Console.WriteLine($"🛍️ 开始删除商品，ID: {id}");

            // 检查商品是否存在
            var product = await _db.Queryable<Product>().FirstAsync(x => x.Id == id);
            if (product == null)
            {
                throw new Exception("商品不存在");
            }

            // 删除关联的图片、服务、安装费用
            await _db.Deleteable<ProductImage>().Where(x => x.ProductId == id).ExecuteCommandAsync();
            await _db.Deleteable<Models.ProductService>().Where(x => x.ProductId == id).ExecuteCommandAsync();
            await _db.Deleteable<ProductInstallationFee>().Where(x => x.ProductId == id).ExecuteCommandAsync();

            // 删除商品
            await _db.Deleteable<Product>().Where(x => x.Id == id).ExecuteCommandAsync();

            Console.WriteLine($"🛍️ 商品删除成功，ID: {id}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除商品失败");
            throw;
        }
    }

    /// <summary>
    /// 生成商品编号
    /// </summary>
    private async Task<string> GenerateProductCodeAsync()
    {
        var today = DateTime.Now.ToString("yyyyMMdd");
        var prefix = $"PRD{today}";
        
        // 查找今天已有的最大序号
        var maxCode = await _db.Queryable<Product>()
            .Where(x => x.ProductCode.StartsWith(prefix))
            .OrderByDescending(x => x.ProductCode)
            .Select(x => x.ProductCode)
            .FirstAsync();

        int sequence = 1;
        if (!string.IsNullOrEmpty(maxCode) && maxCode.Length >= prefix.Length + 3)
        {
            var sequenceStr = maxCode.Substring(prefix.Length);
            if (int.TryParse(sequenceStr, out int lastSequence))
            {
                sequence = lastSequence + 1;
            }
        }

        return $"{prefix}{sequence:D3}";
    }

    /// <summary>
    /// 映射到DTO
    /// </summary>
    private ProductDto MapToDto(Product product)
    {
        return new ProductDto
        {
            Id = product.Id,
            ProductCode = product.ProductCode,
            Name = product.Name,
            Spec = product.Spec,
            Status = product.Status,
            Price = product.Price,
            RentPrice = product.RentPrice,
            ManufactureDate = product.ManufactureDate,
            Lifespan = product.Lifespan,
            Description = product.Description,
            Notes = product.Notes,
            CategoryId = product.CategoryId,
            CategoryCode = product.CategoryCode,
            CategoryName = product.CategoryName,
            Voltage = product.Voltage?.ToString(),
            Capacity = product.Capacity?.ToString(),
            CycleCount = product.CycleCount,
            ChargeTime = product.ChargeTime,
            BatteryId = product.BatteryId,
            BatterySerialNumber = null, // Battery实体已移除，如需要可通过BatteryId查询
            Images = product.Images?.Select(img => new ProductImageDto
            {
                Id = img.Id,
                ProductId = img.ProductId,
                Url = img.ImageUrl,
                RelativePath = img.ImageUrl, // 使用 ImageUrl 作为相对路径
                Name = img.FileName,
                Size = img.FileSize,
                ImageType = img.ImageType,
                IsMain = img.IsMain,
                Sort = img.SortOrder,
                CreatedAt = img.CreatedAt
            }).ToList() ?? new List<ProductImageDto>(),
            Services = product.Services?.Select(svc => new ProductServiceDto
            {
                Id = svc.Id,
                ProductId = svc.ProductId,
                Name = svc.Name,
                Description = svc.Description,
                IsEnabled = svc.IsEnabled,
                Sort = svc.Sort,
                CreatedAt = svc.CreatedAt
            }).ToList() ?? new List<ProductServiceDto>(),
            InstallationFees = product.InstallationFees?.Select(fee => new ProductInstallationFeeDto
            {
                Id = fee.Id,
                ProductId = fee.ProductId,
                Name = fee.Name,
                Price = fee.Price,
                Description = fee.Description,
                IsEnabled = fee.IsEnabled,
                Sort = fee.Sort,
                CreatedAt = fee.CreatedAt
            }).ToList() ?? new List<ProductInstallationFeeDto>(),
            CreatedAt = product.CreatedAt,
            UpdatedAt = product.UpdatedAt
        };
    }

    /// <summary>
    /// 检查商品是否重复
    /// </summary>
    public async Task<bool> CheckProductDuplicateAsync(int categoryId, string spec, int? excludeId = null)
    {
        var query = _db.Queryable<Product>()
            .Where(x => x.CategoryId == categoryId && x.Spec == spec);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// 处理商品图片（JSON方式）
    /// </summary>
    public async Task ProcessProductImagesAsync(int productId, List<ProductImageRequest> images)
    {
        try
        {
            Console.WriteLine($"🖼️ 开始处理商品图片，商品ID: {productId}, 图片数量: {images.Count}");

            foreach (var imageRequest in images)
            {
                if (imageRequest.IsNew && !string.IsNullOrEmpty(imageRequest.TempPath))
                {
                    // 新图片，需要保存文件并创建记录
                    var savedPath = await SaveImageFileAsync(imageRequest.TempPath, productId);

                    var productImage = new ProductImage
                    {
                        ProductId = productId,
                        ImageUrl = savedPath.url,
                        FileName = imageRequest.Name,
                        FileSize = imageRequest.Size,
                        ImageType = imageRequest.ImageType,
                        IsMain = imageRequest.IsMain,
                        SortOrder = 0,
                        CreatedAt = DateTime.Now
                    };

                    await _db.Insertable(productImage).ExecuteCommandAsync();
                    Console.WriteLine($"🖼️ 新图片保存成功: {imageRequest.Name}");
                }
                else if (!imageRequest.IsNew && imageRequest.Id > 0)
                {
                    // 更新现有图片的主图状态
                    await _db.Updateable<ProductImage>()
                        .SetColumns(x => x.IsMain == imageRequest.IsMain)
                        .Where(x => x.Id == imageRequest.Id)
                        .ExecuteCommandAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理商品图片失败");
            throw;
        }
    }

    /// <summary>
    /// 处理商品图片（文件方式）
    /// </summary>
    public async Task ProcessProductImagesFromFilesAsync(int productId, List<IFormFile> files, string imageType)
    {
        try
        {
            Console.WriteLine($"🖼️ 开始处理商品图片文件，商品ID: {productId}, 文件数量: {files.Count}");

            for (int i = 0; i < files.Count; i++)
            {
                var file = files[i];
                var savedPath = await SaveImageFileAsync(file, productId);

                var productImage = new ProductImage
                {
                    ProductId = productId,
                    ImageUrl = savedPath.url,
                    FileName = file.FileName,
                    FileSize = file.Length,
                    ImageType = imageType,
                    IsMain = i == 0, // 第一张图片设为主图
                    SortOrder = i,
                    CreatedAt = DateTime.Now
                };

                await _db.Insertable(productImage).ExecuteCommandAsync();
                Console.WriteLine($"🖼️ 图片文件保存成功: {file.FileName}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理商品图片文件失败");
            throw;
        }
    }

    /// <summary>
    /// 处理商品服务
    /// </summary>
    public async Task ProcessProductServicesAsync(int productId, List<ProductServiceRequest> services)
    {
        try
        {
            Console.WriteLine($"🔧 开始处理商品服务，商品ID: {productId}, 服务数量: {services.Count}");

            // 删除现有服务
            await _db.Deleteable<Models.ProductService>().Where(x => x.ProductId == productId).ExecuteCommandAsync();

            // 添加新服务
            foreach (var serviceRequest in services)
            {
                if (!string.IsNullOrWhiteSpace(serviceRequest.Name))
                {
                    var productService = new Models.ProductService
                    {
                        ProductId = productId,
                        Name = serviceRequest.Name,
                        Description = serviceRequest.Description,
                        IsEnabled = serviceRequest.IsEnabled,
                        Sort = serviceRequest.Sort,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    await _db.Insertable(productService).ExecuteCommandAsync();
                    Console.WriteLine($"🔧 服务保存成功: {serviceRequest.Name}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理商品服务失败");
            throw;
        }
    }

    /// <summary>
    /// 处理商品安装费用
    /// </summary>
    public async Task ProcessProductInstallationFeesAsync(int productId, List<ProductInstallationFeeRequest> installationFees)
    {
        try
        {
            Console.WriteLine($"💰 开始处理商品安装费用，商品ID: {productId}, 费用数量: {installationFees.Count}");

            // 删除现有安装费用
            await _db.Deleteable<ProductInstallationFee>().Where(x => x.ProductId == productId).ExecuteCommandAsync();

            // 添加新安装费用
            foreach (var feeRequest in installationFees)
            {
                if (!string.IsNullOrWhiteSpace(feeRequest.Name))
                {
                    var installationFee = new ProductInstallationFee
                    {
                        ProductId = productId,
                        Name = feeRequest.Name,
                        Price = feeRequest.Price,
                        Description = feeRequest.Description,
                        IsEnabled = feeRequest.IsEnabled,
                        Sort = feeRequest.Sort,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    await _db.Insertable(installationFee).ExecuteCommandAsync();
                    Console.WriteLine($"💰 安装费用保存成功: {feeRequest.Name} - {feeRequest.Price}元");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理商品安装费用失败");
            throw;
        }
    }

    /// <summary>
    /// 保存图片文件（从临时路径）
    /// </summary>
    private async Task<(string url, string relativePath)> SaveImageFileAsync(string tempPath, int productId)
    {
        try
        {
            var uploadsPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads", "products", productId.ToString());
            Directory.CreateDirectory(uploadsPath);

            var fileName = Path.GetFileName(tempPath);
            var newFileName = $"{Guid.NewGuid()}{Path.GetExtension(fileName)}";
            var filePath = Path.Combine(uploadsPath, newFileName);

            // 复制文件
            File.Copy(tempPath, filePath, true);

            var relativePath = $"/uploads/products/{productId}/{newFileName}";
            var url = $"{relativePath}";

            return (url, relativePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存图片文件失败");
            throw;
        }
    }

    /// <summary>
    /// 保存图片文件（从IFormFile）
    /// </summary>
    private async Task<(string url, string relativePath)> SaveImageFileAsync(IFormFile file, int productId)
    {
        try
        {
            var uploadsPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads", "products", productId.ToString());
            Directory.CreateDirectory(uploadsPath);

            var newFileName = $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
            var filePath = Path.Combine(uploadsPath, newFileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            var relativePath = $"/uploads/products/{productId}/{newFileName}";
            var url = $"{relativePath}";

            return (url, relativePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存图片文件失败");
            throw;
        }
    }
}
