using SqlSugar;
using BatteryApi.Models;

namespace BatteryApi.Services.Base
{
    /// <summary>
    /// 数据库迁移服务
    /// </summary>
    public class DatabaseMigrationService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DatabaseMigrationService> _logger;

        public DatabaseMigrationService(ISqlSugarClient db, ILogger<DatabaseMigrationService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 执行所有数据库迁移
        /// </summary>
        public async Task ExecuteAllMigrationsAsync()
        {
            try
            {
                _logger.LogInformation("开始执行数据库迁移...");

                // 1. 添加用户验证表的ValidDate字段
                await AddValidDateToUserVerificationsAsync();

                // 2. 确保ProductImages表存在
                await EnsureProductImagesTableAsync();

                // 3. 清理旧的Battery相关表（在Product系统稳定后执行）
                await CleanupBatteryTablesAsync();

                _logger.LogInformation("数据库迁移执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库迁移执行失败");
                throw;
            }
        }

        /// <summary>
        /// 添加用户验证表的ValidDate字段
        /// </summary>
        private async Task AddValidDateToUserVerificationsAsync()
        {
            try
            {
                // 检查字段是否已存在
                var columns = _db.DbMaintenance.GetColumnInfosByTableName("UserVerifications");
                var validDateExists = columns.Any(c => c.DbColumnName.Equals("ValidDate", StringComparison.OrdinalIgnoreCase));

                if (!validDateExists)
                {
                    // 使用SqlSugar添加字段
                    _db.DbMaintenance.AddColumn("UserVerifications", new DbColumnInfo
                    {
                        DbColumnName = "ValidDate",
                        DataType = "datetime2",
                        IsNullable = true,
                        ColumnDescription = "证件有效期"
                    });

                    _logger.LogInformation("ValidDate字段已添加到UserVerifications表");
                }
                else
                {
                    _logger.LogInformation("ValidDate字段已存在于UserVerifications表");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加ValidDate字段失败");
                throw;
            }
        }



        /// <summary>
        /// 确保ProductImages表存在
        /// </summary>
        private async Task EnsureProductImagesTableAsync()
        {
            try
            {
                // 检查表是否存在
                var tableExists = _db.DbMaintenance.IsAnyTable("ProductImages");

                if (!tableExists)
                {
                    // 使用SqlSugar创建表
                    _db.CodeFirst.InitTables<ProductImage>();
                    _logger.LogInformation("ProductImages表已创建");

                    // 创建索引
                    await CreateProductImagesIndexesAsync();
                }
                else
                {
                    _logger.LogInformation("ProductImages表已存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建ProductImages表失败");
                throw;
            }
        }

        /// <summary>
        /// 创建ProductImages表的索引
        /// </summary>
        private async Task CreateProductImagesIndexesAsync()
        {
            try
            {
                // 创建ProductId索引
                if (!_db.DbMaintenance.IsAnyIndex("IX_ProductImages_ProductId"))
                {
                    _db.DbMaintenance.CreateIndex("ProductImages", new string[] { "ProductId" }, "IX_ProductImages_ProductId", false);
                }

                // 创建ImageType索引
                if (!_db.DbMaintenance.IsAnyIndex("IX_ProductImages_ImageType"))
                {
                    _db.DbMaintenance.CreateIndex("ProductImages", new string[] { "ImageType" }, "IX_ProductImages_ImageType", false);
                }

                // 创建IsMain索引
                if (!_db.DbMaintenance.IsAnyIndex("IX_ProductImages_IsMain"))
                {
                    _db.DbMaintenance.CreateIndex("ProductImages", new string[] { "IsMain" }, "IX_ProductImages_IsMain", false);
                }

                // 创建IsDeleted索引
                if (!_db.DbMaintenance.IsAnyIndex("IX_ProductImages_IsDeleted"))
                {
                    _db.DbMaintenance.CreateIndex("ProductImages", new string[] { "IsDeleted" }, "IX_ProductImages_IsDeleted", false);
                }

                _logger.LogInformation("ProductImages表索引创建完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建ProductImages表索引失败");
                // 索引创建失败不应该阻止应用启动
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        public async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                await _db.Queryable<SystemSetting>().Take(1).ToListAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据库版本信息
        /// </summary>
        public async Task<string> GetDatabaseVersionAsync()
        {
            try
            {
                // 使用SqlSugar的数据库维护功能获取数据库信息
                var dbType = _db.CurrentConnectionConfig.DbType;
                var connectionString = _db.CurrentConnectionConfig.ConnectionString;

                // 简单返回数据库类型信息，避免使用原生SQL
                return $"Database Type: {dbType}, Connection: {connectionString.Substring(0, Math.Min(50, connectionString.Length))}...";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取数据库版本失败");
                return "Unknown";
            }
        }

        /// <summary>
        /// 清理旧的Battery相关表
        /// </summary>
        private async Task CleanupBatteryTablesAsync()
        {
            try
            {
                _logger.LogInformation("开始清理旧的Battery相关表...");

                // 要删除的表列表（按依赖关系排序）
                var tablesToDrop = new[]
                {
                    "BatteryImages",
                    "BatteryServices",
                    "BatteryInstallationFees",
                    "BatterySpecs",
                    "Batteries"
                };

                foreach (var tableName in tablesToDrop)
                {
                    try
                    {
                        if (_db.DbMaintenance.IsAnyTable(tableName))
                        {
                            _db.DbMaintenance.DropTable(tableName);
                            _logger.LogInformation($"已删除表: {tableName}");
                        }
                        else
                        {
                            _logger.LogInformation($"表不存在，跳过: {tableName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"删除表 {tableName} 失败，可能存在外键约束或其他依赖");
                    }
                }

                _logger.LogInformation("Battery相关表清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理Battery相关表失败");
                // 不抛出异常，避免影响应用启动
            }
        }
    }
}
