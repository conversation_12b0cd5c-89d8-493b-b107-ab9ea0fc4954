/**
 * 应用配置文件
 * 集中管理所有API密钥和配置信息
 */

// 腾讯地图API配置
export const mapConfig = {
  // 腾讯地图API密钥
  key: 'NQ3BZ-RFKKN-AN3FP-SYHB4-RLAZF-6QFE6', // 请替换为您的实际API密钥

  // 腾讯地图API基础URL
  baseUrl: 'https://apis.map.qq.com',

  // 默认位置（盐城市中心）
  defaultLocation: {
    latitude: 33.3575,
    longitude: 120.1614,
    address: '江苏省盐城市亭湖区附近'
  },

  // 地图默认缩放级别
  defaultZoom: 14,

  // 地图标记图标
  markerIcon: '/static/marker.png'
};

// API请求配置
export const apiConfig = {
  // API基础URL
  baseUrl: process.env.NODE_ENV === 'development'
    ? 'http://localhost:5242'
    : 'https://api.example.com',

  // 请求超时时间（毫秒）
  timeout: 60000,

  // 是否显示加载提示
  showLoading: true,

  // 是否自动处理错误
  autoHandleError: true
};

// 应用全局配置
export const appConfig = {
  // 应用名称
  appName: '电池租赁系统',

  // 应用版本
  version: '1.0.0',

  // 是否开启调试模式
  debug: process.env.NODE_ENV === 'development',

  // 默认分页大小
  pageSize: 10
};

// 默认导出所有配置
export default {
  map: mapConfig,
  api: apiConfig,
  app: appConfig
};
