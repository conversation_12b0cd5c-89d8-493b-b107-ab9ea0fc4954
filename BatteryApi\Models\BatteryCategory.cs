using SqlSugar;

namespace BatteryApi.Models;

/// <summary>
/// 电池分类
/// </summary>
[SugarTable("BatteryCategories")]
public class BatteryCategory
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 分类编码
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 50)]
    public string Code { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    [SugarColumn(IsNullable = false, Length = 100)]
    public string Name { get; set; }

    /// <summary>
    /// 分类描述
    /// </summary>
    [SugarColumn(IsNullable = true, Length = 500)]
    public string Description { get; set; }

    /// <summary>
    /// 显示顺序
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int DisplayOrder { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的商品列表（导航属性）
    /// 注意：现在关联到 Product 而不是 Battery
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Product.CategoryId))]
    public List<Product> Products { get; set; } = new List<Product>();
}
