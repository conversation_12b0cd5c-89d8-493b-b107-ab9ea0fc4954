<template>
  <view class="detail-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" @tap="goBack">
        <text class="iconfont icon-back">←</text>
      </view>
      <view class="nav-title">电池详情</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 内容区 -->
    <scroll-view v-else scroll-y class="detail-scroll">
      <!-- 商品图片轮播 -->
      <swiper class="product-swiper" indicator-dots autoplay circular>
        <swiper-item>
          <image
            class="product-image"
            :src="getBatteryImage()"
            mode="aspectFill"
            @error="handleImageError"
            @load="handleImageLoad"
            :class="{'image-loaded': imageLoaded.main}"
          ></image>
          <view class="image-loading" v-if="!imageLoaded.main">加载中...</view>
        </swiper-item>
        <swiper-item>
          <image
            class="product-image"
            :src="getBatteryDetailImage()"
            mode="aspectFill"
            @error="handleImageError"
            @load="handleDetailImageLoad"
            :class="{'image-loaded': imageLoaded.detail}"
          ></image>
          <view class="image-loading" v-if="!imageLoaded.detail">加载中...</view>
        </swiper-item>
        <swiper-item>
          <image
            class="product-image"
            :src="getBatterySpecImage()"
            mode="aspectFill"
            @error="handleImageError"
            @load="handleSpecImageLoad"
            :class="{'image-loaded': imageLoaded.spec}"
          ></image>
          <view class="image-loading" v-if="!imageLoaded.spec">加载中...</view>
        </swiper-item>
      </swiper>

      <!-- 基本信息区 -->
      <view class="product-info">
        <view class="product-title">
          <text>{{battery.manufacturer || '高级锂电池'}} {{battery.model || battery.spec}}</text>
        </view>
        <view class="product-price-row">
          <view class="product-price">
            <text class="price-label">售价</text>
            <text class="price-value">{{formatAmount(battery.price)}}</text>
          </view>
          <view class="product-rent">
            <text class="rent-label">租金</text>
            <text class="rent-value">{{formatAmount(battery.rentPrice)}}<text class="rent-unit">/天</text></text>
          </view>
        </view>
        <view class="product-tags">
          <view class="tag status-tag" :class="getStatusClass(battery.status)">
            {{getStatusText(battery.status)}}
          </view>
          <view class="tag">官方正品</view>
          <view class="tag">包安装</view>
          <view class="tag">一年质保</view>
        </view>
      </view>

      <!-- 服务信息 -->
      <view class="service-info">
        <view class="service-title">服务信息</view>
        <view class="service-items">
          <!-- 从API获取的服务信息 -->
          <view class="service-item" v-for="(item, index) in serviceItems" :key="index">
            <text class="service-icon iconfont">✓</text>
            <text class="service-text">{{item.name}}</text>
          </view>

          <!-- 默认服务信息（当API未返回数据时显示） -->
          <template v-if="!serviceItems || serviceItems.length === 0">
            <view class="service-item">
              <text class="service-icon iconfont">✓</text>
              <text class="service-text">免费安装</text>
            </view>
            <view class="service-item">
              <text class="service-icon iconfont">✓</text>
              <text class="service-text">上门维修</text>
            </view>
            <view class="service-item">
              <text class="service-icon iconfont">✓</text>
              <text class="service-text">售后保障</text>
            </view>
            <view class="service-item">
              <text class="service-icon iconfont">✓</text>
              <text class="service-text">正品保证</text>
            </view>
          </template>
        </view>
      </view>

      <!-- 商品参数 -->
      <view class="specs-section">
        <view class="section-title">商品参数</view>
        <view class="specs-table">
          <view class="specs-row">
            <text class="specs-label">电池编号</text>
            <text class="specs-value">{{battery.code || '暂无'}}</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">品牌型号</text>
            <text class="specs-value">{{battery.manufacturer || '高级锂电池'}} {{battery.model || '暂无'}}</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">电池规格</text>
            <text class="specs-value">{{battery.spec || '暂无'}}</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">额定电压</text>
            <text class="specs-value">{{battery.voltage || getBatteryVoltage(battery.spec)}}V</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">额定容量</text>
            <text class="specs-value">{{battery.capacity || getBatteryCapacity(battery.spec)}}Ah</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">生产日期</text>
            <text class="specs-value">{{formatDate(battery.manufactureDate) || '暂无'}}</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">剩余寿命</text>
            <text class="specs-value">{{battery.remainingLife || 0}}个月</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">充放电周期</text>
            <text class="specs-value">{{battery.cycleCount || '≥800'}}次</text>
          </view>
          <view class="specs-row">
            <text class="specs-label">充电时间</text>
            <text class="specs-value">{{battery.chargeTime || '4-6'}}小时</text>
          </view>
          <view class="specs-row" v-if="battery.categoryName">
            <text class="specs-label">商品类别</text>
            <text class="specs-value">{{battery.categoryName}}</text>
          </view>
        </view>
      </view>

      <!-- 安装费用 -->
      <view class="installation-section" v-if="installationFees && installationFees.length > 0">
        <view class="section-title">安装费用</view>
        <view class="installation-content">
          <view class="installation-item" v-for="(fee, index) in installationFees" :key="index">
            <view class="installation-name">{{fee.name}}</view>
            <view class="installation-price">{{fee.price === 0 ? '免费' : formatAmount(fee.price)}}</view>
            <view class="installation-desc">{{fee.description}}</view>
          </view>
        </view>
      </view>

      <!-- 默认安装费用（当API未返回数据时显示） -->
      <view class="installation-section" v-else>
        <view class="section-title">安装费用</view>
        <view class="installation-content">
          <view class="installation-item">
            <view class="installation-name">基础安装费</view>
            <view class="installation-price">免费</view>
            <view class="installation-desc">包含电池安装、调试、测试等基础服务</view>
          </view>
          <view class="installation-item">
            <view class="installation-name">线路改造费</view>
            <view class="installation-price">{{formatAmount(50)}}</view>
            <view class="installation-desc">如需要线路改造，额外收取费用</view>
          </view>
          <view class="installation-item">
            <view class="installation-name">远程上门费</view>
            <view class="installation-price">{{formatAmount(30)}}</view>
            <view class="installation-desc">距离门店10公里以上的上门费用</view>
          </view>
        </view>
      </view>

      <!-- 商品详情 -->
      <view class="detail-section">
        <view class="section-title">商品详情</view>
        <view class="detail-content">
          <view class="detail-text" v-if="battery.description">
            {{battery.description}}
          </view>
          <view class="detail-text" v-else>
            本产品采用高品质锂电池芯，具有安全稳定、寿命长、充放电快速等特点。适用于各类电动车辆，提供持久稳定的电力输出。产品通过严格的质量检测，确保每一块电池都符合高标准要求。
          </view>
          <view class="detail-images">
            <image
              v-if="battery.imageUrl"
              class="detail-image"
              :src="battery.imageUrl"
              mode="widthFix"
              @error="handleImageError"
            ></image>
            <image
              v-else-if="battery.detailImageUrl"
              class="detail-image"
              :src="battery.detailImageUrl"
              mode="widthFix"
              @error="handleImageError"
            ></image>
            <image
              v-else
              class="detail-image"
              :src="getBatteryDetailImage()"
              mode="widthFix"
            ></image>

            <image
              v-if="battery.specImageUrl"
              class="detail-image"
              :src="battery.specImageUrl"
              mode="widthFix"
              @error="handleImageError"
            ></image>
            <image
              v-else
              class="detail-image"
              :src="getBatterySpecImage()"
              mode="widthFix"
            ></image>
          </view>
        </view>
      </view>

      <!-- 附近门店 -->
      <view class="stores-section">
        <view class="section-title">
          <text>有该电池库存的门店</text>
          <text class="section-subtitle">可到以下门店购买或租赁该电池</text>
        </view>
        <view class="stores-content">
          <view v-if="storeLoading" class="no-stores">
            <text class="no-stores-text">正在获取门店信息...</text>
          </view>
          <view v-else-if="nearbyStores.length === 0" class="no-stores">
            <text class="no-stores-text">暂无门店有该电池库存，请联系客服</text>
            <button class="contact-btn" @tap="contactCustomerService">联系客服</button>
          </view>
          <view v-else class="store-list">
            <view
              v-for="store in nearbyStores"
              :key="store.id"
              class="store-item"
              :class="{active: selectedStore && selectedStore.id === store.id}"
              @tap="selectStore(store)"
            >
              <view class="store-info">
                <view class="store-name">{{store.name}}</view>
                <view class="store-address">{{store.address}}</view>
                <view class="store-distance">
                  <text class="distance-label">距离：</text>
                  <text class="distance-value">{{formatDistance(store.distance)}}</text>
                </view>
                <view class="store-business-hours" v-if="store.businessHours">
                  <text class="hours-label">营业时间：</text>
                  <text class="hours-value">{{store.businessHours}}</text>
                </view>
              </view>
              <view class="store-actions">
                <view class="store-action" @tap.stop="callStore(store)">
                  <text class="action-icon iconfont">☎</text>
                  <text class="action-text">电话</text>
                </view>
                <view class="store-action" @tap.stop="navigateToStore(store)">
                  <text class="action-icon iconfont">→</text>
                  <text class="action-text">导航</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部留白 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="bottom-left">
        <view class="bottom-button service-button">
          <!-- <text class="button-icon iconfont">❤</text>
          <text class="button-text">客服</text> -->
        </view>
        <view class="bottom-button favorite-button">
          <!-- <text class="button-icon iconfont">★</text>
          <text class="button-text">收藏</text> -->
        </view>
      </view>
      <view class="bottom-right">
        <button class="action-button edit-button" v-if="isAdmin" @tap="editBattery">编辑电池</button>
        <button class="action-button rent-button" v-if="battery.status === 1 || battery.status === 'Available'" @tap="rentBattery">租赁电池</button>
        <button class="action-button buy-button" v-if="battery.status === 1 || battery.status === 'Available'" @tap="buyBattery">购买电池</button>
      </view>
    </view>

    <!-- 添加 uToast 组件 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 添加 uLoading-icon 组件 -->
    <u-loading-icon ref="uLoading" :show="false"></u-loading-icon>
  </view>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex'

export default {
  data() {
    return {
      batteryId: null,
      // 默认图片路径
      defaultImages: {
        battery: 'https://cdn.pixabay.com/photo/2014/04/02/10/47/car-battery-304435_640.png',
        detail: 'https://cdn.pixabay.com/photo/2017/08/10/08/00/battery-2619888_640.jpg',
        specs: 'https://cdn.pixabay.com/photo/2017/08/10/08/00/battery-2619889_640.jpg'
      },
      // 附近门店列表
      nearbyStores: [],
      // 选中的门店
      selectedStore: null,
      // 当前位置
      location: {
        latitude: 0,
        longitude: 0
      },
      // 图片加载状态
      imageLoaded: {
        main: false,
        detail: false,
        spec: false
      },
      // 门店加载状态
      storeLoading: false,
      // 安装费用列表
      installationFees: [],
      // 服务信息列表
      serviceItems: []
    }
  },
  computed: {
    ...mapState('battery', ['currentBattery', 'detailLoading']),
    ...mapState('user', ['userInfo']),
    battery() {
      if (!this.currentBattery || !this.currentBattery.id) {
        // 如果没有电池数据，返回默认数据
        return {
          id: this.batteryId,
          name: '高级锂电池',
          code: 'BT' + this.batteryId,
          spec: '60V20Ah',
          manufacturer: '高级锂电池',
          model: 'BT-' + this.batteryId,
          voltage: 60,
          capacity: 20,
          manufactureDate: new Date().toISOString().split('T')[0],
          remainingLife: 36,
          cycleCount: 800,
          chargeTime: '4-6',
          price: 1200,
          rentPrice: 100,
          description: '本产品采用高品质锂电池芯，具有安全稳定、寿命长、充放电快速等特点。适用于各类电动车辆，提供持久稳定的电力输出。产品通过严格的质量检测，确保每一块电池都符合高标准要求。'
        };
      }
      return this.currentBattery;
    },
    loading() {
      return this.detailLoading
    },
    // 判断当前用户是否为管理员
    isAdmin() {
      return this.userInfo && this.userInfo.role === 'admin';
    }
  },
  onLoad(options) {
    console.log('detail.vue onLoad', options)
    if (options.id) {
      this.batteryId = parseInt(options.id)
      this.loadBatteryDetail()
    }
    // 获取当前位置
    this.getLocation()
  },

  onShow() {
    // 页面显示时刷新门店列表
    if (this.batteryId && this.battery.id) {
      this.loadNearbyStores()
    }
  },
  methods: {
    ...mapActions('battery', ['getBatteryDetail']),
    ...mapMutations('battery', ['SET_DETAIL_LOADING']),

    // 格式化金额
    formatAmount(amount) {
      if (amount === undefined || amount === null) return '0.00';
      return '¥' + parseFloat(amount).toFixed(2);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '可用',
        2: '已租赁',
        3: '已售出',
        4: '维修中',
        'Available': '可用',
        'InUse': '已租赁',
        'Sold': '已售出',
        'Maintenance': '维修中'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态类名
    getStatusClass(status) {
      const statusMap = {
        1: 'status-available',
        2: 'status-rented',
        3: 'status-sold',
        4: 'status-maintenance',
        'Available': 'status-available',
        'InUse': 'status-rented',
        'Sold': 'status-sold',
        'Maintenance': 'status-maintenance'
      }
      return statusMap[status] || ''
    },

    // 获取电池电压
    getBatteryVoltage(spec) {
      if (!spec || typeof spec !== 'string') return '48'
      const match = spec.match(/^(\d+)V/)
      return match ? match[1] : '48'
    },

    // 获取电池容量
    getBatteryCapacity(spec) {
      if (!spec || typeof spec !== 'string') return '20'
      const match = spec.match(/(\d+)Ah$/)
      return match ? match[1] : '20'
    },

    // 获取电池主图
    getBatteryImage() {
      // 根据电池规格返回不同的图片
      const voltage = this.getBatteryVoltage(this.battery.spec)
      if (voltage === '48') {
        return 'https://pic.rmb.bdstatic.com/bjh/news/6792ab1e35c6a2a6cd10a5990bd033d0.png'
      } else if (voltage === '60') {
        return 'https://img0.baidu.com/it/u=600722015,3838115472&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=750'
      } else if (voltage === '72') {
        return 'https://wx3.sinaimg.cn/mw690/88e90961ly1hwvqdknjo4j20u0140tav.jpg'
      }
      return this.defaultImages.battery
    },

    // 获取电池详情图
    getBatteryDetailImage() {
      return this.defaultImages.detail
    },

    // 获取电池规格图
    getBatterySpecImage() {
      return this.defaultImages.specs
    },

    async loadBatteryDetail() {
      if (!this.batteryId) return;

      console.log('加载电池详情', this.batteryId)
      this.SET_DETAIL_LOADING(true);

      try {
        // 加载电池基本信息
        try {
          await this.getBatteryDetail(this.batteryId);
          console.log('电池详情加载成功', this.currentBattery);
        } catch (error) {
          console.error('电池基本信息加载失败', error);
          // 显示错误提示
          uni.showToast({
            title: '电池基本信息加载失败，使用默认数据',
            icon: 'none'
          });
        }

        // 加载电池服务信息
        await this.loadBatteryServices();

        // 加载电池安装费用
        await this.loadInstallationFees();

        this.SET_DETAIL_LOADING(false);

        // 加载附近门店
        this.loadNearbyStores();
      } catch (error) {
        console.error('电池详情加载失败', error)
        this.SET_DETAIL_LOADING(false);
        uni.showToast({
          title: error.message || '加载失败，请重试',
          icon: 'none'
        });
      }
    },

    // 加载电池服务信息
    async loadBatteryServices() {
      try {
        // 如果电池有服务信息，直接使用
        if (this.battery && this.battery.services && this.battery.services.length > 0) {
          this.serviceItems = this.battery.services;
          return;
        }

        // 使用默认服务信息
        this.serviceItems = [
          { id: 1, name: '免费安装', description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '上门维修', description: '提供上门维修服务' },
          { id: 3, name: '售后保障', description: '提供完善的售后保障服务' },
          { id: 4, name: '正品保证', description: '保证产品为原厂正品' }
        ];

        // 尝试调用API获取服务信息（如果API已实现）
        try {
          const response = await this.$api.battery.getBatteryServices(this.batteryId);
          console.log('获取电池服务信息响应:', response);

          if (response && response.code === 0 && response.data && response.data.length > 0) {
            this.serviceItems = response.data;
          }
        } catch (apiError) {
          console.log('API尚未实现，使用默认服务信息');
          // 使用默认服务信息，已在上面设置
        }
      } catch (error) {
        console.error('获取电池服务信息失败:', error);
        // 使用默认服务信息
        this.serviceItems = [
          { id: 1, name: '免费安装', description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '上门维修', description: '提供上门维修服务' },
          { id: 3, name: '售后保障', description: '提供完善的售后保障服务' },
          { id: 4, name: '正品保证', description: '保证产品为原厂正品' }
        ];
      }
    },

    // 加载安装费用
    async loadInstallationFees() {
      try {
        // 如果电池有安装费用信息，直接使用
        if (this.battery && this.battery.installationFees && this.battery.installationFees.length > 0) {
          this.installationFees = this.battery.installationFees;
          return;
        }

        // 使用默认安装费用
        this.installationFees = [
          { id: 1, name: '基础安装费', price: 0, description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '线路改造费', price: 50, description: '如需要线路改造，额外收取费用' },
          { id: 3, name: '远程上门费', price: 30, description: '距离门店10公里以上的上门费用' }
        ];

        // 尝试调用API获取安装费用信息（如果API已实现）
        try {
          const response = await this.$api.battery.getBatteryInstallationFees(this.batteryId);
          console.log('获取电池安装费用响应:', response);

          if (response && response.code === 0 && response.data && response.data.length > 0) {
            this.installationFees = response.data;
          }
        } catch (apiError) {
          console.log('API尚未实现，使用默认安装费用');
          // 使用默认安装费用，已在上面设置
        }
      } catch (error) {
        console.error('获取电池安装费用失败:', error);
        // 使用默认安装费用
        this.installationFees = [
          { id: 1, name: '基础安装费', price: 0, description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '线路改造费', price: 50, description: '如需要线路改造，额外收取费用' },
          { id: 3, name: '远程上门费', price: 30, description: '距离门店10公里以上的上门费用' }
        ];
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';

      // 如果是日期对象，转换为字符串
      if (dateString instanceof Date) {
        return dateString.toISOString().split('T')[0];
      }

      // 如果是ISO格式的日期字符串，提取日期部分
      if (typeof dateString === 'string' && dateString.includes('T')) {
        return dateString.split('T')[0];
      }

      return dateString;
    },

    // 获取当前位置
    getLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.location.latitude = res.latitude;
          this.location.longitude = res.longitude;
          console.log('获取当前位置成功', this.location);

          // 如果已经加载了电池详情，则加载附近门店
          if (this.battery && this.battery.id) {
            this.loadNearbyStores();
          }
        },
        fail: (err) => {
          console.error('获取当前位置失败', err);
          uni.showToast({
            title: '获取位置失败，将显示所有门店',
            icon: 'none'
          });

          // 即使获取位置失败，也加载门店列表
          if (this.battery && this.battery.id) {
            this.loadNearbyStores();
          }
        }
      });
    },

    // 加载附近门店
    async loadNearbyStores() {
      try {
        // 显示加载提示
        this.storeLoading = true;
        this.nearbyStores = [];

        // 确保有电池ID
        if (!this.battery || !this.battery.id) {
          console.warn('电池ID不存在，无法获取门店信息');
          this.storeLoading = false;
          return;
        }

        // 先获取用户位置
        if (!this.location.latitude || !this.location.longitude) {
          console.warn('用户位置不存在，将使用默认位置');
          // 使用默认位置（盐城市中心）
          this.location.latitude = 33.3575;
          this.location.longitude = 120.1614;
        }

        // 调用获取附近门店API，传递电池ID参数
        console.log('开始获取有当前电池库存的门店', {
          batteryId: this.battery.id,
          latitude: this.location.latitude,
          longitude: this.location.longitude
        });

        const response = await this.$api.store.getNearbyStores({
          batteryId: this.battery.id, // 使用电池ID过滤门店
          latitude: this.location.latitude,
          longitude: this.location.longitude,
          maxDistance: 10000 // 范围设置为10公里
        });

        console.log('获取有当前电池库存的门店成功', response);

        if (response && response.code === 0 && response.data) {
          // 处理门店数据
          this.nearbyStores = response.data;

          // 默认选中第一个门店
          if (this.nearbyStores.length > 0 && !this.selectedStore) {
            this.selectStore(this.nearbyStores[0]);
          }
        } else {
          this.nearbyStores = [];
          console.warn('返回的数据格式不正确', response);
        }

        // 完成加载
        this.storeLoading = false;
      } catch (error) {
        console.error('获取附近门店失败', error);
        this.nearbyStores = [];
        this.storeLoading = false;

        // 显示错误信息
        let errorMsg = '获取门店信息失败';
        if (error.error) {
          errorMsg += ': ' + error.error;
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });

        // 在生产环境中不使用模拟数据
        if (process.env.NODE_ENV !== 'production') {
          // 尝试使用模拟数据（仅在开发环境）
          console.log('尝试使用模拟数据');
          this.nearbyStores = [
            {
              id: 1,
              name: '测试门店1',
              address: '盐城市亭湖区解放南路100号',
              phone: '0515-12345678',
              latitude: this.location.latitude + 0.01,
              longitude: this.location.longitude + 0.01,
              businessHours: '9:00-18:00',
              distance: 1.5
            },
            {
              id: 2,
              name: '测试门店2',
              address: '盐城市亭湖区人民路200号',
              phone: '0515-87654321',
              latitude: this.location.latitude - 0.01,
              longitude: this.location.longitude - 0.01,
              businessHours: '8:30-20:00',
              distance: 2.3
            }
          ];

          // 选中第一个模拟门店
          if (this.nearbyStores.length > 0) {
            this.selectStore(this.nearbyStores[0]);
          }
        }
      }
    },

    // 联系客服
    contactCustomerService() {
      // 检查是否有客服电话
      const customerServicePhone = '0515-12345678'; // 替换为实际的客服电话

      uni.showModal({
        title: '联系客服',
        content: `您可以拨打客服电话 ${customerServicePhone} 咨询电池库存情况`,
        confirmText: '拨打电话',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: customerServicePhone,
              fail: (err) => {
                console.error('拨打电话失败', err);
                uni.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    // 选择门店
    selectStore(store) {
      this.selectedStore = store;
      console.log('选择门店', store);
    },

    // 拨打门店电话
    callStore(store) {
      if (!store || !store.phone) {
        uni.showToast({
          title: '暂无门店电话',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: store.phone,
        fail: (err) => {
          console.error('拨打电话失败', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 导航到门店
    navigateToStore(store) {
      if (!store || !store.latitude || !store.longitude) {
        uni.showToast({
          title: '暂无门店位置信息',
          icon: 'none'
        });
        return;
      }

      uni.openLocation({
        latitude: store.latitude,
        longitude: store.longitude,
        name: store.name,
        address: store.address,
        fail: (err) => {
          console.error('打开地图失败', err);
          uni.showToast({
            title: '打开地图失败',
            icon: 'none'
          });
        }
      });
    },

    // 计算两点之间的距离（单位：公里）
    calculateDistance(lat1, lon1, lat2, lon2) {
      if (!lat1 || !lon1 || !lat2 || !lon2) {
        return null;
      }

      const R = 6371; // 地球半径（公里）
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI/180);
    },

    // 格式化距离
    formatDistance(distance) {
      if (distance === null || distance === undefined) return '距离未知';

      // 如果距离小于1公里，显示为米
      if (distance < 1) {
        const meters = Math.round(distance * 1000);
        return `${meters} 米`;
      }

      // 如果距离大于等于1公里，显示为公里，保留一位小数
      return `${distance.toFixed(1)} 公里`;
    },

    // 处理图片加载错误
    handleImageError(e) {
      console.log('图片加载错误', e);
      // 将错误图片替换为默认图片
      e.target.src = 'https://cdn.pixabay.com/photo/2014/04/02/10/47/car-battery-304435_640.png';
    },

    // 处理主图片加载完成
    handleImageLoad() {
      console.log('主图片加载完成');
      this.imageLoaded.main = true;
    },

    // 处理详情图片加载完成
    handleDetailImageLoad() {
      console.log('详情图片加载完成');
      this.imageLoaded.detail = true;
    },

    // 处理规格图片加载完成
    handleSpecImageLoad() {
      console.log('规格图片加载完成');
      this.imageLoaded.spec = true;
    },

    goBack() {
      uni.navigateBack();
    },

    rentBattery() {
      // 检查是否选择了门店
      if (!this.selectedStore) {
        uni.showToast({
          title: '请选择安装门店',
          icon: 'none'
        });
        return;
      }

      // 检查用户是否已登录
      if (!this.$store.getters['user/isLoggedIn']) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 检查用户是否已实名认证
      if (!this.$store.getters['user/isAuthenticated']) {
        uni.showModal({
          title: '实名认证',
          content: '租赁电池需要先完成实名认证，是否前往认证？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/user/auth'
              });
            }
          }
        });
        return;
      }

      // 将电池信息和门店信息存储到本地
      try {
        uni.setStorageSync('selectedBattery', this.battery);
        uni.setStorageSync('selectedStore', this.selectedStore);
        uni.setStorageSync('orderType', 'rent');

        // 仅传递必要的参数
        uni.navigateTo({
          url: '../order/rental-rules',
          fail: (err) => {
            console.error('跳转到租赁规则页面失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (e) {
        console.error('存储数据失败:', e);
        uni.showToast({
          title: '系统错误，请重试',
          icon: 'none'
        });
      }
    },

    buyBattery() {
      // 检查是否选择了门店
      if (!this.selectedStore) {
        uni.showToast({
          title: '请选择安装门店',
          icon: 'none'
        });
        return;
      }

      // 检查用户是否已登录
      if (!this.$store.getters['user/isLoggedIn']) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 检查用户是否已实名认证
      if (!this.$store.getters['user/isAuthenticated']) {
        uni.showModal({
          title: '实名认证',
          content: '购买电池需要先完成实名认证，是否前往认证？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: '/pages/user/auth'
              });
            }
          }
        });
        return;
      }

      // 将电池信息和门店信息存储到本地
      try {
        uni.setStorageSync('selectedBattery', this.battery);
        uni.setStorageSync('selectedStore', this.selectedStore);
        uni.setStorageSync('orderType', 'buy');

        // 仅传递必要的参数
        uni.navigateTo({
          url: '../order/pay',
          fail: (err) => {
            console.error('跳转到支付页面失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (e) {
        console.error('存储数据失败:', e);
        uni.showToast({
          title: '系统错误，请重试',
          icon: 'none'
        });
      }
    },

    // 编辑电池
    editBattery() {
      if (!this.battery || !this.battery.id) {
        uni.showToast({
          title: '电池数据不完整，无法编辑',
          icon: 'none'
        });
        return;
      }

      // 跳转到编辑页面
      uni.navigateTo({
        url: `/pages/admin/battery/edit?id=${this.battery.id}`,
        fail: (err) => {
          console.error('跳转到编辑页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style>
/* 基础样式 */
.detail-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 90rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

/* 加载样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2979ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 滚动区域 */
.detail-scroll {
  flex: 1;

}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 28rpx;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10rpx 30rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  object-fit: contain;
  background-color: #fff;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-loaded {
  opacity: 1;
}

/* 基本信息区 */
.product-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.product-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-price {
  margin-right: 40rpx;
}

.price-label, .rent-label {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}

.price-value {
  font-size: 40rpx;
  color: #ff5722;
  font-weight: bold;
}

.rent-value {
  font-size: 32rpx;
  color: #ff9800;
  font-weight: bold;
}

.rent-unit {
  font-size: 24rpx;
  font-weight: normal;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  padding: 6rpx 16rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

.status-tag {
  color: #fff;
}

.status-available {
  background-color: #4caf50;
}

.status-rented {
  background-color: #2196f3;
}

.status-sold {
  background-color: #9e9e9e;
}

.status-maintenance {
  background-color: #ff9800;
}

/* 服务信息 */
.service-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.service-title, .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.service-title::before, .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}

.service-items {
  display: flex;
  flex-wrap: wrap;
}

.service-item {
  display: flex;
  align-items: center;
  width: 50%;
  margin-bottom: 20rpx;
}

.service-icon {
  color: #2979ff;
  font-size: 32rpx;
  margin-right: 10rpx;
}

.service-text {
  font-size: 28rpx;
  color: #666;
}

/* 商品参数 */
.specs-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.specs-table {
  width: 100%;
}

.specs-row {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.specs-label {
  width: 180rpx;
  color: #999;
  font-size: 28rpx;
}

.specs-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

/* 安装费用 */
.installation-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.installation-content {
  width: 100%;
}

.installation-item {
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.installation-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.installation-price {
  font-size: 32rpx;
  color: #ff5722;
  margin-bottom: 10rpx;
}

.installation-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

/* 商品详情 */
.detail-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-content {
  width: 100%;
}

.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
  text-align: justify;
}

.detail-images {
  width: 100%;
}

.detail-image {
  width: 100%;
  margin-bottom: 20rpx;
}

/* 附近门店 */
.stores-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-top: 8rpx;
}

.stores-content {
  width: 100%;
}

.no-stores {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-stores-text {
  font-size: 28rpx;
  color: #999;
}

.store-list {
  width: 100%;
}

.store-item {
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s;
}

.store-item.active {
  background-color: #e3f2fd;
  border-left: 4rpx solid #2979ff;
}

.store-info {
  flex: 1;
}

.store-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.store-distance {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.distance-label, .hours-label {
  color: #999;
}

.distance-value, .hours-value {
  color: #666;
}

.store-business-hours {
  font-size: 24rpx;
  color: #999;
}

.store-actions {
  display: flex;
  align-items: center;
}

.store-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 30rpx;
  padding: 10rpx;
}

.action-icon {
  font-size: 36rpx;
  color: #2979ff;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

/* 底部留白 */
.bottom-space {
  height: 120rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.bottom-left {
  display: flex;
  align-items: center;
}

.bottom-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 40rpx;
}

.button-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.button-text {
  font-size: 22rpx;
  color: #666;
}

.service-button .button-icon {
  color: #ff5722;
}

.favorite-button .button-icon {
  color: #ffb300;
}

.bottom-right {
  display: flex;
  align-items: center;
}

.action-button {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-left: 20rpx;
}

.rent-button {
  background-color: #e3f2fd;
  color: #2979ff;
  border: 1px solid #2979ff;
}

.buy-button {
  background-color: #2979ff;
  color: #fff;
}

.edit-button {
  background-color: #ff9800;
  color: #fff;
}

.no-stores {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.no-stores-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.contact-btn {
  background-color: #2979ff;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
  margin-top: 20rpx;
}
</style>