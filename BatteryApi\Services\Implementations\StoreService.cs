using BatteryApi.DTOs;
using BatteryApi.DTOs.Common;
using BatteryApi.DTOs.Store;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using SqlSugar;

namespace BatteryApi.Services.Implementations;

public class StoreService : IStoreService
{
    private readonly ISqlSugarClient _db;

    public StoreService(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<PagedResponse<StoreDto>> GetStoresAsync(StoreQueryParameters parameters)
    {
        try
        {
            // 记录请求参数
            Console.WriteLine($"GetStoresAsync called with parameters: Page={parameters.Page}, PageSize={parameters.PageSize}, " +
                             $"Status={parameters.Status}, Name={parameters.Name}, " +
                             $"Latitude={parameters.Latitude}, Longitude={parameters.Longitude}, MaxDistance={parameters.MaxDistance}");

            // 确保参数有效
            int page = Math.Max(1, parameters.Page);
            int pageSize = Math.Min(Math.Max(1, parameters.PageSize), 100);

            var query = _db.Queryable<Store>()
                .Where(s => !s.IsDeleted); // 只查询未删除的门店

            // 添加筛选条件
            if (!string.IsNullOrEmpty(parameters.Name))
            {
                query = query.Where(s => s.Name.Contains(parameters.Name));
            }

            if (!string.IsNullOrEmpty(parameters.Status))
            {
                query = query.Where(s => s.Status == parameters.Status);
            }

            // 添加联系人筛选条件
            if (!string.IsNullOrEmpty(parameters.Contact))
            {
                // 使用 Contact 字段进行查询
                query = query.Where(s => s.Contact != null && s.Contact.Contains(parameters.Contact));
            }

            // 添加电话筛选条件
            if (!string.IsNullOrEmpty(parameters.Phone))
            {
                query = query.Where(s => s.Phone.Contains(parameters.Phone));
            }

            // 如果提供了经纬度和最大距离，计算距离并筛选
            if (parameters.Latitude.HasValue && parameters.Longitude.HasValue && parameters.MaxDistance.HasValue)
            {
                var lat = parameters.Latitude.Value;
                var lng = parameters.Longitude.Value;
                var maxDistance = parameters.MaxDistance.Value;

                // 先获取所有门店
                var allStores = await query.ToListAsync();

                // 在内存中计算距离并筛选
                var storesWithDistance = new List<StoreDto>();

                foreach (var store in allStores)
                {
                    // 计算距离
                    double distance = CalculateDistance(store.Latitude, store.Longitude, lat, lng);

                    // 如果距离在范围内，添加到结果中
                    if (distance <= maxDistance)
                    {
                        var storeDto = MapToStoreDto(store);
                        storeDto.Distance = distance;
                        storesWithDistance.Add(storeDto);
                    }
                }

                // 按距离排序
                var orderedStores = storesWithDistance.OrderBy(s => s.Distance).ToList();

                // 分页
                var pagedStores = orderedStores
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PagedResponse<StoreDto>(pagedStores, orderedStores.Count, page, pageSize);
            }
            else
            {
                // 如果没有地理位置参数，直接分页查询
                var total = await query.CountAsync();
                var stores = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var storeDtos = stores.Select(s => MapToStoreDto(s)).ToList();

                return new PagedResponse<StoreDto>(storeDtos, total, page, pageSize);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"GetStoresAsync error: {ex.Message}");
            throw;
        }
    }

    public async Task<StoreDto> GetStoreByIdAsync(int id)
    {
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
        if (store == null)
        {
            throw new Exception($"Store with ID {id} not found");
        }

        // 使用MapToStoreDto方法，确保包含所有字段，包括软删除相关字段
        return MapToStoreDto(store);
    }

    public async Task<StoreDto> CreateStoreAsync(StoreCreateDto storeDto)
    {
        var store = new Store
        {
            Name = storeDto.Name,
            Address = storeDto.Address,
            Phone = storeDto.Phone,
            Contact = storeDto.Contact,
            Latitude = storeDto.Latitude,
            Longitude = storeDto.Longitude,
            Status = storeDto.Status,
            Description = storeDto.Description,
            BusinessHours = storeDto.BusinessHours,
            ImageUrl = storeDto.ImageUrl,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };

        await _db.Insertable(store).ExecuteCommandIdentityIntoEntityAsync();

        // 使用MapToStoreDto方法，确保包含所有字段，包括软删除相关字段
        return MapToStoreDto(store);
    }

    public async Task<StoreDto> UpdateStoreAsync(int id, StoreUpdateDto storeDto)
    {
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
        if (store == null)
        {
            throw new Exception($"Store with ID {id} not found");
        }

        store.Name = storeDto.Name;
        store.Address = storeDto.Address;
        store.Phone = storeDto.Phone;
        store.Contact = storeDto.Contact;
        store.Latitude = storeDto.Latitude;
        store.Longitude = storeDto.Longitude;
        store.Status = storeDto.Status;
        store.Description = storeDto.Description;
        store.BusinessHours = storeDto.BusinessHours;
        store.ImageUrl = storeDto.ImageUrl;
        store.UpdatedAt = DateTime.Now;

        await _db.Updateable(store).ExecuteCommandAsync();

        // 使用MapToStoreDto方法，确保包含所有字段，包括软删除相关字段
        return MapToStoreDto(store);
    }

    public async Task<bool> DeleteStoreAsync(int id)
    {
        // 检查门店是否存在
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
        if (store == null)
        {
            throw new Exception($"Store with ID {id} not found");
        }

        // 检查门店状态
        if (store.Status != "Inactive")
        {
            throw new Exception("只有已关闭状态的门店才能删除");
        }

        var result = await _db.Deleteable<Store>().Where(s => s.Id == id).ExecuteCommandAsync();
        return result > 0;
    }

    public async Task<StoreDto> SoftDeleteStoreAsync(int id, StoreSoftDeleteDto deleteDto, string deletedBy)
    {
        // 检查门店是否存在
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
        if (store == null)
        {
            throw new Exception($"Store with ID {id} not found");
        }

        // 检查门店状态
        if (store.Status != "Inactive")
        {
            throw new Exception("只有已关闭状态的门店才能删除");
        }

        // 设置软删除标记
        store.IsDeleted = true;
        store.DeletedAt = DateTime.UtcNow;
        store.DeleteReason = deleteDto.DeleteReason;
        store.DeletedBy = deletedBy;
        store.UpdatedAt = DateTime.UtcNow;

        // 更新数据库
        await _db.Updateable(store).ExecuteCommandAsync();

        // 返回更新后的门店信息
        return MapToStoreDto(store);
    }

    public async Task<bool> CloseAndSoftDeleteStoreAsync(int id, StoreSoftDeleteDto deleteDto, string deletedBy)
    {
        try
        {
            // 检查参数
            if (id <= 0)
            {
                throw new ArgumentException("门店ID无效", nameof(id));
            }

            if (deleteDto == null)
            {
                throw new ArgumentNullException(nameof(deleteDto), "删除数据不能为空");
            }

            if (string.IsNullOrWhiteSpace(deleteDto.DeleteReason))
            {
                throw new ArgumentException("删除原因不能为空", nameof(deleteDto));
            }

            // 检查门店是否存在
            var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
            if (store == null)
            {
                throw new Exception($"未找到ID为 {id} 的门店");
            }

            // 检查门店是否已被删除
            if (store.IsDeleted)
            {
                throw new Exception($"门店已被删除，无法再次删除");
            }

            // 如果门店状态不是已关闭，先关闭门店
            if (store.Status != "Inactive")
            {
                if (!deleteDto.CloseStore)
                {
                    throw new Exception("门店状态不是已关闭，需要设置 CloseStore 为 true 才能一键关闭并删除");
                }

                // 记录状态变更
                Console.WriteLine($"门店 {id} 状态从 {store.Status} 变更为 Inactive");
                store.Status = "Inactive";
            }

            // 设置软删除标记
            store.IsDeleted = true;
            store.DeletedAt = DateTime.UtcNow;
            store.DeleteReason = deleteDto.DeleteReason;
            store.DeletedBy = deletedBy;
            store.UpdatedAt = DateTime.UtcNow;

            // 更新数据库
            var result = await _db.Updateable(store).ExecuteCommandAsync();

            // 检查更新结果
            if (result <= 0)
            {
                throw new Exception("数据库更新失败，请检查数据库连接");
            }

            Console.WriteLine($"门店 {id} 已成功关闭并软删除，操作人: {deletedBy}");
            return true;
        }
        catch (Exception ex)
        {
            // 记录详细错误
            Console.Error.WriteLine($"关闭并软删除门店 {id} 失败: {ex.Message}");
            Console.Error.WriteLine($"错误详情: {ex}");

            // 重新抛出异常，保留原始错误信息
            throw;
        }
    }

    public async Task<StoreDto> RestoreStoreAsync(int id, StoreRestoreDto restoreDto, string restoredBy)
    {
        // 检查门店是否存在
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == id);
        if (store == null)
        {
            throw new Exception($"Store with ID {id} not found");
        }

        // 检查门店是否已被软删除
        if (!store.IsDeleted)
        {
            throw new Exception("门店未被删除，无需恢复");
        }

        // 恢复门店
        store.IsDeleted = false;
        store.DeletedAt = null;
        store.DeleteReason = null;
        store.DeletedBy = null;
        store.Status = restoreDto.Status; // 使用指定的状态
        store.UpdatedAt = DateTime.UtcNow;

        // 更新数据库
        await _db.Updateable(store).ExecuteCommandAsync();

        // 返回更新后的门店信息
        return MapToStoreDto(store);
    }

    public async Task<PagedResponse<StoreDto>> GetDeletedStoresAsync(int page = 1, int pageSize = 10)
    {
        // 确保参数有效
        page = Math.Max(1, page);
        pageSize = Math.Min(Math.Max(1, pageSize), 100);

        // 查询已删除的门店
        var query = _db.Queryable<Store>().Where(s => s.IsDeleted);

        // 获取总记录数
        var totalItems = await query.CountAsync();

        // 获取分页数据
        var stores = await query
            .OrderByDescending(s => s.DeletedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        // 转换为 DTO
        var storeDtos = stores.Select(MapToStoreDto).ToList();

        // 计算总页数
        var totalPages = (int)Math.Ceiling(totalItems / (double)pageSize);

        // 返回分页结果
        return new PagedResponse<StoreDto>(storeDtos, totalItems, page, pageSize);
    }

    public async Task<List<StoreInventoryDto>> GetStoreInventoryAsync(int storeId)
    {
        var inventory = await _db.Queryable<StoreInventory>()
            .Includes(i => i.Product)
            .Where(i => i.StoreId == storeId)
            .ToListAsync();

        return inventory.Select(i => new StoreInventoryDto
        {
            Id = i.Id,
            StoreId = i.StoreId,
            BatteryId = i.ProductId,
            BatteryModel = i.Product?.Name,
            BatteryManufacturer = i.Product?.Manufacturer,
            BatteryCapacity = i.Product != null ? (double)i.Product.Capacity : 0.0,
            BatteryVoltage = i.Product != null ? (double)i.Product.Voltage : 0.0,
            Quantity = i.Quantity,
            AvailableQuantity = i.AvailableQuantity,
            UpdatedAt = i.UpdatedAt
        }).ToList();
    }

    public async Task<StoreInventoryDto> UpdateStoreInventoryAsync(int storeId, StoreInventoryUpdateDto inventoryDto)
    {
        // 检查参数
        if (storeId <= 0)
        {
            throw new ArgumentException("门店ID无效", nameof(storeId));
        }

        if (inventoryDto == null)
        {
            throw new ArgumentNullException(nameof(inventoryDto), "库存更新数据不能为空");
        }

        if (inventoryDto.BatteryId <= 0)
        {
            throw new ArgumentException("商品ID无效", nameof(inventoryDto.BatteryId));
        }

        // 检查门店是否存在
        var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == storeId);
        if (store == null)
        {
            throw new Exception($"门店ID {storeId} 不存在");
        }

        // 检查商品是否存在
        var product = await _db.Queryable<Product>().FirstAsync(p => p.Id == inventoryDto.BatteryId);
        if (product == null)
        {
            throw new Exception($"商品ID {inventoryDto.BatteryId} 不存在");
        }

        // 查找库存记录
        var inventory = await _db.Queryable<StoreInventory>()
            .FirstAsync(i => i.StoreId == storeId && i.ProductId == inventoryDto.BatteryId);

        // 记录旧数量，用于历史记录
        int oldQuantity = 0;

        if (inventory == null)
        {
            // 如果不存在，创建新记录
            inventory = new StoreInventory
            {
                StoreId = storeId,
                ProductId = inventoryDto.BatteryId,
                Quantity = inventoryDto.Quantity,
                AvailableQuantity = inventoryDto.Quantity, // 确保可用数量与总数量一致
                UpdatedAt = DateTime.Now
            };
            await _db.Insertable(inventory).ExecuteCommandIdentityIntoEntityAsync();

            // 记录库存历史
            await RecordInventoryHistory(storeId, inventoryDto.BatteryId, oldQuantity, inventoryDto.Quantity, "Create");
        }
        else
        {
            // 如果存在，更新记录
            oldQuantity = inventory.Quantity;
            inventory.Quantity = inventoryDto.Quantity;
            inventory.AvailableQuantity = inventoryDto.Quantity; // 确保可用数量与总数量一致
            inventory.UpdatedAt = DateTime.Now;
            await _db.Updateable(inventory).ExecuteCommandAsync();

            // 记录库存历史
            await RecordInventoryHistory(storeId, inventoryDto.BatteryId, oldQuantity, inventoryDto.Quantity, "Update");
        }

        // 返回更新后的库存数据
        return new StoreInventoryDto
        {
            Id = inventory.Id,
            StoreId = inventory.StoreId,
            BatteryId = inventory.ProductId,
            BatteryModel = product.Name,
            BatteryManufacturer = product.Manufacturer,
            BatteryCapacity = product != null ? (double)product.Capacity : 0.0,
            BatteryVoltage = product != null ? (double)product.Voltage : 0.0,
            Quantity = inventory.Quantity,
            AvailableQuantity = inventory.AvailableQuantity,
            UpdatedAt = inventory.UpdatedAt
        };
    }

    // 记录库存历史
    private async Task RecordInventoryHistory(int storeId, int productId, int oldQuantity, int newQuantity, string operationType)
    {
        var history = new InventoryHistory
        {
            StoreId = storeId,
            ProductId = productId,
            OldQuantity = oldQuantity,
            NewQuantity = newQuantity,
            Quantity = newQuantity - oldQuantity,
            OperationType = operationType,
            OperatedBy = "system",
            OperationTime = DateTime.Now,
            Remark = $"{operationType} operation by system"
        };

        try
        {
            await _db.Insertable(history).ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            // 捕获历史记录插入错误，但不影响主流程
            Console.WriteLine($"插入库存历史记录失败，可能是数据库表结构不匹配: {ex.Message}");
            Console.WriteLine("请执行 Scripts/AddMissingColumnsToInventoryHistory.sql 脚本添加缺失的列");
        }
    }

    public async Task<List<StoreDto>> GetNearbyStoresAsync(double latitude, double longitude, double maxDistance, int? batteryId = null)
    {
        try
        {
            // 获取所有未删除的门店
            var query = _db.Queryable<Store>().Where(s => !s.IsDeleted);

            // 如果指定了电池ID，只返回有该电池库存的门店
            List<Store> allStores;
            if (batteryId.HasValue)
            {
                // 先获取有该电池库存的门店ID
                var storeIdsWithBattery = await _db.Queryable<StoreInventory>()
                    .Where(i => i.ProductId == batteryId.Value && i.AvailableQuantity > 0)
                    .Select(i => i.StoreId)
                    .ToListAsync();

                // 然后获取这些门店
                allStores = await _db.Queryable<Store>()
                    .Where(s => storeIdsWithBattery.Contains(s.Id))
                    .ToListAsync();
            }
            else
            {
                // 获取所有门店
                allStores = await query.ToListAsync();
            }

            // 在内存中计算距离并过滤
            var result = new List<StoreDto>();

            foreach (var store in allStores)
            {
                // 计算距离
                double distance = CalculateDistance(store.Latitude, store.Longitude, latitude, longitude);

                // 如果距离在范围内，添加到结果中
                if (distance <= maxDistance)
                {
                    // 使用MapToStoreDto方法，确保包含所有字段，包括软删除相关字段
                    var storeDto = MapToStoreDto(store);
                    storeDto.Distance = distance;
                    result.Add(storeDto);
                }
            }

            // 按距离排序
            return result.OrderBy(s => s.Distance).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"GetNearbyStoresAsync error: {ex.Message}");
            Console.WriteLine($"Exception details: {ex}");

            // 重新尝试不使用子查询的方式
            if (batteryId.HasValue)
            {
                try
                {
                    // 直接获取有该电池库存的门店ID
                    var storeIdsWithBattery = await _db.Queryable<StoreInventory>()
                        .Where(i => i.ProductId == batteryId.Value && i.AvailableQuantity > 0)
                        .Select(i => i.StoreId)
                        .ToListAsync();

                    // 获取这些未删除的门店
                    var filteredStores = await _db.Queryable<Store>()
                        .Where(s => storeIdsWithBattery.Contains(s.Id) && !s.IsDeleted)
                        .ToListAsync();

                    // 计算距离并过滤
                    var result = new List<StoreDto>();
                    foreach (var store in filteredStores)
                    {
                        double distance = CalculateDistance(store.Latitude, store.Longitude, latitude, longitude);
                        if (distance <= maxDistance)
                        {
                            var storeDto = MapToStoreDto(store);
                            storeDto.Distance = distance;
                            result.Add(storeDto);
                        }
                    }

                    // 按距离排序
                    return result.OrderBy(s => s.Distance).ToList();
                }
                catch (Exception fallbackEx)
                {
                    Console.WriteLine($"Fallback method also failed: {fallbackEx.Message}");
                    throw new Exception($"Failed to get nearby stores with battery ID {batteryId}: {ex.Message}", ex);
                }
            }

            throw new Exception($"Failed to get nearby stores: {ex.Message}", ex);
        }
    }

    // 计算两点之间的距离（米）
    private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        // 使用简化的距离计算公式
        return 111.111 * 1000 * Math.Sqrt(
            (lat1 - lat2) * (lat1 - lat2) +
            (lon1 - lon2) * (lon1 - lon2)
        );
    }

    // 将 Store 模型转换为 StoreDto
    private StoreDto MapToStoreDto(Store store)
    {
        return new StoreDto
        {
            Id = store.Id,
            Name = store.Name,
            Address = store.Address,
            Phone = store.Phone,
            Contact = store.Contact,
            Latitude = store.Latitude,
            Longitude = store.Longitude,
            Status = store.Status,
            Description = store.Description,
            BusinessHours = store.BusinessHours,
            ImageUrl = store.ImageUrl,
            CreatedAt = store.CreatedAt,
            UpdatedAt = store.UpdatedAt,
            // 软删除相关字段
            IsDeleted = store.IsDeleted,
            DeletedAt = store.DeletedAt,
            DeleteReason = store.DeleteReason,
            DeletedBy = store.DeletedBy
        };
    }
}
