/**
 * 认证API服务
 */
import request from '@/utils/request';

// API接口
const AuthAPI = {
  /**
   * 用户登录
   * @param {Object} data 登录信息
   * @returns {Promise} Promise对象
   */
  login(data) {
    console.log('发送登录请求');

    // 创建请求数据
    const requestData = {
      phoneNumber: data.username,
      password: data.password
    };

    console.log('登录请求数据:', requestData);

    // 使用 request.post 发送请求
    return request.post('/api/auth/login', requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log('登录成功响应:', response);
      return response;
    })
    .catch(error => {
      console.error('登录失败错误:', error);
      // 确保错误消息被正确传递
      if (error && error.message) {
        throw new Error(error.message);
      } else {
        throw error;
      }
    });
  },

  /**
   * 短信验证码登录
   * @param {Object} data 登录信息
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.code 验证码
   * @returns {Promise} Promise对象
   */
  loginWithSms(data) {
    console.log('发送短信登录请求');

    // 创建请求数据
    const requestData = {
      phoneNumber: data.phoneNumber,
      code: data.code
    };

    console.log('短信登录请求数据:', requestData);

    // 使用 request.post 发送请求
    return request.post('/api/auth/login/sms', requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log('短信登录成功响应:', response);
      return response;
    })
    .catch(error => {
      console.error('短信登录失败错误:', error);
      // 确保错误消息被正确传递
      if (error && error.message) {
        throw new Error(error.message);
      } else {
        throw error;
      }
    });
  },

  /**
   * 用户注册
   * @param {Object} data 注册信息
   * @returns {Promise} Promise对象
   */
  register(data) {
    return request.post('/api/auth/register', {
      username: data.username,
      phoneNumber: data.phone,
      password: data.password
    });
  },

  /**
   * 手机号注册
   * @param {Object} data 注册信息
   * @param {string} data.username 用户名
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.code 验证码
   * @param {string} data.password 密码
   * @returns {Promise} Promise对象
   */
  registerWithPhone(data) {
    console.log('发送手机号注册请求');

    // 创建请求数据
    const requestData = {
      username: data.username,
      phoneNumber: data.phoneNumber,
      code: data.code,
      password: data.password
    };

    console.log('手机号注册请求数据:', requestData);

    // 使用 request.post 发送请求
    return request.post('/api/auth/register/phone', requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log('手机号注册成功响应:', response);
      return response;
    })
    .catch(error => {
      console.error('手机号注册失败错误:', error);
      // 确保错误消息被正确传递
      if (error && error.message) {
        throw new Error(error.message);
      } else {
        throw error;
      }
    });
  },

  /**
   * 刷新令牌
   * @param {Object} data 刷新令牌信息
   * @returns {Promise} Promise对象
   */
  refreshToken(data) {
    // 检查刷新令牌是否存在
    if (!data.refreshToken) {
      console.error('刷新令牌不存在');
      return Promise.reject(new Error('刷新令牌不存在'));
    }

    // 确保请求体格式正确 - 根据后端API调整格式
    const requestData = {
      refreshToken: data.refreshToken
    };

    // 如果后端API需要原token，则添加
    if (data.token) {
      requestData.token = data.token;
    }

    console.log('Refresh token request data:', JSON.stringify(requestData));

    // 添加特殊选项，不显示错误提示，由调用者处理
    return request.post('/api/auth/refresh-token', requestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      showError: false,  // 不自动显示错误提示
      loading: false,    // 不显示加载中
      retry: false       // 不自动重试
    }).catch(error => {
      console.error('刷新令牌请求失败:', error);

      // 添加更多错误信息，帮助诊断
      if (error.statusCode) {
        console.error(`HTTP状态码: ${error.statusCode}`);
      }
      if (error.message) {
        console.error(`错误消息: ${error.message}`);
      }
      if (error.detailedMessage) {
        console.error(`详细错误: ${error.detailedMessage}`);
      }

      // 清除本地存储的令牌
      uni.removeStorageSync('token');
      uni.removeStorageSync('refreshToken');

      // 重新抛出错误，让调用者处理
      throw error;
    });
  },

  /**
   * 撤销令牌
   * @returns {Promise} Promise对象
   */
  revokeToken() {
    return request.post('/api/auth/revoke-token');
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} Promise对象
   */
  getCurrentUser() {
    return request.get('/api/auth/current-user');
  },

  /**
   * 更新用户在线状态
   * @param {Boolean} isOnline 是否在线
   * @returns {Promise} Promise对象
   */
  updateOnlineStatus(isOnline) {
    return request.post('/api/auth/online-status', isOnline);
  },

  /**
   * 提交实名认证（包含文件上传）
   * @param {Object} data 认证信息
   * @returns {Promise} Promise对象
   * @deprecated 使用分步上传方法代替
   */
  submitVerification(data) {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('realName', data.realName);
    formData.append('idCardNumber', data.idCardNumber);
    formData.append('idCardFront', data.idCardFront);
    formData.append('idCardBack', data.idCardBack);
    formData.append('faceImage', data.faceImage);

    return request.post('/api/auth/verification/submit', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 上传身份证正面照片
   * @param {File|Blob} file 文件对象
   * @returns {Promise} Promise对象
   */
  uploadIdCardFront(file) {
    // 创建FormData对象
    const formData = new FormData();

    // 确保文件有正确的文件名
    if (file instanceof Blob && !file.name) {
      // 如果是Blob对象且没有文件名，添加一个默认文件名
      const fileName = `idcard_front_${Date.now()}.jpg`;
      // 检查是否支持File构造函数
      if (typeof File !== 'undefined') {
        try {
          file = new File([file], fileName, { type: file.type || 'image/jpeg' });
        } catch (e) {
          console.error('创建File对象失败，使用Blob:', e);
          // 如果File构造函数不可用，添加属性到Blob对象
          const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
          newBlob.name = fileName;
          file = newBlob;
        }
      } else {
        // 如果File构造函数不可用，添加属性到Blob对象
        const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
        newBlob.name = fileName;
        file = newBlob;
      }
    }

    // 添加文件到FormData
    formData.append('file', file, file.name || `idcard_front_${Date.now()}.jpg`);

    // 使用upload方法而不是post方法
    return request.upload('/api/auth/verification/upload-idcard-front', {
      formData: formData,
      name: 'file',
      filePath: file
    });
  },

  /**
   * 上传身份证背面照片
   * @param {File|Blob} file 文件对象
   * @returns {Promise} Promise对象
   */
  uploadIdCardBack(file) {
    // 创建FormData对象
    const formData = new FormData();

    // 确保文件有正确的文件名
    if (file instanceof Blob && !file.name) {
      // 如果是Blob对象且没有文件名，添加一个默认文件名
      const fileName = `idcard_back_${Date.now()}.jpg`;
      // 检查是否支持File构造函数
      if (typeof File !== 'undefined') {
        try {
          file = new File([file], fileName, { type: file.type || 'image/jpeg' });
        } catch (e) {
          console.error('创建File对象失败，使用Blob:', e);
          // 如果File构造函数不可用，添加属性到Blob对象
          const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
          newBlob.name = fileName;
          file = newBlob;
        }
      } else {
        // 如果File构造函数不可用，添加属性到Blob对象
        const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
        newBlob.name = fileName;
        file = newBlob;
      }
    }

    // 添加文件到FormData
    formData.append('file', file, file.name || `idcard_back_${Date.now()}.jpg`);

    // 使用upload方法而不是post方法
    return request.upload('/api/auth/verification/upload-idcard-back', {
      formData: formData,
      name: 'file',
      filePath: file
    });
  },

  /**
   * 上传人脸照片
   * @param {File|Blob} file 文件对象
   * @returns {Promise} Promise对象
   */
  uploadFace(file) {
    // 创建FormData对象
    const formData = new FormData();

    // 确保文件有正确的文件名
    if (file instanceof Blob && !file.name) {
      // 如果是Blob对象且没有文件名，添加一个默认文件名
      const fileName = `face_${Date.now()}.jpg`;
      // 检查是否支持File构造函数
      if (typeof File !== 'undefined') {
        try {
          file = new File([file], fileName, { type: file.type || 'image/jpeg' });
        } catch (e) {
          console.error('创建File对象失败，使用Blob:', e);
          // 如果File构造函数不可用，添加属性到Blob对象
          const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
          newBlob.name = fileName;
          file = newBlob;
        }
      } else {
        // 如果File构造函数不可用，添加属性到Blob对象
        const newBlob = new Blob([file], { type: file.type || 'image/jpeg' });
        newBlob.name = fileName;
        file = newBlob;
      }
    }

    // 添加文件到FormData
    formData.append('file', file, file.name || `face_${Date.now()}.jpg`);

    // 使用upload方法而不是post方法
    return request.upload('/api/auth/verification/upload-face', {
      formData: formData,
      name: 'file',
      filePath: file
    });
  },

  /**
   * 检查用户是否已上传身份证正反面照片
   * @returns {Promise} Promise对象
   */
  checkIdCardPhotos() {
    return request.get('/api/auth/verification/check-idcard-photos');
  },

  /**
   * 提交实名认证信息（不包含文件上传）
   * @param {Object} data 认证信息
   * @param {string} data.realName 真实姓名
   * @param {string} data.idCardNumber 身份证号码
   * @param {string} data.validDate 身份证有效期
   * @returns {Promise} Promise对象
   */
  submitVerificationInfo(data) {
    return request.post('/api/auth/verification/submit-info', data, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**
   * 获取认证状态
   * @returns {Promise} Promise对象
   */
  getVerificationStatus() {
    return request.get('/api/auth/verification/status');
  },

  /**
   * 获取认证列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.pageSize 每页数量
   * @param {string} params.status 状态筛选
   * @returns {Promise} Promise对象
   */
  getVerifications(params) {
    return request.get('/api/auth/verification/list', { params });
  },

  /**
   * 获取认证详情
   * @param {number} id 认证ID
   * @returns {Promise} Promise对象
   */
  getVerificationDetail(id) {
    return request.get(`/api/auth/verification/${id}`);
  },

  /**
   * 审核认证
   * @param {number} id 认证ID
   * @param {Object} data 审核数据
   * @param {string} data.status 状态
   * @param {string} data.rejectReason 拒绝原因
   * @returns {Promise} Promise对象
   */
  verifyUser(id, data) {
    return request.post(`/api/auth/verification/verify/${id}`, data);
  },

  /**
   * 获取用户认证详情
   * @param {number} userId 用户ID
   * @returns {Promise} Promise对象
   */
  getUserVerification(userId) {
    return request.get(`/api/auth/verification/user/${userId}`);
  },

  /**
   * 获取认证统计
   * @returns {Promise} Promise对象
   */
  getVerificationCounts() {
    return request.get('/api/auth/verification/counts');
  },

  /**
   * 修改密码
   * @param {Object} data 密码信息
   * @returns {Promise} Promise对象
   */
  changePassword(data) {
    // 构建请求数据
    const requestData = {};

    // 如果有旧密码，添加到请求数据
    if (data.oldPassword) {
      requestData.oldPassword = data.oldPassword;
    }

    // 如果有验证码，添加到请求数据
    if (data.verifyCode) {
      requestData.verifyCode = data.verifyCode;
      requestData.phoneNumber = data.phone;
    }

    // 添加新密码
    requestData.newPassword = data.newPassword;

    return request.post('/api/auth/change-password', requestData);
  },

  /**
   * 重置密码
   * @param {Object} data 重置信息
   * @returns {Promise} Promise对象
   */
  resetPassword(data) {
    return request.post('/api/auth/reset-password', {
      phoneNumber: data.phone,
      verifyCode: data.verifyCode,
      newPassword: data.password
    });
  },

  /**
   * 发送验证码
   * @param {Object} data 发送信息
   * @returns {Promise} Promise对象
   */
  sendVerifyCode(data) {
    return request.post('/api/sms/send', {
      phoneNumber: data.phone,
      type: data.type
    });
  },

  /**
   * 验证验证码
   * @param {Object} data 验证信息
   * @returns {Promise} Promise对象
   */
  verifyCode(data) {
    return request.post('/api/sms/verify', {
      phoneNumber: data.phone,
      code: data.code,
      type: data.type
    });
  },

  /**
   * 微信登录
   * @param {Object} data 微信登录请求
   * @param {string} data.code 微信授权码
   * @returns {Promise} Promise对象
   */
  wechatLogin(data) {
    console.log('发送微信登录请求');

    // 创建请求数据
    const requestData = {
      code: data.code
    };

    console.log('微信登录请求数据:', requestData);

    // 使用 request.post 发送请求
    return request.post('/api/auth/wechat-login', requestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log('微信登录成功响应:', response);
      return response;
    })
    .catch(error => {
      console.error('微信登录失败错误:', error);
      // 确保错误消息被正确传递
      if (error && error.message) {
        throw new Error(error.message);
      } else {
        throw error;
      }
    });
  }
};

export default AuthAPI;
