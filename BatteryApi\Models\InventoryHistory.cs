using SqlSugar;
using System;

namespace BatteryApi.Models
{
    /// <summary>
    /// 库存历史记录
    /// </summary>
    [SugarTable("InventoryHistory")]
    public class InventoryHistory
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int StoreId { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(StoreId))]
        public Store Store { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ProductId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductId))]
        public Product Product { get; set; }

        /// <summary>
        /// 操作类型（StockIn=入库, StockOut=出库, Transfer=调拨, Create=创建, Update=更新）
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 20)]
        public string OperationType { get; set; }

        /// <summary>
        /// 原数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int OldQuantity { get; set; }

        /// <summary>
        /// 新数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int NewQuantity { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Quantity { get; set; }

        /// <summary>
        /// 出库原因（仅出库操作有效）
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 50)]
        public string Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(IsNullable = true, Length = 500)]
        public string Remark { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string OperatedBy { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime OperationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 源门店ID（仅调拨操作有效）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? FromStoreId { get; set; }

        /// <summary>
        /// 目标门店ID（仅调拨操作有效）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ToStoreId { get; set; }
    }
}
