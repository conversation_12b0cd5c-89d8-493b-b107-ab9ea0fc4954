using BatteryApi.Models;
using SqlSugar;

namespace BatteryApi.Services
{
    /// <summary>
    /// 数据迁移服务
    /// </summary>
    public class DataMigrationService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DataMigrationService> _logger;
        private readonly IConfiguration _configuration;

        public DataMigrationService(
            ISqlSugarClient db, 
            ILogger<DataMigrationService> logger,
            IConfiguration configuration)
        {
            _db = db;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// 执行数据迁移
        /// </summary>
        public async Task MigrateAsync()
        {
            try
            {
                _logger.LogInformation("开始执行数据迁移...");

                // 修复BatteryImage表的ImageUrl和RelativePath字段
                await FixBatteryImagePathsAsync();

                _logger.LogInformation("数据迁移完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据迁移失败");
                throw;
            }
        }

        /// <summary>
        /// 修复BatteryImage表的路径字段
        /// </summary>
        private async Task FixBatteryImagePathsAsync()
        {
            _logger.LogInformation("开始修复BatteryImage表的路径字段...");

            // 获取所有需要修复的图片记录
            var images = await _db.Queryable<ProductImage>()
                .Where(img =>
                    string.IsNullOrEmpty(img.ImageUrl) ||
                    string.IsNullOrEmpty(img.RelativePath) ||
                    string.IsNullOrEmpty(img.FileName))
                .ToListAsync();

            if (images.Count == 0)
            {
                _logger.LogInformation("没有需要修复的图片记录");
                return;
            }

            _logger.LogInformation($"找到 {images.Count} 条需要修复的图片记录");

            var baseUrl = GetBaseUrl();
            var updatedCount = 0;

            foreach (var image in images)
            {
                try
                {
                    var needsUpdate = false;

                    // 修复FileName
                    if (string.IsNullOrEmpty(image.FileName))
                    {
                        image.FileName = $"product_{image.ProductId}_{image.ImageType}_{image.Id}.jpg";
                        needsUpdate = true;
                        _logger.LogDebug($"为图片 {image.Id} 生成文件名: {image.FileName}");
                    }

                    // 修复RelativePath
                    if (string.IsNullOrEmpty(image.RelativePath))
                    {
                        image.RelativePath = $"uploads/products/{image.ProductId}/{image.FileName}";
                        needsUpdate = true;
                        _logger.LogDebug($"为图片 {image.Id} 生成相对路径: {image.RelativePath}");
                    }

                    // 修复ImageUrl
                    if (string.IsNullOrEmpty(image.ImageUrl))
                    {
                        image.ImageUrl = $"{baseUrl}/{image.RelativePath}";
                        needsUpdate = true;
                        _logger.LogDebug($"为图片 {image.Id} 生成完整URL: {image.ImageUrl}");
                    }

                    // 更新数据库
                    if (needsUpdate)
                    {
                        image.UpdatedAt = DateTime.Now;
                        await _db.Updateable(image).ExecuteCommandAsync();
                        updatedCount++;
                        _logger.LogDebug($"已更新图片记录 {image.Id}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"修复图片记录 {image.Id} 失败");
                }
            }

            _logger.LogInformation($"成功修复了 {updatedCount} 条图片记录");
        }

        /// <summary>
        /// 获取基础URL
        /// </summary>
        private string GetBaseUrl()
        {
            // 从配置中获取基础URL，如果没有配置则使用默认值
            var baseUrl = _configuration["BaseUrl"];
            if (string.IsNullOrEmpty(baseUrl))
            {
                // 开发环境默认URL
                baseUrl = "http://localhost:5242";
            }

            return baseUrl.TrimEnd('/');
        }

        /// <summary>
        /// 验证图片路径修复结果
        /// </summary>
        public async Task<bool> ValidateImagePathsAsync()
        {
            try
            {
                var invalidImages = await _db.Queryable<ProductImage>()
                    .Where(img =>
                        string.IsNullOrEmpty(img.ImageUrl) ||
                        string.IsNullOrEmpty(img.RelativePath) ||
                        string.IsNullOrEmpty(img.FileName))
                    .ToListAsync();

                if (invalidImages.Count > 0)
                {
                    _logger.LogWarning($"仍有 {invalidImages.Count} 条图片记录的路径字段为空");
                    return false;
                }

                _logger.LogInformation("所有图片记录的路径字段都已正确设置");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证图片路径时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 获取图片路径统计信息
        /// </summary>
        public async Task<object> GetImagePathStatsAsync()
        {
            try
            {
                var totalCount = await _db.Queryable<ProductImage>().CountAsync();
                var validImageUrlCount = await _db.Queryable<ProductImage>()
                    .Where(img => !string.IsNullOrEmpty(img.ImageUrl))
                    .CountAsync();
                var validRelativePathCount = await _db.Queryable<ProductImage>()
                    .Where(img => !string.IsNullOrEmpty(img.RelativePath))
                    .CountAsync();
                var validFileNameCount = await _db.Queryable<ProductImage>()
                    .Where(img => !string.IsNullOrEmpty(img.FileName))
                    .CountAsync();

                return new
                {
                    TotalCount = totalCount,
                    ValidImageUrlCount = validImageUrlCount,
                    ValidRelativePathCount = validRelativePathCount,
                    ValidFileNameCount = validFileNameCount,
                    InvalidCount = totalCount - Math.Min(Math.Min(validImageUrlCount, validRelativePathCount), validFileNameCount)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取图片路径统计信息时发生错误");
                throw;
            }
        }
    }
}
