{"openapi": "3.0.1", "info": {"title": "Battery API", "description": "Battery Management System API with User Verification", "version": "v1"}, "paths": {"/api/auth/online-status": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/register/phone": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneRegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PhoneRegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PhoneRegisterRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/login/sms": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SmsLoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/wechat-login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeChatLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WeChatLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WeChatLoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/revoke-token": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/current-user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/verification/submit-old": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["FaceImage", "IdCardBack", "IdCardFront", "IdCardNumber", "RealName"], "type": "object", "properties": {"RealName": {"maxLength": 50, "minLength": 0, "type": "string"}, "IdCardNumber": {"maxLength": 18, "minLength": 18, "pattern": "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X|x)$", "type": "string"}, "IdCardFront": {"type": "string", "format": "binary"}, "IdCardBack": {"type": "string", "format": "binary"}, "FaceImage": {"type": "string", "format": "binary"}, "ValidDate": {"type": "string"}}}, "encoding": {"RealName": {"style": "form"}, "IdCardNumber": {"style": "form"}, "IdCardFront": {"style": "form"}, "IdCardBack": {"style": "form"}, "FaceImage": {"style": "form"}, "ValidDate": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/verification/status-old": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/verification/review": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerificationUpdateRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/update-status": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusRequest"}}}}, "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/users/{phoneNumber}": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "phoneNumber", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/battery-categories": {"get": {"tags": ["BatteryCategory"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}}, "post": {"tags": ["BatteryCategory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBatteryCategoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBatteryCategoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBatteryCategoryRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}}, "/api/battery-categories/{id}": {"get": {"tags": ["BatteryCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}, "put": {"tags": ["BatteryCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBatteryCategoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBatteryCategoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBatteryCategoryRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}, "delete": {"tags": ["BatteryCategory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/battery-categories/code/{code}": {"get": {"tags": ["BatteryCategory"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}}, "/api/battery/categories": {"get": {"tags": ["BatteryCompatibility"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BatteryCategoryDto"}}}}}}}}, "/api/BMS/battery-params/{macid}": {"get": {"tags": ["BMS"], "parameters": [{"name": "macid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/BMS/battery-status/{macid}": {"get": {"tags": ["BMS"], "parameters": [{"name": "macid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/BMS/clear-token-cache": {"post": {"tags": ["BMS"], "responses": {"200": {"description": "Success"}}}}, "/api/BMS/generate-battery-code/{macid}": {"get": {"tags": ["BMS"], "parameters": [{"name": "macid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/BMS/test-parse": {"get": {"tags": ["BMS"], "responses": {"200": {"description": "Success"}}}}, "/api/BMS/test-connection": {"get": {"tags": ["BMS"], "responses": {"200": {"description": "Success"}}}}, "/api/Database/initialize": {"post": {"tags": ["Database"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitializeDatabaseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InitializeDatabaseDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InitializeDatabaseDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Database/reset-admin": {"post": {"tags": ["Database"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetAdminDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetAdminDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetAdminDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Database/update-schema": {"post": {"tags": ["Database"], "responses": {"200": {"description": "Success"}}}}, "/api/Database/create-test-orders": {"post": {"tags": ["Database"], "responses": {"200": {"description": "Success"}}}}, "/api/DataMigration/migrate": {"post": {"tags": ["DataMigration"], "responses": {"200": {"description": "Success"}}}}, "/api/DataMigration/validate-image-paths": {"get": {"tags": ["DataMigration"], "responses": {"200": {"description": "Success"}}}}, "/api/DataMigration/image-path-stats": {"get": {"tags": ["DataMigration"], "responses": {"200": {"description": "Success"}}}}, "/api/EnhancedImage/smart-upload": {"post": {"tags": ["EnhancedImage"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "productId": {"type": "integer", "format": "int32"}, "imageType": {"type": "string", "default": "main"}, "autoCompress": {"type": "boolean", "default": true}, "generateThumbnail": {"type": "boolean", "default": true}}}, "encoding": {"file": {"style": "form"}, "productId": {"style": "form"}, "imageType": {"style": "form"}, "autoCompress": {"style": "form"}, "generateThumbnail": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/EnhancedImage/batch-process": {"post": {"tags": ["EnhancedImage"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "productId": {"type": "integer", "format": "int32"}, "processOptions": {"type": "string", "default": "compress,thumbnail"}}}, "encoding": {"files": {"style": "form"}, "productId": {"style": "form"}, "processOptions": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/EnhancedImage/info/{imageId}": {"get": {"tags": ["EnhancedImage"], "parameters": [{"name": "imageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Image/{category}/{filename}": {"get": {"tags": ["Image"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "filename", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Image/product/{productId}/{filename}": {"get": {"tags": ["Image"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "filename", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Image/guid/{guid}": {"get": {"tags": ["Image"], "parameters": [{"name": "guid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Image/info/{category}/{filename}": {"get": {"tags": ["Image"], "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "filename", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Inventory": {"get": {"tags": ["Inventory"], "parameters": [{"name": "StoreId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}, {"name": "SpecIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, {"name": "LowStock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Inventory/stock-in": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StockInRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockInRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StockInRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Inventory/transfer": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Inventory/clear": {"post": {"tags": ["Inventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClearRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ClearRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ClearRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Map/geocode": {"get": {"tags": ["Map"], "parameters": [{"name": "address", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Map/reverse-geocode": {"get": {"tags": ["Map"], "parameters": [{"name": "latitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "longitude", "in": "query", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Map/search": {"get": {"tags": ["Map"], "parameters": [{"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "region", "in": "query", "schema": {"type": "string", "default": "全国"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Map/nearby": {"get": {"tags": ["Map"], "parameters": [{"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "latitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "longitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "radius", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5000}}], "responses": {"200": {"description": "Success"}}}}, "/api/Map/ip-location": {"get": {"tags": ["Map"], "parameters": [{"name": "ip", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Map/network-location": {"post": {"tags": ["Map"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NetworkLocationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NetworkLocationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NetworkLocationRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MultiImageUpload/upload-multiple": {"post": {"tags": ["MultiImageUpload"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "productId": {"type": "integer", "format": "int32"}, "imageType": {"type": "string", "default": "gallery"}, "autoCompress": {"type": "boolean", "default": true}, "generateThumbnail": {"type": "boolean", "default": false}}}, "encoding": {"files": {"style": "form"}, "productId": {"style": "form"}, "imageType": {"style": "form"}, "autoCompress": {"style": "form"}, "generateThumbnail": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/MultiImageUpload/quick-upload": {"post": {"tags": ["MultiImageUpload"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}, "productId": {"type": "integer", "format": "int32"}, "imageType": {"type": "string", "default": "gallery"}}}, "encoding": {"files": {"style": "form"}, "productId": {"style": "form"}, "imageType": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ocr/idcard": {"post": {"tags": ["Ocr"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"side": {"type": "string"}}}, "encoding": {"side": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ocr/performance": {"get": {"tags": ["Ocr"], "responses": {"200": {"description": "Success"}}}}, "/api/Order": {"get": {"tags": ["Order"], "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatteryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StoreId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderCreateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Order/{id}": {"get": {"tags": ["Order"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Order/byOrderNo/{orderNo}": {"get": {"tags": ["Order"], "parameters": [{"name": "orderNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Order/user/{userId}": {"get": {"tags": ["Order"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Order/counts": {"get": {"tags": ["Order"], "responses": {"200": {"description": "Success"}}}}, "/api/Order/pay": {"post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPayDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderPayDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderPayDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Order/cancel": {"post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCancelDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderCancelDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderCancelDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Order/complete": {"post": {"tags": ["Order"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCompleteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderCompleteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderCompleteDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Product": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Product"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "categoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Product/{id}": {"put": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Product/test": {"post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Product/specs": {"get": {"tags": ["Product"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}}}}}, "post": {"tags": ["Product"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductSpecRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductSpecRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductSpecRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}}}}}}, "/api/Product/specs/category/{categoryId}": {"get": {"tags": ["Product"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSpecDto"}}}}}}}}, "/api/Product/specs/{id}": {"get": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}}}}}, "put": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSpecRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductSpecRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductSpecRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductSpecDto"}}}}}}, "delete": {"tags": ["Product"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/product/{productId}": {"get": {"tags": ["ProductImage"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "includeDeleted", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/product/{productId}/type/{imageType}": {"get": {"tags": ["ProductImage"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "imageType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "includeDeleted", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/product/{productId}/main": {"get": {"tags": ["ProductImage"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/product/{productId}/main/{imageId}": {"put": {"tags": ["ProductImage"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "imageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/{imageId}": {"put": {"tags": ["ProductImage"], "parameters": [{"name": "imageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductImageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductImageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductImageRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["ProductImage"], "parameters": [{"name": "imageId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "hardDelete", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/sort": {"put": {"tags": ["ProductImage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateImageOrdersRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateImageOrdersRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateImageOrdersRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/product/{productId}/all": {"delete": {"tags": ["ProductImage"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "hardDelete", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/delete": {"delete": {"tags": ["ProductImage"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteImageByUrlRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteImageByUrlRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteImageByUrlRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/ProductImage/types": {"get": {"tags": ["ProductImage"], "responses": {"200": {"description": "Success"}}}}, "/api/inventory/product": {"get": {"tags": ["ProductInventory"], "parameters": [{"name": "StoreId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ProductId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Keyword", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/inventory/product/history": {"get": {"tags": ["ProductInventory"], "parameters": [{"name": "storeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "productId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "operationType", "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "keyword", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}}}}, "/api/inventory/product/stock-in": {"post": {"tags": ["ProductInventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductStockInRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductStockInRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductStockInRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/inventory/product/stock-out": {"post": {"tags": ["ProductInventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductStockOutRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductStockOutRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductStockOutRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/inventory/product/transfer": {"post": {"tags": ["ProductInventory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductTransferRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductTransferRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductTransferRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/sms/send": {"post": {"tags": ["Sms"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSmsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSmsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendSmsRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendSmsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSmsResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Server Error"}}}}, "/api/sms/verify": {"post": {"tags": ["Sms"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifySmsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifySmsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifySmsRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerifySmsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerifySmsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifySmsResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Server Error"}}}}, "/api/Store": {"get": {"tags": ["Store"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Contact", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "Latitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Longitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxDistance", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Store"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreCreateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Store/{id}": {"get": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Store/nearby": {"get": {"tags": ["Store"], "parameters": [{"name": "latitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "longitude", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxDistance", "in": "query", "schema": {"type": "number", "format": "double", "default": 5000}}, {"name": "batteryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Store/{storeId}/inventory": {"get": {"tags": ["Store"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Store"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreInventoryUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreInventoryUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreInventoryUpdateDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Store/{id}/soft-delete": {"post": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Store/{id}/close-and-delete": {"post": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreSoftDeleteDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Store/{id}/restore": {"post": {"tags": ["Store"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreRestoreDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StoreRestoreDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StoreRestoreDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Store/deleted": {"get": {"tags": ["Store"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}}}}, "/api/store-inventory/{storeId}": {"get": {"tags": ["StoreInventory"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "forceRefresh", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Success"}}}}, "/api/store-inventory/{storeId}/update": {"post": {"tags": ["StoreInventory"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateInventoryRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/store/{storeId}/product-inventory": {"get": {"tags": ["StoreProduct"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/store/{storeId}/assign-products": {"post": {"tags": ["StoreProduct"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignProductsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignProductsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignProductsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/overview": {"get": {"tags": ["SystemMonitor"], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/performance": {"get": {"tags": ["SystemMonitor"], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/slow-operations": {"get": {"tags": ["SystemMonitor"], "parameters": [{"name": "thresholdMs", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1000}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/memory": {"get": {"tags": ["SystemMonitor"], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/gc": {"post": {"tags": ["SystemMonitor"], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/clear-stats": {"post": {"tags": ["SystemMonitor"], "responses": {"200": {"description": "Success"}}}}, "/api/SystemMonitor/app-info": {"get": {"tags": ["SystemMonitor"], "responses": {"200": {"description": "Success"}}}}, "/api/settings": {"get": {"tags": ["SystemSetting"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSystemSettingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSystemSettingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSystemSettingRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/grouped": {"get": {"tags": ["SystemSetting"], "responses": {"200": {"description": "Success"}}}}, "/api/settings/group/{group}": {"get": {"tags": ["SystemSetting"], "parameters": [{"name": "group", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/settings/{key}": {"get": {"tags": ["SystemSetting"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["SystemSetting"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSystemSettingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSystemSettingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSystemSettingRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["SystemSetting"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/settings/batch": {"put": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdateSystemSettingsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchUpdateSystemSettingsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BatchUpdateSystemSettingsRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/user-management": {"put": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserManagementSettingsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserManagementSettingsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserManagementSettingsDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/payment": {"put": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSettingsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentSettingsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentSettingsDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/rental": {"put": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RentalSettingsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RentalSettingsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RentalSettingsDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/maintenance": {"put": {"tags": ["SystemSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MaintenanceSettingsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MaintenanceSettingsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MaintenanceSettingsDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/settings/backup": {"post": {"tags": ["SystemSetting"], "responses": {"200": {"description": "Success"}}}}, "/api/settings/clear-cache": {"post": {"tags": ["SystemSetting"], "responses": {"200": {"description": "Success"}}}}, "/api/users": {"get": {"tags": ["User"], "parameters": [{"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "PhoneNumber", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "IsVerified", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOnline", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoPagedResponse"}}}}}}, "post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCreateDto"}}}}, "responses": {"201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/users/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/users/{id}/status": {"put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserStatusUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserStatusUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserStatusUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/users/{id}/reset-password": {"post": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPasswordUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserPasswordUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserPasswordUpdateDto"}}}}, "responses": {"200": {"description": "Success"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/auth/verification/submit": {"post": {"tags": ["Verification"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["FaceImage", "IdCardBack", "IdCardFront", "IdCardNumber", "RealName"], "type": "object", "properties": {"RealName": {"maxLength": 50, "minLength": 0, "type": "string"}, "IdCardNumber": {"maxLength": 18, "minLength": 18, "pattern": "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X|x)$", "type": "string"}, "IdCardFront": {"type": "string", "format": "binary"}, "IdCardBack": {"type": "string", "format": "binary"}, "FaceImage": {"type": "string", "format": "binary"}, "ValidDate": {"type": "string"}}}, "encoding": {"RealName": {"style": "form"}, "IdCardNumber": {"style": "form"}, "IdCardFront": {"style": "form"}, "IdCardBack": {"style": "form"}, "FaceImage": {"style": "form"}, "ValidDate": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/status": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/verify/{id}": {"post": {"tags": ["Verification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationStatusUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationStatusUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerificationStatusUpdateRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/list": {"get": {"tags": ["Verification"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "keyword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/counts": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/pending": {"get": {"tags": ["Verification"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/{id}": {"get": {"tags": ["Verification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/user/{userId}": {"get": {"tags": ["Verification"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/recognize-idcard": {"post": {"tags": ["Verification"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Image", "Side"], "type": "object", "properties": {"Image": {"type": "string", "format": "binary"}, "Side": {"type": "string"}}}, "encoding": {"Image": {"style": "form"}, "Side": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/upload-idcard-front": {"post": {"tags": ["Verification"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/upload-idcard-back": {"post": {"tags": ["Verification"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/upload-face": {"post": {"tags": ["Verification"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/check-idcard-photos": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/submit-info": {"post": {"tags": ["Verification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerificationInfoRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerificationInfoRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerificationInfoRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/test-list": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success"}}}}, "/api/auth/verification/network-diagnostic": {"get": {"tags": ["Verification"], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AssignProductsRequest": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductAssignment"}, "nullable": true}}, "additionalProperties": false}, "BatchUpdateSystemSettingsRequest": {"required": ["settings"], "type": "object", "properties": {"settings": {"type": "array", "items": {"$ref": "#/components/schemas/SystemSettingDto"}}}, "additionalProperties": false}, "BatteryCategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CellInfo": {"type": "object", "properties": {"mcc": {"type": "integer", "format": "int32"}, "mnc": {"type": "integer", "format": "int32"}, "lac": {"type": "integer", "format": "int32"}, "cellid": {"type": "integer", "format": "int32"}, "signal": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"oldPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ClearRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batterySpec": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateBatteryCategoryRequest": {"required": ["code", "name"], "type": "object", "properties": {"code": {"maxLength": 20, "minLength": 0, "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "displayOrder": {"maximum": 9999, "minimum": 0, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateProductRequest": {"required": ["categoryId", "name", "spec"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 0, "type": "string"}, "spec": {"maxLength": 100, "minLength": 0, "type": "string"}, "status": {"type": "string", "nullable": true}, "price": {"maximum": 1000000, "minimum": 0, "type": "number", "format": "double"}, "rentPrice": {"maximum": 10000, "minimum": 0, "type": "number", "format": "double"}, "manufactureDate": {"type": "string", "format": "date-time", "nullable": true}, "lifespan": {"maximum": 1200, "minimum": 1, "type": "integer", "format": "int32"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "categoryId": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "categoryCode": {"type": "string", "nullable": true}, "categoryName": {"type": "string", "nullable": true}, "voltage": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "capacity": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "cycleCount": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "chargeTime": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "batteryId": {"type": "integer", "format": "int32", "nullable": true}, "mainImages": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImageRequest"}, "nullable": true}, "mainImagesFiles": {"type": "array", "items": {"type": "string", "format": "binary"}, "nullable": true}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/ProductServiceRequest"}, "nullable": true}, "installationFees": {"type": "array", "items": {"$ref": "#/components/schemas/ProductInstallationFeeRequest"}, "nullable": true}, "removedImages": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "CreateProductSpecRequest": {"required": ["categoryId", "name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "voltage": {"maximum": 1000, "minimum": 0, "type": "number", "format": "double"}, "capacity": {"maximum": 10000, "minimum": 0, "type": "number", "format": "double"}, "weight": {"maximum": 1000, "minimum": 0, "type": "number", "format": "double"}, "dimensions": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "price": {"maximum": 1000000, "minimum": 0, "type": "number", "format": "double"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "categoryId": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateSystemSettingRequest": {"required": ["key", "value"], "type": "object", "properties": {"key": {"minLength": 1, "type": "string"}, "value": {"minLength": 1, "type": "string"}, "group": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeleteImageByUrlRequest": {"type": "object", "properties": {"imageUrl": {"type": "string", "nullable": true}, "hardDelete": {"type": "boolean"}}, "additionalProperties": false}, "InitializeDatabaseDto": {"required": ["adminPassword"], "type": "object", "properties": {"adminPassword": {"minLength": 6, "type": "string"}, "adminUsername": {"type": "string", "nullable": true}, "adminPhone": {"type": "string", "nullable": true}, "createSampleData": {"type": "boolean"}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiryTime": {"type": "string", "format": "date-time"}, "username": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isVerified": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "lastLoginIp": {"type": "string", "nullable": true}, "isOnline": {"type": "boolean"}, "lastOnlineTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "MaintenanceSettingsDto": {"type": "object", "properties": {"maintenanceMode": {"type": "boolean"}, "maintenanceMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NetworkLocationRequest": {"type": "object", "properties": {"cellid": {"type": "array", "items": {"$ref": "#/components/schemas/CellInfo"}, "nullable": true}, "wifi": {"type": "array", "items": {"$ref": "#/components/schemas/WifiInfo"}, "nullable": true}}, "additionalProperties": false}, "OrderCancelDto": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderCompleteDto": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderCreateDto": {"type": "object", "properties": {"batteryId": {"type": "integer", "format": "int32"}, "storeId": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "number", "format": "double"}, "rentAmount": {"type": "number", "format": "double"}, "deposit": {"type": "number", "format": "double"}, "rentDays": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OrderPayDto": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int32"}, "payMethod": {"type": "integer", "format": "int32"}, "transactionId": {"type": "string", "nullable": true}, "payTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "PaymentSettingsDto": {"type": "object", "properties": {"enableAlipay": {"type": "boolean"}, "alipayMerchantId": {"type": "string", "nullable": true}, "enableWechat": {"type": "boolean"}, "wechatMerchantId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PhoneRegisterRequest": {"required": ["code", "password", "phoneNumber"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "pattern": "^1\\d{10}$", "type": "string"}, "code": {"minLength": 1, "pattern": "^\\d{6}$", "type": "string"}, "password": {"maxLength": 20, "minLength": 6, "type": "string"}, "username": {"maxLength": 20, "type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProductAssignment": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductImageRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "tempPath": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "relativePath": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int64"}, "status": {"type": "string", "nullable": true}, "isMain": {"type": "boolean"}, "isNew": {"type": "boolean"}, "imageType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductInstallationFeeRequest": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "price": {"maximum": 1000000, "minimum": 0, "type": "number", "format": "double"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "sort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductInventory": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductServiceRequest": {"required": ["name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isEnabled": {"type": "boolean"}, "sort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductSpecDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "voltage": {"type": "number", "format": "double"}, "capacity": {"type": "number", "format": "double"}, "weight": {"type": "number", "format": "double"}, "dimensions": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProductStockInRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductStockOutRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProductTransferRequest": {"type": "object", "properties": {"fromStoreId": {"type": "integer", "format": "int32"}, "toStoreId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefreshTokenRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "verifyCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RentalSettingsDto": {"type": "object", "properties": {"rentalTimeout": {"type": "integer", "format": "int32"}, "rentalDeposit": {"type": "number", "format": "double"}, "overtimeFee": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ResetAdminDto": {"required": ["adminUsername", "newPassword"], "type": "object", "properties": {"newPassword": {"minLength": 6, "type": "string"}, "adminUsername": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "verifyCode": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendSmsRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendSmsResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SmsLoginRequest": {"required": ["code", "phoneNumber"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "pattern": "^1\\d{10}$", "type": "string"}, "code": {"minLength": 1, "pattern": "^\\d{6}$", "type": "string"}}, "additionalProperties": false}, "StockInItem": {"type": "object", "properties": {"spec": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "price": {"type": "number", "format": "double"}, "remark": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StockInRequest": {"type": "object", "properties": {"storeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/StockInItem"}, "nullable": true}}, "additionalProperties": false}, "StoreCreateDto": {"required": ["address", "name", "phone"], "type": "object", "properties": {"name": {"minLength": 1, "type": "string"}, "address": {"minLength": 1, "type": "string"}, "phone": {"minLength": 1, "type": "string"}, "contact": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "status": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "businessHours": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StoreInventoryUpdateDto": {"type": "object", "properties": {"batteryId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "availableQuantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StoreRestoreDto": {"required": ["status"], "type": "object", "properties": {"status": {"minLength": 1, "type": "string"}, "restoreReason": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "StoreSoftDeleteDto": {"required": ["deleteReason"], "type": "object", "properties": {"deleteReason": {"maxLength": 500, "minLength": 0, "type": "string"}, "closeStore": {"type": "boolean"}}, "additionalProperties": false}, "StoreUpdateDto": {"required": ["address", "name", "phone"], "type": "object", "properties": {"name": {"minLength": 1, "type": "string"}, "address": {"minLength": 1, "type": "string"}, "phone": {"minLength": 1, "type": "string"}, "contact": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "status": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "businessHours": {"type": "string", "nullable": true}, "imageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SystemSettingDto": {"required": ["key", "value"], "type": "object", "properties": {"key": {"minLength": 1, "type": "string"}, "value": {"minLength": 1, "type": "string"}, "group": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TokenResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiryTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TransferRequest": {"type": "object", "properties": {"fromStoreId": {"type": "integer", "format": "int32"}, "toStoreId": {"type": "integer", "format": "int32"}, "batterySpec": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateBatteryCategoryRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "displayOrder": {"maximum": 9999, "minimum": 0, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateImageOrdersRequest": {"type": "object", "properties": {"imageOrders": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "UpdateInventoryRequest": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/ProductInventory"}, "nullable": true}}, "additionalProperties": false}, "UpdateProductImageRequest": {"type": "object", "properties": {"imageType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "isMain": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateProductRequest": {"required": ["categoryId", "name", "spec"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 0, "type": "string"}, "spec": {"maxLength": 100, "minLength": 0, "type": "string"}, "status": {"type": "string", "nullable": true}, "price": {"maximum": 1000000, "minimum": 0, "type": "number", "format": "double"}, "rentPrice": {"maximum": 10000, "minimum": 0, "type": "number", "format": "double"}, "manufactureDate": {"type": "string", "format": "date-time", "nullable": true}, "lifespan": {"maximum": 1200, "minimum": 1, "type": "integer", "format": "int32"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "categoryId": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "categoryCode": {"type": "string", "nullable": true}, "categoryName": {"type": "string", "nullable": true}, "voltage": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "capacity": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "cycleCount": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "chargeTime": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "batteryId": {"type": "integer", "format": "int32", "nullable": true}, "mainImages": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImageRequest"}, "nullable": true}, "mainImagesFiles": {"type": "array", "items": {"type": "string", "format": "binary"}, "nullable": true}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/ProductServiceRequest"}, "nullable": true}, "installationFees": {"type": "array", "items": {"$ref": "#/components/schemas/ProductInstallationFeeRequest"}, "nullable": true}, "removedImages": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "UpdateProductSpecRequest": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "voltage": {"maximum": 1000, "minimum": 0, "type": "number", "format": "double"}, "capacity": {"maximum": 10000, "minimum": 0, "type": "number", "format": "double"}, "weight": {"maximum": 1000, "minimum": 0, "type": "number", "format": "double"}, "dimensions": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "price": {"maximum": 1000000, "minimum": 0, "type": "number", "format": "double"}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "imageUrl": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateSystemSettingRequest": {"required": ["value"], "type": "object", "properties": {"value": {"minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserStatusRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserCreateDto": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isVerified": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "isOnline": {"type": "boolean"}, "lastOnlineTime": {"type": "string", "format": "date-time", "nullable": true}, "verification": {"$ref": "#/components/schemas/UserVerificationDto"}}, "additionalProperties": false}, "UserDtoPagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "UserManagementSettingsDto": {"type": "object", "properties": {"allowRegister": {"type": "boolean"}, "requireApproval": {"type": "boolean"}, "forceRealAuth": {"type": "boolean"}}, "additionalProperties": false}, "UserPasswordUpdateDto": {"type": "object", "properties": {"oldPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserStatusUpdateDto": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "statusReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserUpdateDto": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isVerified": {"type": "boolean"}}, "additionalProperties": false}, "UserVerificationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "realName": {"type": "string", "nullable": true}, "idCardNumber": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "verifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "verifiedBy": {"type": "string", "nullable": true}, "faceMatchResult": {"type": "boolean", "nullable": true}, "idCardFrontUrl": {"type": "string", "nullable": true}, "idCardBackUrl": {"type": "string", "nullable": true}, "faceImageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerificationInfoRequest": {"required": ["idCardNumber", "realName"], "type": "object", "properties": {"realName": {"maxLength": 50, "minLength": 0, "type": "string"}, "idCardNumber": {"maxLength": 18, "minLength": 18, "pattern": "^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|X|x)$", "type": "string"}, "validDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerificationResponse": {"type": "object", "properties": {"realName": {"type": "string", "nullable": true}, "idCardNumber": {"type": "string", "nullable": true}, "idCardFrontUrl": {"type": "string", "nullable": true}, "idCardBackUrl": {"type": "string", "nullable": true}, "faceImageUrl": {"type": "string", "nullable": true}, "faceMatchResult": {"type": "boolean"}, "validDate": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "rejectReason": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "verifiedAt": {"type": "string", "format": "date-time", "nullable": true}, "message": {"type": "string", "nullable": true}, "submittedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VerificationStatusUpdateRequest": {"required": ["status"], "type": "object", "properties": {"status": {"minLength": 1, "pattern": "^(Approved|Rejected)$", "type": "string"}, "rejectReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerificationUpdateRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "rejectReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifySmsRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifySmsResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeChatLoginRequest": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "encryptedData": {"type": "string", "nullable": true}, "iv": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WifiInfo": {"type": "object", "properties": {"mac": {"type": "string", "nullable": true}, "signal": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}