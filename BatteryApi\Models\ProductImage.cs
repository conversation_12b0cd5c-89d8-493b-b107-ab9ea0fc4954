using SqlSugar;

namespace BatteryApi.Models
{
    /// <summary>
    /// 商品图片实体类
    /// </summary>
    [SugarTable("ProductImages")]
    public class ProductImage
    {
        /// <summary>
        /// 图片ID（主键）
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 商品ID（外键）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ProductId { get; set; }

        /// <summary>
        /// 图片类型（main=主图, detail=详情图, spec=规格图, gallery=画廊图等）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string ImageType { get; set; } = string.Empty;

        /// <summary>
        /// 图片URL
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// 图片文件名
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = true)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 图片相对路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string RelativePath { get; set; } = string.Empty;

        /// <summary>
        /// 图片文件大小（字节）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long FileSize { get; set; }

        /// <summary>
        /// 图片排序顺序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否为主图
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// 图片描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除（软删除）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 导航属性：关联的商品
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductId))]
        public Product? Product { get; set; }
    }

    /// <summary>
    /// 图片类型枚举
    /// </summary>
    public static class ImageTypes
    {
        /// <summary>
        /// 主图
        /// </summary>
        public const string Main = "main";

        /// <summary>
        /// 详情图
        /// </summary>
        public const string Detail = "detail";

        /// <summary>
        /// 规格图
        /// </summary>
        public const string Spec = "spec";

        /// <summary>
        /// 画廊图
        /// </summary>
        public const string Gallery = "gallery";

        /// <summary>
        /// 缩略图
        /// </summary>
        public const string Thumbnail = "thumbnail";

        /// <summary>
        /// 360度展示图
        /// </summary>
        public const string View360 = "view360";

        /// <summary>
        /// 安装图
        /// </summary>
        public const string Installation = "installation";

        /// <summary>
        /// 包装图
        /// </summary>
        public const string Package = "package";

        /// <summary>
        /// 获取所有图片类型
        /// </summary>
        /// <returns>图片类型列表</returns>
        public static List<string> GetAllTypes()
        {
            return new List<string>
            {
                Main, Detail, Spec, Gallery, Thumbnail,
                View360, Installation, Package
            };
        }

        /// <summary>
        /// 验证图片类型是否有效
        /// </summary>
        /// <param name="imageType">图片类型</param>
        /// <returns>是否有效</returns>
        public static bool IsValidType(string imageType)
        {
            return GetAllTypes().Contains(imageType?.ToLower() ?? string.Empty);
        }
    }
}
