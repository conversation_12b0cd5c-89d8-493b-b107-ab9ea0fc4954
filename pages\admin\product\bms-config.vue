<template>
  <view class="bms-config-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="back-icon">‹</text>
          <text class="back-text">返回</text>
        </view>
        <text class="navbar-title">参数信息</text>
        <view class="navbar-right">
          <view class="refresh-btn" @click="refreshData">
            <text class="refresh-text">刷新</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在获取配置参数...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="loadBMSConfig">重试</button>
    </view>

    <!-- BMS信息内容 -->
    <scroll-view v-else-if="configData" scroll-y class="config-content">
      <!-- 电芯特征 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">电芯特征</text>
        </view>

        <view class="config-table">
          <view class="table-row">
            <text class="param-label">电池类型</text>
            <text class="param-value">{{ getBatteryType() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电池标称容量</text>
            <text class="param-value">{{ getNominalCapacity() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电池实际容量</text>
            <text class="param-value">{{ getActualCapacity() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">休眠等待时间</text>
            <text class="param-value">{{ getSleepWaitTime() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电池编号</text>
            <text class="param-value">{{ getBatteryNumber() }}</text>
          </view>
        </view>
      </view>

      <!-- 保护参数 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">保护参数</text>
        </view>

        <view class="config-table">
          <view class="table-row">
            <text class="param-label">单体过压保护</text>
            <text class="param-value">{{ getOverVoltageProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">单体过压保护延时</text>
            <text class="param-value">{{ getOverVoltageDelay() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">单体过压恢复电压</text>
            <text class="param-value">{{ getOverVoltageRecovery() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">单体欠压保护</text>
            <text class="param-value">{{ getUnderVoltageProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">单体欠压保护延时</text>
            <text class="param-value">{{ getUnderVoltageDelay() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">单体欠压恢复电压</text>
            <text class="param-value">{{ getUnderVoltageRecovery() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">总压过压保护</text>
            <text class="param-value">{{ getTotalOverVoltage() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">总压欠压保护</text>
            <text class="param-value">{{ getTotalUnderVoltage() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">压差保护</text>
            <text class="param-value">{{ getVoltageDiffProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">充电过流保护</text>
            <text class="param-value">{{ getChargeOverCurrent() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">放电过流保护</text>
            <text class="param-value">{{ getDischargeOverCurrent() }}</text>
          </view>
        </view>
      </view>

      <!-- 温度保护 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">温度保护</text>
        </view>

        <view class="config-table">
          <view class="table-row">
            <text class="param-label">充电高温保护</text>
            <text class="param-value">{{ getChargeHighTemp() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">充电低温保护</text>
            <text class="param-value">{{ getChargeLowTemp() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">充电低温保护恢复值</text>
            <text class="param-value">{{ getChargeLowTempRecovery() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">放电高温保护</text>
            <text class="param-value">{{ getDischargeHighTemp() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">放电低温保护</text>
            <text class="param-value">{{ getDischargeLowTemp() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">放电低温保护恢复值</text>
            <text class="param-value">{{ getDischargeLowTempRecovery() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">温差保护</text>
            <text class="param-value">{{ getTempDiffProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">功率管温度保护</text>
            <text class="param-value">{{ getMosfetTempProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">功率管温度保护恢复值</text>
            <text class="param-value">{{ getMosfetTempRecovery() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电池箱内温度保护</text>
            <text class="param-value">{{ getBatteryBoxTempProtection() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电池箱内温度保护恢复值</text>
            <text class="param-value">{{ getBatteryBoxTempRecovery() }}</text>
          </view>
        </view>
      </view>

      <!-- 均衡控制 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">均衡控制</text>
        </view>

        <view class="config-table">
          <view class="table-row">
            <text class="param-label">均衡开启电压</text>
            <text class="param-value">{{ getBalanceStartVoltage() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">均衡开启压差</text>
            <text class="param-value">{{ getBalanceStartDiff() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">主动均衡控制开关</text>
            <text class="param-value">{{ getBalanceControlText() }}</text>
          </view>
        </view>
      </view>

      <!-- 其他 -->
      <view class="config-section">
        <view class="section-header">
          <text class="section-title">其他</text>
        </view>

        <view class="config-table">
          <view class="table-row">
            <text class="param-label">是否启动电流校准</text>
            <text class="param-value">{{ getCurrentCalibrationText() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">电流校准</text>
            <text class="param-value">{{ getCurrentCalibration() }}</text>
          </view>
          <view class="table-row">
            <text class="param-label">加热开关</text>
            <text class="param-value">{{ getHeatingText() }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import BMSAPI from '@/api/bms.js'

export default {
  name: 'BMSConfig',
  data() {
    return {
      macId: '',
      configData: null,
      loading: false,
      error: null,

    }
  },

  onLoad(options) {
    if (options.macId) {
      this.macId = options.macId
      this.loadBMSConfig()
    } else {
      this.error = '缺少MAC ID参数'
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      // 获取页面栈
      const pages = getCurrentPages()

      if (pages.length > 1) {
        // 如果有上一页，则返回上一页
        uni.navigateBack()
      } else {
        // 如果没有上一页，导航到电池管理页面
        uni.reLaunch({
          url: '/pages/admin/battery/list'
        })
      }
    },

    // 加载配置数据
    async loadBMSConfig() {
      this.loading = true
      this.error = null

      try {
        console.log('获取BMS配置数据，MAC ID:', this.macId)
        const response = await BMSAPI.getBatteryStatus(this.macId)

        console.log('BMS配置数据响应:', response)

        if (response && response.data && response.data.data) {
          this.configData = response.data.data
        } else {
          throw new Error('获取BMS配置数据失败')
        }
      } catch (error) {
        console.error('获取BMS配置数据失败:', error)
        this.error = error.message || '获取BMS配置数据失败，请检查网络连接'
      } finally {
        this.loading = false
      }
    },

    // 刷新数据
    refreshData() {
      this.loadBMSConfig()
    },

    // ========== 实时状态数据获取方法 ==========

    // 获取SOC
    getSOC() {
      return this.getStateValue('soc', 0)
    },

    // 获取总电压
    getTotalVoltage() {
      const voltageAll = this.getStateValue('voltageAll', '0V')
      return voltageAll.replace('V', '') || '0'
    },

    // 获取电流
    getCurrent() {
      return this.getStateValue('current', 0).toFixed(2)
    },

    // 获取功率
    getPower() {
      return this.getStateValue('power', 0).toFixed(2)
    },

    // 获取充电状态
    getChargeStatus() {
      const chargeState = this.getStateValue('chargeState', 0)
      const statusMap = {
        0: '未充电',
        1: '充电中',
        2: '充电完成',
        3: '充电异常'
      }
      return statusMap[chargeState] || '未知'
    },

    // 获取充电状态样式类
    getChargeStatusClass() {
      const chargeState = this.getStateValue('chargeState', 0)
      return chargeState === 1 ? 'enabled' : 'disabled'
    },

    // 获取连接状态
    getConnectStatus() {
      const connectState = this.getStateValue('connectState', 0)
      return connectState === 1 ? '已连接' : '未连接'
    },

    // 获取连接状态样式类
    getConnectStatusClass() {
      const connectState = this.getStateValue('connectState', 0)
      return connectState === 1 ? 'enabled' : 'disabled'
    },

    // 获取最高电压
    getMaxVoltage() {
      return this.getStateValue('maxVoltage', 0).toFixed(3)
    },

    // 获取最低电压
    getMinVoltage() {
      return this.getStateValue('minVoltage', 0).toFixed(3)
    },

    // 获取平均电压
    getAverageVoltage() {
      return this.getStateValue('averageVoltage', 0).toFixed(3)
    },

    // 获取压差
    getVoltageDifference() {
      return this.getStateValue('voltageDifference', 0).toFixed(3)
    },

    // 获取单体电压数量
    getCellCount() {
      return this.getStateValue('count', 0)
    },

    // 获取单体电压数组
    getCellVoltages() {
      return this.getStateValue('voltage', [])
    },

    // 获取单体电压样式类
    getCellVoltageClass(voltage) {
      const maxVoltage = this.getStateValue('maxVoltage', 0)
      const minVoltage = this.getStateValue('minVoltage', 0)

      if (voltage === maxVoltage) {
        return 'cell-max'
      } else if (voltage === minVoltage) {
        return 'cell-min'
      }
      return 'cell-normal'
    },

    // 获取最高温度
    getMaxTemp() {
      return this.getStateValue('maxTemp', 0)
    },

    // 获取最低温度
    getMinTemp() {
      return this.getStateValue('minTemp', 0)
    },

    // 获取温度传感器数量
    getTempCount() {
      return this.getStateValue('tempCount', 0)
    },

    // 获取详细温度信息
    getDetailedTemperatures() {
      const tempV2 = this.getStateValue('tempV2', [])
      if (tempV2.length > 0) {
        return tempV2.map(t => ({
          label: t.label || t.label_eng || '',
          value: t.value || 0
        }))
      }

      // 如果没有详细温度信息，使用基本温度数据
      const temps = this.getStateValue('temp', [])
      return temps.map((temp, index) => ({
        label: `温度传感器${index + 1}`,
        value: temp
      }))
    },

    // 获取温度样式类
    getTempClass(temp) {
      if (temp > 50) {
        return 'temp-high'
      } else if (temp < 0) {
        return 'temp-low'
      }
      return 'temp-normal'
    },

    // 获取固件版本
    getFirmware() {
      return this.getStateValue('firmware', 'N/A')
    },

    // 获取协议版本
    getProtocol() {
      return this.getStateValue('protocol', 'N/A')
    },

    // 获取容量
    getCapacity() {
      return this.getStateValue('capacity', 0)
    },

    // 获取循环次数
    getCycleCount() {
      return this.getStateValue('cycle', 0)
    },

    // 获取充电MOS状态
    getChargeMOSStatus() {
      const chargingMOS = this.getStateValue('chargingMOS', 0)
      return chargingMOS === 1 ? '开启' : '关闭'
    },

    // 获取充电MOS样式类
    getChargeMOSClass() {
      const chargingMOS = this.getStateValue('chargingMOS', 0)
      return chargingMOS === 1 ? 'enabled' : 'disabled'
    },

    // 获取放电MOS状态
    getDischargeMOSStatus() {
      const dischargeMOS = this.getStateValue('dischargeMOS', 0)
      return dischargeMOS === 1 ? '开启' : '关闭'
    },

    // 获取放电MOS样式类
    getDischargeMOSClass() {
      const dischargeMOS = this.getStateValue('dischargeMOS', 0)
      return dischargeMOS === 1 ? 'enabled' : 'disabled'
    },

    // 获取均衡状态
    getEquilibriumStatus() {
      const equilibrium = this.getStateValue('equilibrium', 0)
      return equilibrium === 1 ? '均衡中' : '未均衡'
    },

    // 获取均衡状态样式类
    getEquilibriumClass() {
      const equilibrium = this.getStateValue('equilibrium', 0)
      return equilibrium === 1 ? 'enabled' : 'disabled'
    },

    // 获取故障状态
    getFaultStates() {
      return this.getStateValue('faultState', [])
    },

    // 获取报警状态
    getAlarmStates() {
      return this.getStateValue('alarmState', [])
    },

    // 通用方法：从状态数据中获取值
    getStateValue(key, defaultValue) {
      if (!this.configData || !this.configData.state) {
        return defaultValue
      }

      const value = this.configData.state[key]
      return value !== undefined && value !== null ? value : defaultValue
    },

    // ========== 配置参数获取方法 ==========

    // 获取电池类型
    getBatteryType() {
      return this.getConfigValue('电池类型', '磷酸铁锂')
    },

    // 获取标称容量
    getNominalCapacity() {
      return this.getConfigValue('电池标称容量', '40AH')
    },

    // 获取实际容量
    getActualCapacity() {
      return this.getConfigValue('电池实际容量', '40AH')
    },

    // 获取休眠等待时间
    getSleepWaitTime() {
      return this.getConfigValue('休眠等待时间', '10s')
    },

    // 获取电池编号
    getBatteryNumber() {
      return this.getConfigValue('电池编号', this.macId || 'InputUserdata60322504290')
    },

    // 获取单体过压保护
    getOverVoltageProtection() {
      return this.getConfigValue('单体过压保护', '3.63V')
    },

    // 获取单体过压保护延时
    getOverVoltageDelay() {
      return this.getConfigValue('单体过压保护延时', '5s')
    },

    // 获取单体过压恢复电压
    getOverVoltageRecovery() {
      return this.getConfigValue('单体过压恢复电压', '3.55V')
    },

    // 获取单体欠压保护
    getUnderVoltageProtection() {
      return this.getConfigValue('单体欠压保护', '2.6V')
    },

    // 获取单体欠压保护延时
    getUnderVoltageDelay() {
      return this.getConfigValue('单体欠压保护延时', '5s')
    },

    // 获取单体欠压恢复电压
    getUnderVoltageRecovery() {
      return this.getConfigValue('单体欠压恢复电压', '2.65V')
    },

    // 获取总压过压保护
    getTotalOverVoltage() {
      return this.getConfigValue('总压过压保护', '72.6V')
    },

    // 获取总压欠压保护
    getTotalUnderVoltage() {
      return this.getConfigValue('总压欠压保护', '52V')
    },

    // 获取压差保护
    getVoltageDiffProtection() {
      return this.getConfigValue('压差保护', '0.3V')
    },

    // 获取充电过流保护
    getChargeOverCurrent() {
      return this.getConfigValue('充电过流保护', '25A')
    },

    // 获取放电过流保护
    getDischargeOverCurrent() {
      return this.getConfigValue('放电过流保护', '40A')
    },

    // 获取充电高温保护
    getChargeHighTemp() {
      return this.getConfigValue('充电高温保护', '70°C')
    },

    // 获取充电低温保护
    getChargeLowTemp() {
      return this.getConfigValue('充电低温保护', '-20°C')
    },

    // 获取充电低温保护恢复值
    getChargeLowTempRecovery() {
      return this.getConfigValue('充电低温保护恢复值', '-10°C')
    },

    // 获取放电高温保护
    getDischargeHighTemp() {
      return this.getConfigValue('放电高温保护', '70°C')
    },

    // 获取放电低温保护
    getDischargeLowTemp() {
      return this.getConfigValue('放电低温保护', '-20°C')
    },

    // 获取放电低温保护恢复值
    getDischargeLowTempRecovery() {
      return this.getConfigValue('放电低温保护恢复值', '-10°C')
    },

    // 获取温差保护
    getTempDiffProtection() {
      return this.getConfigValue('温差保护', '20°C')
    },

    // 获取功率管温度保护
    getMosfetTempProtection() {
      return this.getConfigValue('功率管温度保护', '100°C')
    },

    // 获取功率管温度保护恢复值
    getMosfetTempRecovery() {
      return this.getConfigValue('功率管温度保护恢复值', '80°C')
    },

    // 获取电池箱内温度保护
    getBatteryBoxTempProtection() {
      return this.getConfigValue('电池箱内温度保护', '100°C')
    },

    // 获取电池箱内温度保护恢复值
    getBatteryBoxTempRecovery() {
      return this.getConfigValue('电池箱内温度保护恢复值', '100°C')
    },

    // 获取均衡开启电压
    getBalanceStartVoltage() {
      return this.getConfigValue('均衡开启电压', '3V')
    },

    // 获取均衡开启压差
    getBalanceStartDiff() {
      return this.getConfigValue('均衡开启压差', '0.003V')
    },

    // 获取均衡控制状态
    getBalanceControlStatus() {
      const value = this.getConfigValue('主动均衡控制开关', '开启')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取均衡控制文本
    getBalanceControlText() {
      const value = this.getConfigValue('主动均衡控制开关', '开启')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 获取电流校准状态
    getCurrentCalibrationStatus() {
      const value = this.getConfigValue('是否启动电流校准', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取电流校准文本
    getCurrentCalibrationText() {
      const value = this.getConfigValue('是否启动电流校准', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 获取电流校准值
    getCurrentCalibration() {
      return this.getConfigValue('电流校准', '952MA')
    },

    // 获取加热开关状态
    getHeatingStatus() {
      const value = this.getConfigValue('加热开关', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取加热开关文本
    getHeatingText() {
      const value = this.getConfigValue('加热开关', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 通用方法：从配置数据中获取值
    getConfigValue(paramName, defaultValue = 'N/A') {
      if (!this.configData || !this.configData.sets) {
        return defaultValue
      }

      // 遍历所有配置集合
      for (const set of this.configData.sets) {
        if (set.vars && Array.isArray(set.vars)) {
          // 在当前配置集合中查找参数
          const param = set.vars.find(v =>
            v.label === paramName ||
            v.name === paramName ||
            v.label?.includes(paramName) ||
            paramName.includes(v.label || '')
          )

          if (param) {
            // 如果是radio类型，返回选中的选项标签
            if (param.type === 'radio' && param.option && param.option.length > 0) {
              const selectedOption = param.option.find(opt => opt.value === param.value)
              return selectedOption ? selectedOption.label : param.value
            }

            // 返回参数值，如果有单位则添加单位
            let value = param.value
            if (param.unit && param.unit !== '') {
              value += param.unit
            }
            return value
          }
        }
      }

      return defaultValue
    },

    // 编辑配置
    editConfig() {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      })
    },

    // 导出配置
    exportConfig() {
      uni.showActionSheet({
        itemList: ['导出为JSON', '导出为文本', '分享配置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.exportAsJSON()
              break
            case 1:
              this.exportAsText()
              break
            case 2:
              this.shareConfig()
              break
          }
        }
      })
    },

    // 导出为JSON
    exportAsJSON() {
      console.log('导出配置为JSON:', this.configData)
      uni.showToast({
        title: '导出JSON成功',
        icon: 'success'
      })
    },

    // 导出为文本
    exportAsText() {
      console.log('导出配置为文本')
      uni.showToast({
        title: '导出文本成功',
        icon: 'success'
      })
    },

    // 分享配置
    shareConfig() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      })
    },

    // ========== 开关组件值获取方法 ==========

    // 获取均衡控制开关值
    getBalanceControlValue() {
      const status = this.getConfigValue('主动均衡控制开关', '关闭')
      return status === '开启' || status === '1' || status === 1
    },

    // 获取电流校准开关值
    getCurrentCalibrationValue() {
      const status = this.getConfigValue('是否启动电流校准', '关闭')
      return status === '启动' || status === '开启' || status === '1' || status === 1
    },

    // 获取加热开关值
    getHeatingValue() {
      const status = this.getConfigValue('加热开关', '关闭')
      return status === '开启' || status === '1' || status === 1
    },

    // 获取均衡控制开关文本
    getBalanceControlText() {
      return this.getBalanceControlValue() ? '开启' : '关闭'
    },

    // 获取电流校准开关文本
    getCurrentCalibrationText() {
      return this.getCurrentCalibrationValue() ? '启动' : '关闭'
    },

    // 获取加热开关文本
    getHeatingText() {
      return this.getHeatingValue() ? '开启' : '关闭'
    },

    // ========== 参数操作方法 ==========

    // 查看参数详情
    viewParam(paramName) {
      const paramValue = this.getParamValue(paramName)
      uni.showModal({
        title: '参数详情',
        content: `参数名称：${this.getParamLabel(paramName)}\n当前值：${paramValue}`,
        showCancel: false,
        confirmText: '确定'
      })
    },



    // 获取参数值
    getParamValue(paramName) {
      const methodMap = {
        'batteryType': this.getBatteryType,
        'nominalCapacity': this.getNominalCapacity,
        'actualCapacity': this.getActualCapacity,
        'sleepWaitTime': this.getSleepWaitTime,
        'batteryNumber': this.getBatteryNumber,
        'overVoltageProtection': this.getOverVoltageProtection,
        'overVoltageDelay': this.getOverVoltageDelay,
        'overVoltageRecovery': this.getOverVoltageRecovery,
        'underVoltageProtection': this.getUnderVoltageProtection,
        'underVoltageDelay': this.getUnderVoltageDelay,
        'underVoltageRecovery': this.getUnderVoltageRecovery,
        'totalOverVoltage': this.getTotalOverVoltage,
        'totalUnderVoltage': this.getTotalUnderVoltage,
        'voltageDiffProtection': this.getVoltageDiffProtection,
        'chargeOverCurrent': this.getChargeOverCurrent,
        'dischargeOverCurrent': this.getDischargeOverCurrent,
        'chargeHighTemp': this.getChargeHighTemp,
        'chargeLowTemp': this.getChargeLowTemp,
        'chargeLowTempRecovery': this.getChargeLowTempRecovery,
        'dischargeHighTemp': this.getDischargeHighTemp,
        'dischargeLowTemp': this.getDischargeLowTemp,
        'dischargeLowTempRecovery': this.getDischargeLowTempRecovery,
        'tempDiffProtection': this.getTempDiffProtection,
        'mosfetTempProtection': this.getMosfetTempProtection,
        'mosfetTempRecovery': this.getMosfetTempRecovery,
        'batteryBoxTempProtection': this.getBatteryBoxTempProtection,
        'batteryBoxTempRecovery': this.getBatteryBoxTempRecovery,
        'balanceStartVoltage': this.getBalanceStartVoltage,
        'balanceStartDiff': this.getBalanceStartDiff,
        'balanceControlSwitch': this.getBalanceControlText,
        'currentCalibrationSwitch': this.getCurrentCalibrationText,
        'currentCalibration': this.getCurrentCalibration,
        'heatingSwitch': this.getHeatingText
      }

      const method = methodMap[paramName]
      return method ? method.call(this) : 'N/A'
    },

    // 获取参数标签
    getParamLabel(paramName) {
      const labelMap = {
        'batteryType': '电池类型',
        'nominalCapacity': '电池标称容量',
        'actualCapacity': '电池实际容量',
        'sleepWaitTime': '休眠等待时间',
        'batteryNumber': '电池编号',
        'overVoltageProtection': '单体过压保护',
        'overVoltageDelay': '单体过压保护延时',
        'overVoltageRecovery': '单体过压恢复电压',
        'underVoltageProtection': '单体欠压保护',
        'underVoltageDelay': '单体欠压保护延时',
        'underVoltageRecovery': '单体欠压恢复电压',
        'totalOverVoltage': '总压过压保护',
        'totalUnderVoltage': '总压欠压保护',
        'voltageDiffProtection': '压差保护',
        'chargeOverCurrent': '充电过流保护',
        'dischargeOverCurrent': '放电过流保护',
        'chargeHighTemp': '充电高温保护',
        'chargeLowTemp': '充电低温保护',
        'chargeLowTempRecovery': '充电低温保护恢复值',
        'dischargeHighTemp': '放电高温保护',
        'dischargeLowTemp': '放电低温保护',
        'dischargeLowTempRecovery': '放电低温保护恢复值',
        'tempDiffProtection': '温差保护',
        'mosfetTempProtection': '功率管温度保护',
        'mosfetTempRecovery': '功率管温度保护恢复值',
        'batteryBoxTempProtection': '电池箱内温度保护',
        'batteryBoxTempRecovery': '电池箱内温度保护恢复值',
        'balanceStartVoltage': '均衡开启电压',
        'balanceStartDiff': '均衡开启压差',
        'balanceControlSwitch': '主动均衡控制开关',
        'currentCalibrationSwitch': '是否启动电流校准',
        'currentCalibration': '电流校准',
        'heatingSwitch': '加热开关'
      }

      return labelMap[paramName] || paramName
    },


  }
}
</script>

<style lang="scss" scoped>
.bms-config-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
  padding-top: var(--status-bar-height);

  .navbar-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;

    .navbar-left {
      display: flex;
      align-items: center;

      .back-icon {
        font-size: 40rpx;
        color: #333;
        margin-right: 10rpx;
      }

      .back-text {
        font-size: 32rpx;
        color: #333;
      }
    }

    .navbar-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      flex: 1;
      text-align: center;
    }

    .navbar-right {
      .refresh-btn {
        .refresh-text {
          font-size: 28rpx;
          color: #007aff;
        }
      }


    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #e0e0e0;
    border-top: 4rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    color: #666;
    font-size: 28rpx;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;

  .error-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .error-text {
    color: #ff4757;
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .retry-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 40rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
  }
}

.config-content {
  padding-top: calc(var(--status-bar-height) + 88rpx + 20rpx);
  padding-bottom: 20rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
}

// 配置区块样式
.config-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .section-header {
    background: #f8f9fa;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #e9ecef;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

// 表格样式
.config-table {
  .table-row {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    min-height: 100rpx;

    &:last-child {
      border-bottom: none;
    }

    .param-label {
      width: 200rpx;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      flex-shrink: 0;
    }



    .param-value {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      margin: 0 20rpx;
      font-weight: 500;

      &.highlight {
        color: #007aff;
        font-weight: bold;
      }

      &.status {
        &.enabled {
          color: #52c41a;
        }

        &.disabled {
          color: #ff4d4f;
        }
      }
    }


  }
}


</style>
