<!--
  规格表单组件
  精简版本，移除冗余代码

  <AUTHOR> System Team
  @version 2.0.0
  @since 2024-01-01
-->

<template>
  <u-popup
    v-model="visible"
    mode="center"
    border-radius="12"
    width="90%"
    @close="handleClose"
  >
    <view class="spec-form">
      <!-- 表单标题 -->
      <view class="form-header">
        <text class="form-title">{{ isEditing ? '编辑规格' : '添加规格' }}</text>
        <u-icon name="close" @click="handleClose" />
      </view>

      <!-- 表单内容 -->
      <view class="form-body">
        <u-form
          :model="formData"
          :rules="formRules"
          ref="specForm"
          label-position="top"
        >
          <!-- 所属分类 -->
          <u-form-item label="所属分类">
            <u-input
              :value="category && category.name || ''"
              disabled
              placeholder="请先选择分类"
            />
          </u-form-item>

          <!-- 规格名称 -->
          <u-form-item
            label="规格名称"
            prop="name"
          >
            <u-input
              v-model="formData.name"
              placeholder="请输入规格名称"
              maxlength="100"
            />
          </u-form-item>

          <!-- 技术参数 -->
          <view class="form-section">
            <text class="section-title">技术参数</text>

            <u-form-item label="电压 (V)">
              <u-input
                v-model="formData.voltage"
                placeholder="请输入电压"
                type="number"
              />
            </u-form-item>

            <u-form-item label="容量 (Ah)">
              <u-input
                v-model="formData.capacity"
                placeholder="请输入容量"
                type="number"
              />
            </u-form-item>

            <u-form-item label="重量 (kg)">
              <u-input
                v-model="formData.weight"
                placeholder="请输入重量"
                type="number"
              />
            </u-form-item>

            <u-form-item label="尺寸">
              <u-input
                v-model="formData.dimensions"
                placeholder="请输入尺寸，如：长×宽×高"
                maxlength="50"
              />
            </u-form-item>
          </view>

          <!-- 商业信息 -->
          <view class="form-section">
            <text class="section-title">商业信息</text>

            <u-form-item
              label="价格 (元)"
              prop="price"
            >
              <u-input
                v-model="formData.price"
                placeholder="请输入价格"
                type="number"
              />
            </u-form-item>

            <u-form-item label="规格描述">
              <u-textarea
                v-model="formData.description"
                placeholder="请输入规格描述（可选）"
                maxlength="500"
                height="120"
              />
            </u-form-item>

            <u-form-item label="图片URL">
              <u-input
                v-model="formData.imageUrl"
                placeholder="请输入图片URL（可选）"
                maxlength="500"
              />
            </u-form-item>
          </view>
        </u-form>
      </view>

      <!-- 表单按钮 -->
      <view class="form-footer">
        <u-button @click="handleClose">取消</u-button>
        <u-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          {{ isEditing ? '更新' : '创建' }}
        </u-button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { BatterySpecValidator } from '@/utils/validators/BatteryValidators'

/**
 * 规格表单组件
 */
export default {
  name: 'SpecForm',

  props: {
    // 显示状态
    value: {
      type: Boolean,
      default: false
    },

    // 规格数据
    spec: {
      type: Object,
      default: null
    },

    // 所属分类
    category: {
      type: Object,
      default: null
    },

    // 是否编辑模式
    isEditing: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      saving: false,

      // 表单数据
      formData: {
        name: '',
        voltage: 0,
        capacity: 0,
        weight: 0,
        dimensions: '',
        price: 0,
        description: '',
        imageUrl: ''
      },

      // 表单验证规则
      formRules: {
        name: [
          {
            required: true,
            message: '请输入规格名称',
            trigger: 'blur'
          },
          {
            min: 1,
            max: 100,
            message: '规格名称长度在1到100个字符之间',
            trigger: 'blur'
          }
        ],
        price: [
          {
            required: true,
            message: '请输入价格',
            trigger: 'blur'
          },
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: '请输入有效的价格',
            trigger: 'blur'
          }
        ]
      }
    }
  },

  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },

  watch: {
    // 监听规格数据变化
    spec: {
      handler(newSpec) {
        if (newSpec) {
          this.formData = {
            name: newSpec.name || '',
            voltage: newSpec.voltage || 0,
            capacity: newSpec.capacity || 0,
            weight: newSpec.weight || 0,
            dimensions: newSpec.dimensions || '',
            price: newSpec.price || 0,
            description: newSpec.description || '',
            imageUrl: newSpec.imageUrl || ''
          }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    },

    // 监听显示状态
    visible(newVal) {
      if (!newVal) {
        this.resetForm()
      }
    }
  },

  methods: {
    /**
     * 处理保存
     */
    async handleSave() {
      try {
        // 检查分类
        if (!this.category) {
          uni.showToast({
            title: '请先选择分类',
            icon: 'none'
          })
          return
        }

        // 表单验证
        const valid = await this.validateForm()
        if (!valid) return

        // 业务验证
        const validationResult = BatterySpecValidator.validate({
          ...this.formData,
          categoryId: this.category.id
        }, this.isEditing)

        if (!validationResult.isValid) {
          uni.showToast({
            title: validationResult.getFirstError(),
            icon: 'none'
          })
          return
        }

        this.saving = true

        // 触发保存事件
        this.$emit('save', { ...this.formData })

      } catch (error) {
        console.error('保存规格失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },

    /**
     * 处理关闭
     */
    handleClose() {
      this.$emit('cancel')
      this.visible = false
    },

    /**
     * 表单验证
     */
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.specForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        name: '',
        voltage: 0,
        capacity: 0,
        weight: 0,
        dimensions: '',
        price: 0,
        description: '',
        imageUrl: ''
      }

      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.specForm) {
          this.$refs.specForm.clearValidate()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.spec-form {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .form-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .form-body {
    padding: 30rpx;
    max-height: 70vh;
    overflow-y: auto;

    .form-section {
      margin-bottom: 40rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #666;
        margin-bottom: 20rpx;
        display: block;
        padding-bottom: 10rpx;
        border-bottom: 1rpx solid #f0f0f0;
      }
    }
  }

  .form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    background: #f8f9fa;
  }
}
</style>
