namespace BatteryApi.DTOs.Product;

/// <summary>
/// 商品规格DTO
/// </summary>
public class ProductSpecDto
{
    /// <summary>
    /// 规格ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 电压 (V)
    /// </summary>
    public decimal Voltage { get; set; } = 0;

    /// <summary>
    /// 容量 (Ah)
    /// </summary>
    public decimal Capacity { get; set; } = 0;

    /// <summary>
    /// 重量 (kg)
    /// </summary>
    public decimal Weight { get; set; } = 0;

    /// <summary>
    /// 尺寸规格
    /// </summary>
    public string? Dimensions { get; set; }

    /// <summary>
    /// 价格 (元)
    /// </summary>
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 描述信息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 图片URL
    /// </summary>
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 所属分类ID
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int DisplayOrder { get; set; } = 0;
}
