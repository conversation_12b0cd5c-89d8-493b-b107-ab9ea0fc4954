/**
 * 电池管理服务
 * 提供电池分类、规格、电池实体的统一管理API
 * 
 * <AUTHOR> System Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import request from '@/utils/request'

/**
 * 电池分类管理服务
 */
export class BatteryCategoryService {
  /**
   * 获取所有电池分类
   * @returns {Promise<Array>} 分类列表
   */
  static async getCategories() {
    try {
      console.log('🔍 开始获取电池分类列表')
      const response = await request.get('/api/battery-categories')
      console.log('✅ 成功获取分类列表，数量:', response.length)
      return response
    } catch (error) {
      console.error('❌ 获取分类列表失败:', error)
      throw new Error('获取分类列表失败')
    }
  }

  /**
   * 根据ID获取分类详情
   * @param {number} id - 分类ID
   * @returns {Promise<Object>} 分类详情
   */
  static async getCategoryById(id) {
    try {
      console.log('🔍 开始获取分类详情，ID:', id)
      const response = await request.get(`/api/battery-categories/${id}`)
      console.log('✅ 成功获取分类详情:', response.name)
      return response
    } catch (error) {
      console.error('❌ 获取分类详情失败:', error)
      throw new Error(`获取分类详情失败: ${error.message}`)
    }
  }

  /**
   * 创建新分类
   * @param {Object} categoryData - 分类数据
   * @param {string} categoryData.code - 分类代码
   * @param {string} categoryData.name - 分类名称
   * @param {string} [categoryData.description] - 分类描述
   * @param {boolean} [categoryData.isActive=true] - 是否激活
   * @param {number} [categoryData.displayOrder=0] - 显示顺序
   * @returns {Promise<Object>} 创建的分类信息
   */
  static async createCategory(categoryData) {
    try {
      console.log('🔍 开始创建分类:', categoryData.name)
      
      // 数据验证
      this._validateCategoryData(categoryData)
      
      // 准备请求数据
      const requestData = {
        code: categoryData.code.trim(),
        name: categoryData.name.trim(),
        description: categoryData.description?.trim() || '',
        isActive: categoryData.isActive !== false,
        displayOrder: Number(categoryData.displayOrder) || 0
      }
      
      const response = await request.post('/api/battery-categories', requestData)
      console.log('✅ 成功创建分类，ID:', response.id)
      return response
    } catch (error) {
      console.error('❌ 创建分类失败:', error)
      throw new Error(`创建分类失败: ${error.message}`)
    }
  }

  /**
   * 更新分类信息
   * @param {number} id - 分类ID
   * @param {Object} categoryData - 更新的分类数据
   * @returns {Promise<Object>} 更新后的分类信息
   */
  static async updateCategory(id, categoryData) {
    try {
      console.log('🔍 开始更新分类，ID:', id)
      
      // 数据验证
      this._validateCategoryData(categoryData, false)
      
      // 准备请求数据
      const requestData = {
        name: categoryData.name.trim(),
        description: categoryData.description?.trim() || '',
        isActive: categoryData.isActive !== false,
        displayOrder: Number(categoryData.displayOrder) || 0
      }
      
      const response = await request.put(`/api/battery-categories/${id}`, requestData)
      console.log('✅ 成功更新分类:', response.name)
      return response
    } catch (error) {
      console.error('❌ 更新分类失败:', error)
      throw new Error(`更新分类失败: ${error.message}`)
    }
  }

  /**
   * 删除分类
   * @param {number} id - 分类ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  static async deleteCategory(id) {
    try {
      console.log('🔍 开始删除分类，ID:', id)
      await request.delete(`/api/battery-categories/${id}`)
      console.log('✅ 成功删除分类，ID:', id)
      return true
    } catch (error) {
      console.error('❌ 删除分类失败:', error)
      throw new Error(`删除分类失败: ${error.message}`)
    }
  }

  /**
   * 验证分类数据
   * @private
   * @param {Object} categoryData - 分类数据
   * @param {boolean} [requireCode=true] - 是否需要验证代码
   */
  static _validateCategoryData(categoryData, requireCode = true) {
    if (!categoryData) {
      throw new Error('分类数据不能为空')
    }
    
    if (requireCode && !categoryData.code?.trim()) {
      throw new Error('分类代码不能为空')
    }
    
    if (!categoryData.name?.trim()) {
      throw new Error('分类名称不能为空')
    }
    
    if (categoryData.name.length > 50) {
      throw new Error('分类名称不能超过50个字符')
    }
    
    if (requireCode && categoryData.code.length > 20) {
      throw new Error('分类代码不能超过20个字符')
    }
  }
}

/**
 * 电池规格管理服务
 */
export class BatterySpecService {
  /**
   * 获取所有电池规格
   * @returns {Promise<Array>} 规格列表
   */
  static async getSpecs() {
    try {
      console.log('🔍 开始获取电池规格列表')
      const response = await request.get('/api/battery-specs')
      console.log('✅ 成功获取规格列表，数量:', response.length)
      return response
    } catch (error) {
      console.error('❌ 获取规格列表失败:', error)
      throw new Error('获取规格列表失败')
    }
  }

  /**
   * 根据分类代码获取规格列表
   * @param {string} categoryCode - 分类代码
   * @returns {Promise<Array>} 规格列表
   */
  static async getSpecsByCategory(categoryCode) {
    try {
      console.log('🔍 开始获取分类规格列表，分类代码:', categoryCode)
      const response = await request.get(`/api/battery-specs/category/${categoryCode}`)
      console.log('✅ 成功获取分类规格列表，数量:', response.length)
      return response
    } catch (error) {
      console.error('❌ 获取分类规格列表失败:', error)
      throw new Error(`获取分类规格列表失败: ${error.message}`)
    }
  }

  /**
   * 根据ID获取规格详情
   * @param {number} id - 规格ID
   * @returns {Promise<Object>} 规格详情
   */
  static async getSpecById(id) {
    try {
      console.log('🔍 开始获取规格详情，ID:', id)
      const response = await request.get(`/api/battery-specs/${id}`)
      console.log('✅ 成功获取规格详情:', response.name)
      return response
    } catch (error) {
      console.error('❌ 获取规格详情失败:', error)
      throw new Error(`获取规格详情失败: ${error.message}`)
    }
  }

  /**
   * 创建新规格
   * @param {Object} specData - 规格数据
   * @returns {Promise<Object>} 创建的规格信息
   */
  static async createSpec(specData) {
    try {
      console.log('🔍 开始创建规格:', specData.name)
      
      // 数据验证
      this._validateSpecData(specData)
      
      // 准备请求数据
      const requestData = {
        name: specData.name.trim(),
        voltage: Number(specData.voltage) || 0,
        capacity: Number(specData.capacity) || 0,
        weight: Number(specData.weight) || 0,
        dimensions: specData.dimensions?.trim() || '',
        price: Number(specData.price) || 0,
        description: specData.description?.trim() || '',
        imageUrl: specData.imageUrl?.trim() || '',
        categoryId: Number(specData.categoryId)
      }
      
      const response = await request.post('/api/battery-specs', requestData)
      console.log('✅ 成功创建规格，ID:', response.id)
      return response
    } catch (error) {
      console.error('❌ 创建规格失败:', error)
      throw new Error(`创建规格失败: ${error.message}`)
    }
  }

  /**
   * 更新规格信息
   * @param {number} id - 规格ID
   * @param {Object} specData - 更新的规格数据
   * @returns {Promise<Object>} 更新后的规格信息
   */
  static async updateSpec(id, specData) {
    try {
      console.log('🔍 开始更新规格，ID:', id)
      
      // 数据验证
      this._validateSpecData(specData, false)
      
      // 准备请求数据
      const requestData = {
        name: specData.name.trim(),
        voltage: Number(specData.voltage) || 0,
        capacity: Number(specData.capacity) || 0,
        weight: Number(specData.weight) || 0,
        dimensions: specData.dimensions?.trim() || '',
        price: Number(specData.price) || 0,
        description: specData.description?.trim() || '',
        imageUrl: specData.imageUrl?.trim() || ''
      }
      
      const response = await request.put(`/api/battery-specs/${id}`, requestData)
      console.log('✅ 成功更新规格:', response.name)
      return response
    } catch (error) {
      console.error('❌ 更新规格失败:', error)
      throw new Error(`更新规格失败: ${error.message}`)
    }
  }

  /**
   * 删除规格
   * @param {number} id - 规格ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  static async deleteSpec(id) {
    try {
      console.log('🔍 开始删除规格，ID:', id)
      await request.delete(`/api/battery-specs/${id}`)
      console.log('✅ 成功删除规格，ID:', id)
      return true
    } catch (error) {
      console.error('❌ 删除规格失败:', error)
      throw new Error(`删除规格失败: ${error.message}`)
    }
  }

  /**
   * 验证规格数据
   * @private
   * @param {Object} specData - 规格数据
   * @param {boolean} [requireCategoryId=true] - 是否需要验证分类ID
   */
  static _validateSpecData(specData, requireCategoryId = true) {
    if (!specData) {
      throw new Error('规格数据不能为空')
    }
    
    if (!specData.name?.trim()) {
      throw new Error('规格名称不能为空')
    }
    
    if (specData.name.length > 100) {
      throw new Error('规格名称不能超过100个字符')
    }
    
    if (requireCategoryId && !specData.categoryId) {
      throw new Error('分类ID不能为空')
    }
    
    const price = Number(specData.price)
    if (isNaN(price) || price < 0) {
      throw new Error('价格必须是非负数')
    }
    
    if (price > 1000000) {
      throw new Error('价格不能超过1,000,000')
    }
  }
}

/**
 * 统一导出电池管理服务
 */
export default {
  category: BatteryCategoryService,
  spec: BatterySpecService
}
