using BatteryApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BatteryApi.Controllers
{
    [Route("api/store")]
    [ApiController]
    [Authorize]
    public class StoreProductController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<StoreProductController> _logger;

        public StoreProductController(ISqlSugarClient db, ILogger<StoreProductController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取门店商品库存
        /// </summary>
        /// <param name="storeId">门店ID</param>
        /// <param name="keyword">搜索关键词（可选）</param>
        /// <returns>门店商品库存列表</returns>
        [HttpGet("{storeId}/product-inventory")]
        [Authorize]
        public async Task<IActionResult> GetStoreProductInventory(int storeId, string keyword = null)
        {
            try
            {
                _logger.LogInformation("获取门店商品库存，门店ID：{StoreId}，关键词：{Keyword}", storeId, keyword);

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == storeId);
                if (store == null)
                {
                    return NotFound(new { message = $"门店ID {storeId} 不存在" });
                }

                // 获取所有电池/商品列表
                var allProducts = await _db.Queryable<Product>().ToListAsync();

                // 获取门店商品库存
                var query = _db.Queryable<StoreInventory>()
                    .Where(i => i.StoreId == storeId);

                // 如果有关键词，则过滤
                if (!string.IsNullOrEmpty(keyword) && keyword != "empty")
                {
                    // 获取所有电池ID
                    var batteryIds = await query.Select(i => i.ProductId).ToListAsync();

                    // 查找匹配关键词的商品
                    var matchedProductIds = await _db.Queryable<Product>()
                        .Where(p => batteryIds.Contains(p.Id))
                        .Where(p => p.Model != null && p.Model.Contains(keyword))
                        .Select(p => p.Id)
                        .ToListAsync();

                    // 过滤库存
                    query = query.Where(i => matchedProductIds.Contains(i.ProductId));
                }

                var inventoryItems = await query.ToListAsync();

                // 创建结果列表
                var inventory = new List<StoreProductInventoryDto>();

                // 将库存项转换为DTO
                foreach (var item in inventoryItems)
                {
                    var product = allProducts.FirstOrDefault(p => p.Id == item.ProductId);
                    if (product != null)
                    {
                        inventory.Add(new StoreProductInventoryDto
                        {
                            Id = item.Id,
                            StoreId = item.StoreId,
                            ProductId = item.ProductId,
                            ProductName = product.Model,
                            ProductSpec = $"{product.Voltage}V{product.Capacity}Ah",
                            CategoryId = product.CategoryId,
                            Quantity = item.Quantity,
                            AvailableQuantity = item.AvailableQuantity,
                            UpdatedAt = item.UpdatedAt
                        });
                    }
                }

                // 记录返回的库存数量
                _logger.LogInformation("返回门店 {StoreId} 的商品库存，共 {Count} 条记录", storeId, inventory.Count);

                return Ok(inventory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取门店商品库存失败");
                return StatusCode(500, new { message = "获取门店商品库存失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 为门店分配商品
        /// </summary>
        /// <param name="storeId">门店ID</param>
        /// <param name="request">分配请求</param>
        /// <returns>分配结果</returns>
        [HttpPost("{storeId}/assign-products")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignProductsToStore(int storeId, [FromBody] AssignProductsRequest request)
        {
            try
            {
                _logger.LogInformation("为门店分配商品，门店ID：{StoreId}，商品数量：{ProductCount}",
                    storeId, request?.Products?.Count ?? 0);

                // 验证参数
                if (request == null || request.Products == null || !request.Products.Any())
                {
                    return BadRequest(new { message = "请提供有效的商品数据" });
                }

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == storeId);
                if (store == null)
                {
                    return NotFound(new { message = $"门店ID {storeId} 不存在" });
                }

                // 开始事务
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    // 处理每个商品
                    var assignedProducts = new List<AssignedProductDto>();
                    foreach (var product in request.Products)
                    {
                        // 检查商品是否存在
                        var productEntity = await _db.Queryable<Product>().FirstAsync(p => p.Id == product.ProductId);
                        if (productEntity == null)
                        {
                            _logger.LogWarning("分配商品时找不到商品ID：{ProductId}", product.ProductId);
                            continue;
                        }

                        // 检查门店库存是否存在
                        var inventory = await _db.Queryable<StoreInventory>()
                            .FirstAsync(i => i.StoreId == storeId && i.ProductId == product.ProductId);

                        if (inventory == null)
                        {
                            // 创建新库存记录
                            inventory = new StoreInventory
                            {
                                StoreId = storeId,
                                ProductId = product.ProductId,
                                Quantity = product.Quantity,
                                AvailableQuantity = product.Quantity,
                                UpdatedAt = DateTime.Now
                            };
                            await _db.Insertable(inventory).ExecuteCommandAsync();

                            _logger.LogInformation("为门店 {StoreId} 创建新商品 {ProductId} 库存记录，数量：{Quantity}",
                                storeId, product.ProductId, product.Quantity);
                        }
                        else
                        {
                            // 更新现有库存
                            var oldQuantity = inventory.Quantity;
                            inventory.Quantity += product.Quantity;
                            inventory.AvailableQuantity += product.Quantity;
                            inventory.UpdatedAt = DateTime.Now;
                            await _db.Updateable(inventory).ExecuteCommandAsync();

                            _logger.LogInformation("更新门店 {StoreId} 商品 {ProductId} 库存，原数量：{OldQuantity}，新数量：{NewQuantity}",
                                storeId, product.ProductId, oldQuantity, inventory.Quantity);
                        }

                        // 记录分配历史
                        var assignRecord = new ProductAssignRecord
                        {
                            StoreId = storeId,
                            ProductId = product.ProductId,
                            Quantity = product.Quantity,
                            AssignedBy = User.Identity.Name ?? "admin",
                            AssignTime = DateTime.Now
                        };
                        await _db.Insertable(assignRecord).ExecuteCommandAsync();

                        // 添加到返回结果
                        assignedProducts.Add(new AssignedProductDto
                        {
                            ProductId = product.ProductId,
                            ProductName = battery.Model, // 使用 Model 替代 Name
                            Spec = $"{battery.Voltage}V{battery.Capacity}Ah", // 使用电压容量规格
                            Quantity = product.Quantity,
                            AssignTime = assignRecord.AssignTime
                        });
                    }

                    return assignedProducts;
                });

                if (!result.IsSuccess)
                {
                    throw result.ErrorException;
                }

                var assignedProducts = result.Data as List<AssignedProductDto>;

                // 记录成功信息
                _logger.LogInformation("成功为门店 {StoreId} 分配 {Count} 个商品", storeId, assignedProducts.Count);

                // 返回成功响应
                return Ok(new
                {
                    success = true,
                    message = "商品分配成功",
                    data = new
                    {
                        storeId,
                        storeName = store.Name,
                        assignedProducts,
                        assignTime = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为门店分配商品失败");
                return StatusCode(500, new { message = "为门店分配商品失败", error = ex.Message });
            }
        }
    }

    /// <summary>
    /// 分配商品请求
    /// </summary>
    public class AssignProductsRequest
    {
        /// <summary>
        /// 商品列表
        /// </summary>
        public List<ProductAssignment> Products { get; set; }
    }

    /// <summary>
    /// 商品分配项
    /// </summary>
    public class ProductAssignment
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
    }

    /// <summary>
    /// 已分配商品DTO
    /// </summary>
    public class AssignedProductDto
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Spec { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime AssignTime { get; set; }
    }

    /// <summary>
    /// 门店商品库存DTO
    /// </summary>
    public class StoreProductInventoryDto
    {
        /// <summary>
        /// 库存ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        public int StoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        public string ProductSpec { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 可用数量
        /// </summary>
        public int AvailableQuantity { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
}
