D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.csproj.AssemblyReference.cache
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.GeneratedMSBuildEditorConfig.editorconfig
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.AssemblyInfoInputs.cache
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.AssemblyInfo.cs
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.csproj.CoreCompileInputs.cache
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.MvcApplicationPartsAssemblyInfo.cs
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.MvcApplicationPartsAssemblyInfo.cache
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\appsettings.Development.json
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\appsettings.json
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.staticwebassets.endpoints.json
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\x86\leptonica-1.82.0.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\x86\tesseract50.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\x64\leptonica-1.82.0.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\x64\tesseract50.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.exe
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.deps.json
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.runtimeconfig.json
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BatteryApi.pdb
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.EndpointUtil.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.GatewaySpi.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.OpenApiClient.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.OpenApiUtil.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.SDK.Dysmsapi20170525.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.TeaUtil.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\AlibabaCloud.TeaXML.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Aliyun.Credentials.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\BCrypt.Net-Next.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.Data.SqlClient.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.Data.Sqlite.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.Identity.Client.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.Abstractions.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.Logging.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IdentityModel.Tokens.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.IO.RecyclableMemoryStream.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.OpenApi.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\MySqlConnector.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Newtonsoft.Json.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Npgsql.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Oracle.ManagedDataAccess.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SixLabors.ImageSharp.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SixLabors.ImageSharp.Web.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SQLitePCLRaw.batteries_v2.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SQLitePCLRaw.core.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SQLitePCLRaw.provider.e_sqlite3.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\SqlSugar.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\DmProvider.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Kdbndp.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.Swagger.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Configuration.ConfigurationManager.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Diagnostics.PerformanceCounter.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.DirectoryServices.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.DirectoryServices.Protocols.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Drawing.Common.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.IdentityModel.Tokens.Jwt.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Runtime.Caching.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Security.Permissions.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\System.Windows.Extensions.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Tea.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\Tesseract.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\unix\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-arm\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-arm64\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-armel\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-mips64\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-s390x\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-x64\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux-x86\native\libe_sqlite3.so
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-arm\native\e_sqlite3.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-arm64\native\e_sqlite3.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-x64\native\e_sqlite3.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win-x86\native\e_sqlite3.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Diagnostics.PerformanceCounter.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.DirectoryServices.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\linux\lib\net6.0\System.DirectoryServices.Protocols.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\osx\lib\net6.0\System.DirectoryServices.Protocols.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.DirectoryServices.Protocols.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\netstandard2.0\System.Runtime.Caching.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Code\BatteryApi\BatteryApi\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\scopedcss\bundle\BatteryApi.styles.css
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets.build.json
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets.development.json
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets.build.endpoints.json
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets\msbuild.BatteryApi.Microsoft.AspNetCore.StaticWebAssets.props
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets\msbuild.BatteryApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets\msbuild.build.BatteryApi.props
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets\msbuild.buildMultiTargeting.BatteryApi.props
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets\msbuild.buildTransitive.BatteryApi.props
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\staticwebassets.pack.json
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.csproj.Up2Date
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.dll
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\refint\BatteryApi.dll
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.pdb
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\BatteryApi.genruntimeconfig.cache
D:\Code\BatteryApi\BatteryApi\obj\Debug\net6.0\ref\BatteryApi.dll
