/**
 * 地图API服务
 */
import request from '@/utils/request';
import { mapConfig } from '@/config/index';

// API接口
const MapAPI = {
  /**
   * 地址解析（地址转坐标）
   * @param {String} address 地址
   * @returns {Promise} Promise对象
   */
  geocodeAddress(address) {
    return request.get('/api/map/geocode', { address }).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 逆地址解析（坐标转地址）
   * @param {Number} latitude 纬度
   * @param {Number} longitude 经度
   * @returns {Promise} Promise对象
   */
  reverseGeocode(latitude, longitude) {
    return request.get('/api/map/reverse-geocode', { latitude, longitude }).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 关键词搜索
   * @param {String} keyword 关键词
   * @param {String} region 地区，默认为"全国"
   * @returns {Promise} Promise对象
   */
  searchPlace(keyword, region = '全国') {
    return request.get('/api/map/search', { keyword, region }).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 周边搜索
   * @param {String} keyword 关键词
   * @param {Number} latitude 中心点纬度，如果为null或undefined则使用默认位置
   * @param {Number} longitude 中心点经度，如果为null或undefined则使用默认位置
   * @param {Number} radius 搜索半径，单位：米，默认为5000
   * @returns {Promise} Promise对象
   */
  searchNearby(keyword, latitude, longitude, radius = 5000) {
    // 确保使用默认位置处理undefined和null值
    const lat = latitude !== undefined && latitude !== null ? latitude : mapConfig.defaultLocation.latitude;
    const lng = longitude !== undefined && longitude !== null ? longitude : mapConfig.defaultLocation.longitude;

    return request.get('/api/map/nearby', {
      keyword,
      latitude: lat,
      longitude: lng,
      radius
    }).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * IP定位
   * @param {String} ip IP地址，可选
   * @returns {Promise} Promise对象
   */
  getLocationByIp(ip = null) {
    const params = {};
    if (ip) {
      params.ip = ip;
    }

    return request.get('/api/map/ip-location', params).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 网络定位（智能硬件定位）
   * @param {Object} networkInfo 网络信息，包含基站信息和WIFI信息
   * @returns {Promise} Promise对象
   */
  getLocationByNetwork(networkInfo) {
    return request.post('/api/map/network-location', networkInfo).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 获取当前位置
   * @returns {Promise} Promise对象
   */
  getCurrentLocation() {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          const { latitude, longitude } = res;
          resolve({ latitude, longitude });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          reject(new Error('获取位置失败，请检查定位权限'));
        }
      });
    });
  },

  /**
   * 获取地图静态图片URL
   * @param {Number} latitude 纬度
   * @param {Number} longitude 经度
   * @param {Number} zoom 缩放级别，默认为14
   * @param {Number} width 图片宽度，默认为300
   * @param {Number} height 图片高度，默认为200
   * @returns {String} 静态图片URL
   */
  getStaticMapUrl(latitude, longitude, zoom = 14, width = 300, height = 200) {
    return `${mapConfig.baseUrl}/ws/staticmap/v2/?key=${mapConfig.key}&center=${longitude},${latitude}&zoom=${zoom}&size=${width}*${height}&scale=2&markers=color:red|${longitude},${latitude}`;
  },

  /**
   * 直接调用腾讯地图API进行地址搜索
   * @param {String} keyword 关键词
   * @param {String} region 地区，默认为"全国"
   * @returns {Promise} Promise对象
   */
  searchPlaceDirect(keyword, region = '全国') {
    return new Promise((resolve, reject) => {
      uni.request({
        url: `${mapConfig.baseUrl}/ws/place/v1/suggestion`,
        method: 'GET',
        data: {
          key: mapConfig.key,
          keyword,
          region,
          region_fix: 0,
          policy: 1,
          output: 'json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 0) {
            resolve({
              code: 0,
              message: 'success',
              data: {
                data: res.data.data.map(item => ({
                  title: item.title,
                  address: item.address,
                  location: {
                    lat: item.location.lat,
                    lng: item.location.lng
                  }
                }))
              }
            });
          } else {
            reject({
              code: res.data ? res.data.status : -1,
              message: res.data ? res.data.message : '搜索失败',
              error: res.data ? res.data.message : '未知错误'
            });
          }
        },
        fail: (err) => {
          console.error('搜索地点失败:', err);
          reject({
            code: -1,
            message: '搜索失败',
            error: err.errMsg || '网络请求失败'
          });
        }
      });
    });
  },

  /**
   * 直接调用腾讯地图API进行逆地址解析
   * @param {Number} latitude 纬度，如果为null或undefined则使用默认位置
   * @param {Number} longitude 经度，如果为null或undefined则使用默认位置
   * @returns {Promise} Promise对象
   */
  reverseGeocodeDirect(latitude, longitude) {
    // 确保使用默认位置处理undefined和null值
    const lat = latitude !== undefined && latitude !== null ? latitude : mapConfig.defaultLocation.latitude;
    const lng = longitude !== undefined && longitude !== null ? longitude : mapConfig.defaultLocation.longitude;

    return new Promise((resolve, reject) => {
      uni.request({
        url: `${mapConfig.baseUrl}/ws/geocoder/v1/`,
        method: 'GET',
        data: {
          key: mapConfig.key,
          location: `${lat},${lng}`,
          output: 'json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 0) {
            resolve({
              code: 0,
              message: 'success',
              data: {
                result: {
                  address: res.data.result.address,
                  formatted_addresses: res.data.result.formatted_addresses
                }
              }
            });
          } else {
            reject({
              code: res.data ? res.data.status : -1,
              message: '逆地址解析失败',
              error: res.data ? res.data.message : '未知错误'
            });
          }
        },
        fail: (err) => {
          console.error('逆地址解析失败:', err);
          reject({
            code: -1,
            message: '逆地址解析失败',
            error: err.errMsg || '网络请求失败'
          });
        }
      });
    });
  },

  /**
   * 地址联想（输入提示）
   * @param {String} keyword 关键词
   * @param {String} region 地区，默认为"全国"
   * @param {Number} regionFix 是否固定区域，默认为0
   * @param {Number} policy 返回行政区划信息，默认为1
   * @returns {Promise} Promise对象
   */
  getSuggestion(keyword, region = '全国', regionFix = 0, policy = 1) {
    return new Promise((resolve, reject) => {
      uni.request({
        url: `${mapConfig.baseUrl}/ws/place/v1/suggestion`,
        method: 'GET',
        data: {
          key: mapConfig.key,
          keyword,
          region,
          region_fix: regionFix,
          policy: policy,
          output: 'json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 0) {
            resolve({
              code: 0,
              message: 'success',
              data: {
                data: res.data.data.map(item => ({
                  title: item.title,
                  address: item.address || item.title,
                  location: item.location || {},
                  adcode: item.adcode,
                  province: item.province,
                  city: item.city,
                  district: item.district
                }))
              }
            });
          } else {
            reject({
              code: res.data ? res.data.status : -1,
              message: res.data ? res.data.message : '地址联想失败',
              error: res.data ? res.data.message : '未知错误'
            });
          }
        },
        fail: (err) => {
          console.error('地址联想失败:', err);
          reject({
            code: -1,
            message: '地址联想失败',
            error: err.errMsg || '网络请求失败'
          });
        }
      });
    });
  },

  /**
   * 地点搜索API
   * @param {String} keyword 关键词
   * @param {String} region 地区，默认为"全国"
   * @returns {Promise} Promise对象
   */
  searchPlace(keyword, region = '全国') {
    return new Promise((resolve, reject) => {
      uni.request({
        url: `${mapConfig.baseUrl}/ws/place/v1/suggestion`,
        method: 'GET',
        data: {
          key: mapConfig.key,
          keyword,
          region,
          region_fix: 0,
          policy: 1,
          output: 'json'
        },
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.status === 0) {
            resolve({
              code: 0,
              message: 'success',
              data: {
                count: res.data.count,
                data: res.data.data.map(item => ({
                  id: item.id,
                  title: item.title,
                  address: item.address,
                  category: item.category,
                  location: item.location,
                  adInfo: item.ad_info,
                  tel: item.tel,
                  distance: item.distance
                }))
              }
            });
          } else {
            reject({
              code: res.data ? res.data.status : -1,
              message: res.data ? res.data.message : '地点搜索失败',
              error: res.data ? res.data.message : '未知错误'
            });
          }
        },
        fail: (err) => {
          console.error('地点搜索失败:', err);
          reject({
            code: -1,
            message: '地点搜索失败',
            error: err.errMsg || '网络请求失败'
          });
        }
      });
    });
  }
};

export default MapAPI;
