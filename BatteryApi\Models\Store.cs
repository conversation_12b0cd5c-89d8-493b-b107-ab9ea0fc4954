using SqlSugar;
using SqlSugar.Extensions;

namespace BatteryApi.Models;

[SugarTable("Stores")]
public class Store
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 100)]
    public string Name { get; set; }

    [SugarColumn(Length = 200)]
    public string Address { get; set; }

    [SugarColumn(Length = 20)]
    public string Phone { get; set; }

    [SugarColumn(Length = 100, IsNullable = true)]
    public string? Contact { get; set; }

    public double Latitude { get; set; }

    public double Longitude { get; set; }

    [SugarColumn(Length = 50)]
    public string Status { get; set; } = "Active"; // Active, Inactive, Maintenance

    [SugarColumn(Length = 500)]
    public string Description { get; set; }

    [SugarColumn(Length = 500)]
    public string BusinessHours { get; set; }

    [SugarColumn(Length = 200)]
    public string ImageUrl { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // 软删除相关字段
    [SugarColumn(IsNullable = true)]
    public DateTime? DeletedAt { get; set; }

    [SugarColumn(IsNullable = true, Length = 500)]
    public string? DeleteReason { get; set; }

    [SugarColumn(IsNullable = true, Length = 100)]
    public string? DeletedBy { get; set; }

    [SugarColumn(IsNullable = false)]
    public bool IsDeleted { get; set; } = false;

    [Navigate(NavigateType.OneToMany, nameof(StoreInventory.StoreId))]
    public List<StoreInventory> Inventory { get; set; }
}

[SugarTable("StoreInventory")]
public class StoreInventory
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(IsNullable = false)]
    public int StoreId { get; set; }

    [SugarColumn(IsNullable = false)]
    public int ProductId { get; set; }

    [SugarColumn(IsNullable = false)]
    public int Quantity { get; set; } = 0;

    [SugarColumn(IsNullable = false)]
    public int AvailableQuantity { get; set; } = 0;

    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    [Navigate(NavigateType.OneToOne, nameof(StoreId))]
    public Store Store { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(ProductId))]
    public Product Product { get; set; }
}
