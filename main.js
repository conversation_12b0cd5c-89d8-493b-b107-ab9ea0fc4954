import Vue from 'vue'
import App from './App'
import store from './store'

// 引入全局工具函数
import * as utils from './utils/common.js'
import formatUtils from './utils/formatUtils.js'
import request from './utils/request'
import toast from './utils/toast'

// 引入路由拦截器
import routerGuard from './utils/router-guard'

// 引入全局混入
import globalMixin from './mixins/global'

// 引入警告处理插件
import warningHandler from './plugins/warning-handler'

// 引入API
import batteryAPI from './api/battery'
import productAPI from './api/product'
import storeAPI from './api/store'
// main.js
import uView from "uview-ui";
Vue.use(uView);
Vue.use(warningHandler);
// 引入uni-popup组件
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
Vue.component('uni-popup', uniPopup)

// 引入uni-transition组件
import uniTransition from '@/uni_modules/uni-transition/components/uni-transition/uni-transition.vue'
Vue.component('uni-transition', uniTransition)

// 引入页面包装组件
import PageWrapper from '@/components/common/page-wrapper.vue'
Vue.component('page-wrapper', PageWrapper)

// 挂载工具函数到全局
Vue.prototype.$utils = {
  ...utils,
  ...formatUtils
}

// 挂载请求工具到全局
Vue.prototype.$request = request

// 挂载API到全局
Vue.prototype.$api = {
  battery: batteryAPI,
  product: productAPI,
  store: storeAPI
}

// 挂载Toast工具到全局
Vue.prototype.$toast = toast

// 设置为 false 以阻止 Vue 在启动时生成生产提示
Vue.config.productionTip = false

// 注册全局混入
Vue.mixin(globalMixin)

// 全局方法混入
Vue.mixin({
  methods: {
    // 跳转页面
    navTo(url, params = {}) {
      let query = ''
      if (Object.keys(params).length > 0) {
        query = '?' + Object.keys(params)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&')
      }
      uni.navigateTo({
        url: url + query
      })
    },
    // 返回上一页
    navBack(delta = 1) {
      uni.navigateBack({
        delta
      })
    },
    // 显示消息提示（使用全局Toast工具）
    handleShowToast(title, type = 'default') {
      this.$toast.show(title, type)
    },
    // 显示加载中（使用全局变量控制uView的loading组件）
    showLoading(title = '加载中') {
      // 获取当前页面
      const pages = getCurrentPages();
      const page = pages[pages.length - 1];

      // 如果页面存在且有loading属性，则使用页面的loading
      if (page && page.$data && page.$data.loading !== undefined) {
        page.$data.loadingText = title;
        page.$data.loading = true;
      } else {
        // 否则使用uni.showLoading作为备选方案
        uni.showLoading({
          title,
          mask: true
        });
      }
    },
    // 隐藏加载中
    hideLoading() {
      // 获取当前页面
      const pages = getCurrentPages();
      const page = pages[pages.length - 1];

      // 如果页面存在且有loading属性，则使用页面的loading
      if (page && page.$data && page.$data.loading !== undefined) {
        page.$data.loading = false;
      } else {
        // 否则使用uni.hideLoading作为备选方案
        uni.hideLoading();
      }
    }
  }
})

App.mpType = 'app'

// 添加路由拦截器
// 拦截跳转方法
const routerMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
routerMethods.forEach(method => {
  const original = uni[method]
  uni[method] = function(options) {
    const result = routerGuard(options)
    if (result) {
      return original.call(this, options)
    }
  }
})

// 添加页面路由拦截
uni.addInterceptor('navigateTo', {
  invoke(args) {
    return routerGuard(args)
  }
})
uni.addInterceptor('redirectTo', {
  invoke(args) {
    return routerGuard(args)
  }
})
uni.addInterceptor('reLaunch', {
  invoke(args) {
    return routerGuard(args)
  }
})
uni.addInterceptor('switchTab', {
  invoke(args) {
    return routerGuard(args)
  }
})

// 添加页面不存在拦截
uni.addInterceptor('navigateTo', {
  fail(err) {
    console.error('页面跳转失败:', err)
    // 如果是页面不存在错误
    if (err.errMsg.includes('page "') && err.errMsg.includes('" is not found')) {
      uni.showToast({
        title: '页面不存在，即将跳转到登录页',
        icon: 'none',
        duration: 2000
      })

      // 延迟跳转，确保提示显示
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }, 1000)
    }
  }
})

const app = new Vue({
  store,
  ...App
})
app.$mount()