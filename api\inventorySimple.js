import request from '@/utils/request';

/**
 * 简化版库存管理 API
 */
export default {
  /**
   * 获取门店库存
   * @param {Number} storeId 门店ID
   * @param {Boolean} forceRefresh 是否强制刷新
   * @returns {Promise} Promise对象
   */
  getStoreInventory(storeId, forceRefresh = false) {
    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    // 确保 storeId 是有效的数字
    const numericStoreId = parseInt(storeId, 10);
    if (isNaN(numericStoreId) || numericStoreId <= 0) {
      return Promise.reject(new Error(`无效的门店ID: ${storeId}`));
    }

    // 添加时间戳，避免浏览器缓存
    const params = {
      _t: Date.now()
      // 不添加keyword参数，因为空值会导致验证错误
    };

    if (forceRefresh) {
      params.forceRefresh = true;
    }

    console.log(`获取门店库存: 门店ID=${numericStoreId}, 强制刷新=${forceRefresh}`);
    return request.get(`/api/store-inventory/${numericStoreId}`, params);
  },

  /**
   * 更新门店商品库存
   * @param {Number} storeId 门店ID
   * @param {Number} productId 商品ID
   * @param {Number} quantity 新数量
   * @returns {Promise} Promise对象
   */
  updateInventory(storeId, productId, quantity) {
    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    if (!productId) {
      return Promise.reject(new Error('缺少商品ID'));
    }

    if (quantity === undefined || quantity === null) {
      return Promise.reject(new Error('缺少数量'));
    }

    // 确保参数是有效的数字
    const numericStoreId = parseInt(storeId, 10);
    const numericProductId = parseInt(productId, 10);
    const numericQuantity = parseInt(quantity, 10);

    if (isNaN(numericStoreId) || numericStoreId <= 0) {
      return Promise.reject(new Error(`无效的门店ID: ${storeId}`));
    }

    if (isNaN(numericProductId) || numericProductId <= 0) {
      return Promise.reject(new Error(`无效的商品ID: ${productId}`));
    }

    if (isNaN(numericQuantity) || numericQuantity < 0) {
      return Promise.reject(new Error(`无效的数量: ${quantity}`));
    }

    // 构建请求数据
    const requestData = {
      products: [{
        productId: numericProductId,
        quantity: numericQuantity
      }]
    };

    // 添加时间戳，避免缓存
    const timestamp = Date.now();

    console.log(`发送库存更新请求: 门店ID=${numericStoreId}, 商品ID=${numericProductId}, 数量=${numericQuantity}`);
    return request.post(`/api/store-inventory/${numericStoreId}/update?_t=${timestamp}`, requestData);
  },

  /**
   * 批量更新门店商品库存
   * @param {Number} storeId 门店ID
   * @param {Array} products 商品列表，每个商品包含 productId 和 quantity
   * @returns {Promise} Promise对象
   */
  batchUpdateInventory(storeId, products) {
    if (!storeId) {
      return Promise.reject(new Error('缺少门店ID'));
    }

    if (!products || !Array.isArray(products) || products.length === 0) {
      return Promise.reject(new Error('缺少商品数据'));
    }

    // 确保门店ID是有效的数字
    const numericStoreId = parseInt(storeId, 10);
    if (isNaN(numericStoreId) || numericStoreId <= 0) {
      return Promise.reject(new Error(`无效的门店ID: ${storeId}`));
    }

    // 验证并处理每个商品的数据
    const validProducts = [];
    const invalidProducts = [];

    for (const p of products) {
      try {
        const numericProductId = parseInt(p.productId, 10);
        const numericQuantity = parseInt(p.quantity, 10);

        if (isNaN(numericProductId) || numericProductId <= 0) {
          invalidProducts.push({ ...p, reason: `无效的商品ID: ${p.productId}` });
          continue;
        }

        if (isNaN(numericQuantity) || numericQuantity < 0) {
          invalidProducts.push({ ...p, reason: `无效的数量: ${p.quantity}` });
          continue;
        }

        validProducts.push({
          productId: numericProductId,
          quantity: numericQuantity
        });
      } catch (error) {
        invalidProducts.push({ ...p, reason: error.message || '数据处理错误' });
      }
    }

    // 如果没有有效的商品，返回错误
    if (validProducts.length === 0) {
      console.error('批量更新库存失败: 没有有效的商品数据', invalidProducts);
      return Promise.reject(new Error('没有有效的商品数据'));
    }

    // 如果有无效的商品，记录警告
    if (invalidProducts.length > 0) {
      console.warn('批量更新库存: 忽略无效的商品数据', invalidProducts);
    }

    // 构建请求数据
    const requestData = {
      products: validProducts
    };

    // 添加时间戳，避免缓存
    const timestamp = Date.now();

    console.log(`发送批量库存更新请求: 门店ID=${numericStoreId}, 商品数量=${validProducts.length}`);
    return request.post(`/api/store-inventory/${numericStoreId}/update?_t=${timestamp}`, requestData);
  }
};
