/**
 * 权限控制模块
 */
import store from '@/store'

// 不需要登录的页面白名单
const whiteList = [
  '/pages/login/login',
  '/pages/login/register',
  '/pages/login/reset-password',
  '/pages/login/verify-code'
]

/**
 * 全局导航守卫
 * @param {Object} options 页面参数
 */
function setupNavigationGuard() {
  // 页面跳转前拦截
  const onPageNavigationStart = (options) => {
    const { url } = options
    console.log('页面跳转拦截:', url)

    // 获取当前页面路径
    const path = '/' + url.split('?')[0]
    console.log('当前页面路径:', path)

    // 检查是否是白名单页面
    const isWhiteListPage = whiteList.includes(path) || whiteList.some(item => path.startsWith(item))
    console.log('是否是白名单页面:', isWhiteListPage)

    // 如果是白名单页面，允许直接访问
    if (isWhiteListPage) {
      console.log('白名单页面，允许访问')
      return true
    }

    // 检查用户是否已登录
    const isLoggedIn = store.getters['user/isLoggedIn']
    console.log('用户登录状态:', isLoggedIn)

    // 如果用户未登录且不在白名单中，跳转到登录页
    if (!isLoggedIn) {
      console.log('用户未登录，跳转到登录页')

      // 使用 setTimeout 避免可能的循环重定向
      setTimeout(() => {
        uni.redirectTo({
          url: '/pages/login/login'
        })
      }, 100)

      return false
    }

    // 如果用户已登录且访问登录页，跳转到首页
    if (isLoggedIn && path === '/pages/login/login') {
      console.log('用户已登录，跳转到首页')
      const userRole = store.getters['user/userRole']

      // 使用 setTimeout 避免可能的循环重定向
      setTimeout(() => {
        // 根据用户角色跳转到不同的首页
        if (userRole === 'Admin') {
          uni.switchTab({
            url: '/pages/admin/product/list'
          })
        } else if (userRole === 'store') {
          uni.switchTab({
            url: '/pages/store/inventory'
          })
        } else {
          uni.switchTab({
            url: '/pages/buy/index'
          })
        }
      }, 100)

      return false
    }

    return true
  }

  // 监听页面跳转
  uni.addInterceptor('navigateTo', {
    invoke(args) {
      return onPageNavigationStart(args)
    }
  })

  uni.addInterceptor('redirectTo', {
    invoke(args) {
      return onPageNavigationStart(args)
    }
  })

  uni.addInterceptor('reLaunch', {
    invoke(args) {
      return onPageNavigationStart(args)
    }
  })

  uni.addInterceptor('switchTab', {
    invoke(args) {
      return onPageNavigationStart(args)
    }
  })
}

export default {
  setupNavigationGuard
}
