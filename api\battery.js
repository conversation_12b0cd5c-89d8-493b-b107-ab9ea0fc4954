/**
 * 电池API服务
 */
import request from '@/utils/request';
import { BatteryValidator } from '@/utils/validators/BatteryValidators';

// 是否使用模拟数据的标志
const useMockData = false;

// API接口
const BatteryAPI = {
  /**
   * 获取电池列表（简化版，用于BMS监控）
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getBatteryList(params = { page: 1, pageSize: 20 }) {
    console.log('获取电池列表（BMS监控），参数:', params)

    // 转换参数格式
    const apiParams = {
      pageNumber: params.page || 1,
      pageSize: params.pageSize || 20
    }

    return request.get('/api/batteries', apiParams).then(response => {
      console.log('电池列表响应:', response)

      // 处理响应数据
      let batteries = []

      if (response && response.items && Array.isArray(response.items)) {
        batteries = response.items.map(item => ({
          id: item.id,
          code: item.serialNumber || item.code || `BAT-${item.id}`,
          serialNumber: item.serialNumber,
          categoryName: item.categoryName,
          status: item.status,
          price: item.price,
          rentPrice: item.rentPrice,
          manufactureDate: item.manufactureDate,
          manufacturer: item.manufacturer
        }))
      } else if (Array.isArray(response)) {
        batteries = response.map(item => ({
          id: item.id,
          code: item.serialNumber || item.code || `BAT-${item.id}`,
          serialNumber: item.serialNumber,
          categoryName: item.categoryName,
          status: item.status,
          price: item.price,
          rentPrice: item.rentPrice,
          manufactureDate: item.manufactureDate,
          manufacturer: item.manufacturer
        }))
      }

      return {
        code: 0,
        message: 'success',
        data: batteries
      }
    }).catch(error => {
      console.error('获取电池列表失败:', error)
      return {
        code: -1,
        message: error.message || '获取电池列表失败',
        data: []
      }
    })
  },

  /**
   * 获取电池列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getBatteries(params = { pageNumber: 1, pageSize: 15 }) {

    // 将 page 参数转换为 pageNumber，以匹配后端 API 的参数名称
    const apiParams = { ...params };
    if (apiParams.page) {
      apiParams.pageNumber = apiParams.page;
      delete apiParams.page;
    }

    // 处理关键词搜索参数
    if (apiParams.keyword !== undefined && apiParams.keyword !== null) {
      // 同时设置多个可能的参数名称，以确保兼容不同的后端API
      apiParams.SearchTerm = apiParams.keyword;
      apiParams.searchTerm = apiParams.keyword;
      apiParams.search = apiParams.keyword;
      // 保留 keyword 参数，因为某些后端 API 可能使用 keyword
      console.log('设置搜索参数:', apiParams.keyword);
    }

    // 处理筛选参数
    // 处理规格筛选
    if (apiParams.specIds && Array.isArray(apiParams.specIds) && apiParams.specIds.length > 0) {
      apiParams.specIds = apiParams.specIds.join(',');
    }

    // 处理状态筛选
    if (apiParams.statusIds && Array.isArray(apiParams.statusIds) && apiParams.statusIds.length > 0) {
      apiParams.statusIds = apiParams.statusIds.join(',');
    }

    // 处理剩余寿命筛选
    if (apiParams.lifeLevel) {
      switch (apiParams.lifeLevel) {
        case 'high':
          apiParams.minLifePercentage = 75;
          break;
        case 'medium':
          apiParams.minLifePercentage = 50;
          apiParams.maxLifePercentage = 75;
          break;
        case 'low':
          apiParams.maxLifePercentage = 50;
          break;
      }
      delete apiParams.lifeLevel;
    }

    console.log('处理后的筛选参数:', apiParams);

    console.log('发送请求到 /api/batteries，参数:', apiParams);
    return request.get('/api/batteries', apiParams).then(response => {
      console.log('获取电池列表原始响应:', JSON.stringify(response));
      console.log('响应类型:', typeof response, '响应结构:', response ? Object.keys(response) : 'null');

      // 检查响应中的电池数据
      if (response && response.items && response.items.length > 0) {
        console.log('第一个电池数据:', JSON.stringify(response.items[0]));
        console.log('电池价格:', response.items[0].price);
        console.log('电池日租金:', response.items[0].rentPrice);
        console.log('电池描述:', response.items[0].description);
        console.log('电池类别 ID:', response.items[0].categoryId);
      }

      // 将API响应数据转换为前端需要的格式
      let batteries = [];
      let totalItems = 0;
      let pageNumber = apiParams.pageNumber || 1;
      let pageSize = apiParams.pageSize || 15;
      let totalPages = 0;
      let hasNextPage = false;
      let hasPreviousPage = false;

      try {
        // 处理不同的响应格式
        if (response && Array.isArray(response)) {
          // 如果响应是数组，直接使用
          console.log('响应是数组，长度:', response.length);

          batteries = response.map(item => ({
            id: item.id,
            code: item.serialNumber || item.code || `BAT-${item.id}`,
            spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
            categoryCode: item.categoryCode || item.categoryId,
            status: this._mapStatusFromApi(item.status),
            price: item.price || 0,
            rentPrice: item.rentPrice || 0,
            manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
            lifespan: item.lifespan || 36,
            remainingLife: item.remainingLife || (item.healthPercentage ? Math.round(item.healthPercentage / 100 * 36) : 30),
            description: item.description || '',
            notes: item.notes || '',
            manufacturer: item.manufacturer || ''
          }));

          totalItems = response.length;
          totalPages = 1;
        } else if (response && response.items && Array.isArray(response.items)) {
          // 如果响应有 items 属性且是数组
          console.log('响应有 items 属性，长度:', response.items.length);

          // 将 items 数组转换为前端需要的格式
          batteries = response.items.map(item => {
            // 处理价格字段，确保它们是数字
            let price = 0;
            let rentPrice = 0;

            // 尝试将价格转换为数字
            if (item.price !== undefined && item.price !== null) {
              price = parseFloat(item.price);
              if (isNaN(price)) price = 0;
            }

            if (item.rentPrice !== undefined && item.rentPrice !== null) {
              rentPrice = parseFloat(item.rentPrice);
              if (isNaN(rentPrice)) rentPrice = 0;
            }

            // 处理类别 ID 和名称
            let categoryId = null;
            let categoryName = '';
            let categoryCode = '';

            if (item.categoryId !== undefined && item.categoryId !== null) {
              categoryId = item.categoryId;
            }

            if (item.categoryName !== undefined && item.categoryName !== null) {
              categoryName = item.categoryName;
            }

            if (item.categoryCode !== undefined && item.categoryCode !== null) {
              categoryCode = item.categoryCode;
            }

            // 处理描述字段
            let description = '';
            if (item.description !== undefined && item.description !== null) {
              description = item.description;
            }

            // 处理生产日期
            let manufactureDate = '未知';
            if (item.manufactureDate) {
              try {
                // 如果是带有时区信息的日期字符串，先提取日期部分
                let dateStr = item.manufactureDate;
                if (typeof dateStr === 'string' && dateStr.includes('T')) {
                  dateStr = dateStr.split('T')[0];
                }

                // 尝试将日期字符串转换为 Date 对象
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                  // 如果是有效的日期，直接使用提取的日期部分
                  manufactureDate = dateStr;
                  console.log('原始生产日期:', item.manufactureDate, '格式化后:', manufactureDate);
                }
              } catch (e) {
                console.error('处理生产日期出错:', e);
              }
            }

            return {
              id: item.id,
              code: item.serialNumber || item.code || `BAT-${item.id}`,
              spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
              categoryCode: categoryCode || item.categoryCode || categoryId,
              categoryId: categoryId,
              categoryName: categoryName,
              status: this._mapStatusFromApi(item.status),
              price: price,
              rentPrice: rentPrice,
              manufactureDate: manufactureDate,
              lifespan: item.lifespan || 36,
              remainingLife: item.remainingLife || (item.healthPercentage ? Math.round(item.healthPercentage / 100 * 36) : 30),
              description: description,
              notes: item.notes || '',
              manufacturer: item.manufacturer || '',
              model: item.model || ''
            };
          });

          totalItems = response.totalItems || response.items.length;
          pageNumber = response.pageNumber || apiParams.pageNumber || 1;
          pageSize = response.pageSize || apiParams.pageSize || 15;
          totalPages = response.totalPages || Math.ceil(totalItems / pageSize);
          hasNextPage = response.hasNextPage || pageNumber < totalPages;
          hasPreviousPage = response.hasPreviousPage || pageNumber > 1;
        } else if (response && response.data) {
          // 如果响应有 data 属性
          console.log('响应有 data 属性');

          if (Array.isArray(response.data)) {
            // 如果 data 是数组
            console.log('data 是数组，长度:', response.data.length);

            batteries = response.data.map(item => ({
              id: item.id,
              code: item.serialNumber || item.code || `BAT-${item.id}`,
              spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
              categoryCode: item.categoryCode || item.categoryId,
              status: this._mapStatusFromApi(item.status),
              price: item.price || 0,
              rentPrice: item.rentPrice || 0,
              manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
              lifespan: item.lifespan || 36,
              remainingLife: item.remainingLife || (item.healthPercentage ? Math.round(item.healthPercentage / 100 * 36) : 30),
              description: item.description || '',
              notes: item.notes || '',
              manufacturer: item.manufacturer || ''
            }));

            totalItems = response.data.length;
            totalPages = 1;
          } else if (response.data.batteries && Array.isArray(response.data.batteries)) {
            // 如果 data.batteries 是数组
            console.log('data.batteries 是数组，长度:', response.data.batteries.length);

            batteries = response.data.batteries.map(item => ({
              id: item.id,
              code: item.serialNumber || item.code || `BAT-${item.id}`,
              spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
              categoryCode: item.categoryCode || item.categoryId,
              status: this._mapStatusFromApi(item.status),
              price: item.price || 0,
              rentPrice: item.rentPrice || 0,
              manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
              lifespan: item.lifespan || 36,
              remainingLife: item.remainingLife || (item.healthPercentage ? Math.round(item.healthPercentage / 100 * 36) : 30),
              description: item.description || '',
              notes: item.notes || '',
              manufacturer: item.manufacturer || ''
            }));

            totalItems = response.data.totalItems || response.data.batteries.length;
            pageNumber = response.data.pageNumber || apiParams.pageNumber || 1;
            pageSize = response.data.pageSize || apiParams.pageSize || 15;
            totalPages = response.data.totalPages || Math.ceil(totalItems / pageSize);
            hasNextPage = response.data.hasNextPage || pageNumber < totalPages;
            hasPreviousPage = response.data.hasPreviousPage || pageNumber > 1;
          }
        }

        // 检查处理后的数据是否有效
        if (batteries.length === 0 && response) {
          console.warn('数据处理可能有问题，原始数据有内容但处理后为空');

          // 如果有 items 属性，尝试直接使用
          if (response.items && Array.isArray(response.items) && response.items.length > 0) {
            batteries = response.items;
            totalItems = response.totalItems || response.items.length;
            console.log('直接使用 items 数组:', batteries);
          }
          // 如果没有 items 属性但响应本身是数组
          else if (Array.isArray(response) && response.length > 0) {
            batteries = response;
            totalItems = response.length;
            console.log('直接使用响应数组:', batteries);
          }
        }
      } catch (error) {
        console.error('处理电池列表数据时出错:', error);
        // 如果处理出错，尝试直接使用原始数据
        if (response) {
          if (response.items && Array.isArray(response.items)) {
            batteries = response.items;
            totalItems = response.totalItems || response.items.length;
            console.log('处理出错，直接使用 items 数组:', batteries);
          } else if (Array.isArray(response)) {
            batteries = response;
            totalItems = response.length;
            console.log('处理出错，直接使用响应数组:', batteries);
          }
        }
      }

      console.log('处理后的电池列表:', batteries);

      return {
        code: 0,
        message: 'success',
        data: {
          batteries,
          totalItems,
          pageNumber,
          pageSize,
          totalPages,
          hasNextPage,
          hasPreviousPage
        }
      };
    }).catch(error => {
      console.error('获取电池列表失败:', error);
      return {
        code: -1,
        message: error.message || '获取电池列表失败',
        data: {
          batteries: [],
          totalItems: 0,
          pageNumber: apiParams.pageNumber || 1,
          pageSize: apiParams.pageSize || 15,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false
        }
      };
    });
  },

  /**
   * 获取电池统计信息
   * @returns {Promise} Promise对象
   */
  getBatteryStats() {

    return request.get('/api/batteries/stats').then(response => {
      // 将API响应数据转换为前端需要的格式
      if (response) {
        return {
          code: 0,
          message: 'success',
          data: {
            totalBatteries: response.totalBatteries || 0,
            availableBatteries: response.availableBatteries || 0,
            rentedBatteries: response.rentedBatteries || 0,
            soldBatteries: response.soldBatteries || 0,
            maintenanceBatteries: response.maintenanceBatteries || 0,
            categoryStats: response.categoryStats || [],
            monthlyRentals: response.monthlyRentals || []
          }
        };
      }

      return {
        code: 0,
        message: 'success',
        data: {
          totalBatteries: 0,
          availableBatteries: 0,
          rentedBatteries: 0,
          soldBatteries: 0,
          maintenanceBatteries: 0,
          categoryStats: [],
          monthlyRentals: []
        }
      };
    });
  },

  /**
   * 获取电池详情
   * @param {Number} id 电池ID
   * @returns {Promise} Promise对象
   */
  getBatteryDetail(id) {
    if (!id || id <= 0) {
      console.error('无效的电池 ID:', id);
      return Promise.reject(new Error('无效的电池 ID'));
    }

    console.log(`发送请求到 /api/batteries/${id}`);

    // 使用真实API调用
    return request.get(`/api/batteries/${id}`).then(response => {
      console.log('获取电池详情响应:', response);

      // 将API响应数据转换为前端需要的格式
      if (response) {
        // 处理价格字段，确保它们是数字
        let price = 0;
        let rentPrice = 0;

        // 尝试将价格转换为数字
        if (response.price !== undefined && response.price !== null) {
          price = parseFloat(response.price);
          if (isNaN(price)) price = 0;
        }

        if (response.rentPrice !== undefined && response.rentPrice !== null) {
          rentPrice = parseFloat(response.rentPrice);
          if (isNaN(rentPrice)) rentPrice = 0;
        }

        // 处理类别 ID 和名称
        let categoryId = null;
        let categoryName = '';
        let categoryCode = '';

        if (response.categoryId !== undefined && response.categoryId !== null) {
          categoryId = response.categoryId;
        }

        if (response.categoryName !== undefined && response.categoryName !== null) {
          categoryName = response.categoryName;
        }

        if (response.categoryCode !== undefined && response.categoryCode !== null) {
          categoryCode = response.categoryCode;
        }

        // 处理生产日期
        let manufactureDate = '';
        if (response.manufactureDate) {
          try {
            // 如果是带有时区信息的日期字符串，先提取日期部分
            let dateStr = response.manufactureDate;
            if (typeof dateStr === 'string' && dateStr.includes('T')) {
              dateStr = dateStr.split('T')[0];
            }

            // 尝试将日期字符串转换为 Date 对象
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              // 如果是有效的日期，直接使用提取的日期部分
              manufactureDate = dateStr;
              console.log('详情页原始生产日期:', response.manufactureDate, '格式化后:', manufactureDate);
            }
          } catch (e) {
            console.error('处理生产日期出错:', e);
          }
        }

        // 如果响应是对象，则将其转换为前端需要的格式
        // 确保 lifespan 字段有默认值
        let lifespan = 36; // 默认值
        if (response.lifespan !== undefined && response.lifespan !== null) {
          lifespan = parseInt(response.lifespan);
          if (isNaN(lifespan) || lifespan <= 0) {
            lifespan = 36; // 如果不是有效数字或者小于等于0，使用默认值
          }
        }
        console.log('电池详情原始 lifespan:', response.lifespan, '处理后:', lifespan);
        const batteryData = {
          id: response.id,
          code: response.serialNumber || response.code || `BAT-${response.id}`,
          model: response.model || '',
          spec: response.spec || (response.voltage && response.capacity ? `${response.voltage}V${response.capacity}Ah` : '未知规格'),
          categoryCode: categoryCode || response.categoryCode || categoryId,
          categoryId: categoryId,
          categoryName: categoryName,
          status: this._mapStatusFromApi(response.status),
          price: price,
          rentPrice: rentPrice,
          manufactureDate: manufactureDate,
          lifespan: lifespan, // 使用响应中的寿命
          remainingLife: this._calculateRemainingLife(manufactureDate, lifespan), // 根据生产日期和寿命计算剩余寿命
          description: response.description || '',
          notes: response.notes || '',
          manufacturer: response.manufacturer || ''
        };

        return {
          code: 0,
          message: 'success',
          data: batteryData
        };
      }

      return {
        code: 0,
        message: 'success',
        data: null
      };
    }).catch(error => {
      console.error(`获取电池详情失败:`, error);

      // 返回错误信息
      return Promise.reject(new Error(`获取电池详情失败: ${error.message || '未知错误'}`));

      /* 注释掉模拟数据部分，使用真实错误处理
      // 如果API调用失败，使用模拟数据作为备份
      console.log('使用模拟数据作为备份');

      // 模拟电池数据
      const mockBatteryData = {
        id: id,
        code: `BAT-${id}`,
        model: id % 2 === 0 ? '高级锂电池' : '超级锂电池',
        spec: id % 3 === 0 ? '72V20Ah' : (id % 3 === 1 ? '48V20Ah' : '60V20Ah'),
        categoryCode: id % 3 === 0 ? '72V' : (id % 3 === 1 ? '48V' : '60V'),
        categoryName: id % 3 === 0 ? '72V锂电池' : (id % 3 === 1 ? '48V锂电池' : '60V锂电池'),
        status: 'Available',
        price: 1500 + (id * 100),
        rentPrice: 50 + (id * 5),
        manufactureDate: '2023-01-01',
        lifespan: 36,
        remainingLife: 30,
        description: '本产品采用高品质锂电池芯，具有安全稳定、寿命长、充放电快速等特点。',
        notes: '适用于各类电动车辆，提供持久稳定的电力输出。',
        manufacturer: id % 2 === 0 ? '比亚迪' : '松下'
      };

      return {
        code: 0,
        message: 'success',
        data: mockBatteryData
      };
      */
    });
  },

  /**
   * 将API状态映射为前端状态
   * @private
   * @param {String} apiStatus API状态
   * @returns {Number} 前端状态码
   */
  _mapStatusFromApi(apiStatus) {
    console.log('将API状态映射为前端状态:', apiStatus);

    // 如果状态已经是数字，直接返回
    if (typeof apiStatus === 'number') {
      // 确保状态值在有效范围内
      if (apiStatus >= 1 && apiStatus <= 4) {
        return apiStatus;
      }
      // 如果状态值超出范围，返回默认值 1
      return 1;
    }

    // 如果状态是字符串，尝试转换为数字
    if (typeof apiStatus === 'string') {
      // 如果状态是数字字符串，直接转换
      if (/^\d+$/.test(apiStatus)) {
        const statusNum = parseInt(apiStatus, 10);
        if (statusNum >= 1 && statusNum <= 4) {
          return statusNum;
        }
        return 1;
      }

      // 如果状态是文本描述，使用映射表
      const statusMap = {
        'Available': 1,
        'Rented': 2,
        'Sold': 3,
        'Maintenance': 4,
        'Defective': 4,
        '可用': 1,
        '已租赁': 2,
        '已售出': 3,
        '维修中': 4
      };

      return statusMap[apiStatus] || 1;
    }

    // 如果状态是其他类型或未定义，返回默认值 1
    return 1;
  },

  /**
   * 获取电池类别列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getBatteryCategories(params = { keyword: 'empty' }) {
    if (useMockData) {
      return request.get('/data/mock/battery-categories.json');
    }

    console.log('发送请求到 /api/battery-categories，参数:', params);
    return request.get('/api/battery-categories', params).then(response => {
      console.log('获取电池类别列表原始响应:', response);

      // 将API响应数据转换为前端需要的格式
      if (response && Array.isArray(response)) {
        const categories = response.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code,
          description: item.description,
          specs: item.specs || []
        }));

        console.log('处理后的类别数据:', categories);
        return {
          code: 0,
          message: 'success',
          data: categories
        };
      }

      console.warn('电池类别数据格式不正确:', response);
      return {
        code: 0,
        message: 'success',
        data: []
      };
    }).catch(error => {
      console.error('获取电池类别列表失败:', error);
      return {
        code: -1,
        message: error.message || '获取电池类别列表失败',
        data: []
      };
    });
  },

  /**
   * 创建电池类别
   * @param {Object} categoryData 类别数据
   * @returns {Promise} Promise对象
   */
  createBatteryCategory(categoryData) {
    return request.post('/api/battery-categories', categoryData);
  },

  /**
   * 更新电池类别
   * @param {Number} id 类别ID
   * @param {Object} categoryData 类别数据
   * @returns {Promise} Promise对象
   */
  updateBatteryCategory(id, categoryData) {
    return request.put(`/api/battery-categories/${id}`, categoryData);
  },

  /**
   * 删除电池类别
   * @param {Number} id 类别ID
   * @returns {Promise} Promise对象
   */
  deleteBatteryCategory(id) {
    return request.delete(`/api/battery-categories/${id}`);
  },

  /**
   * 获取电池规格列表
   * @returns {Promise} Promise对象
   */
  getBatterySpecs() {
    console.log('发送请求到 /api/battery-specs');
    return request.get('/api/battery-specs').then(response => {
      console.log('获取电池规格列表响应:', response);

      if (response && Array.isArray(response)) {
        console.log('响应是数组，长度:', response.length);
        const specs = response.map(item => ({
          id: item.id,
          name: item.name,
          voltage: item.voltage,
          capacity: item.capacity,
          weight: item.weight,
          dimensions: item.dimensions,
          price: item.price,
          description: item.description,
          imageUrl: item.imageUrl,
          categoryId: item.categoryId,
          categoryCode: item.categoryCode,
          spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格')
        }));

        console.log('处理后的规格数据:', specs);
        return {
          code: 0,
          message: 'success',
          data: specs
        };
      } else {
        console.log('响应不是数组或为空:', response);

        // 如果API调用失败或返回空数据，使用模拟数据
        const mockSpecs = [
          {
            id: 1,
            name: '48V20Ah',
            voltage: 48,
            capacity: 20,
            price: 1500,
            rentPrice: 50,
            categoryId: 1,
            spec: '48V20Ah'
          },
          {
            id: 2,
            name: '60V20Ah',
            voltage: 60,
            capacity: 20,
            price: 1800,
            rentPrice: 60,
            categoryId: 1,
            spec: '60V20Ah'
          },
          {
            id: 3,
            name: '72V20Ah',
            voltage: 72,
            capacity: 20,
            price: 2100,
            rentPrice: 70,
            categoryId: 1,
            spec: '72V20Ah'
          },
          {
            id: 4,
            name: '48V30Ah',
            voltage: 48,
            capacity: 30,
            price: 1700,
            rentPrice: 55,
            categoryId: 1,
            spec: '48V30Ah'
          }
        ];

        return {
          code: 0,
          message: 'success',
          data: mockSpecs
        };
      }
    }).catch(error => {
      console.error('获取电池规格列表错误:', error);

      // 出错时使用模拟数据
      const mockSpecs = [
        {
          id: 1,
          name: '48V20Ah',
          voltage: 48,
          capacity: 20,
          price: 1500,
          rentPrice: 50,
          categoryId: 1,
          spec: '48V20Ah'
        },
        {
          id: 2,
          name: '60V20Ah',
          voltage: 60,
          capacity: 20,
          price: 1800,
          rentPrice: 60,
          categoryId: 1,
          spec: '60V20Ah'
        },
        {
          id: 3,
          name: '72V20Ah',
          voltage: 72,
          capacity: 20,
          price: 2100,
          rentPrice: 70,
          categoryId: 1,
          spec: '72V20Ah'
        },
        {
          id: 4,
          name: '48V30Ah',
          voltage: 48,
          capacity: 30,
          price: 1700,
          rentPrice: 55,
          categoryId: 1,
          spec: '48V30Ah'
        }
      ];

      return {
        code: 0,
        message: 'success',
        data: mockSpecs
      };
    });
  },

  /**
   * 根据类别获取电池规格列表
   * @param {Number} categoryId 类别ID
   * @returns {Promise} Promise对象
   */
  getSpecsByCategoryId(categoryId) {
    return request.get(`/api/battery-specs/category/${categoryId}`).then(response => {
      if (response && Array.isArray(response)) {
        const specs = response.map(item => ({
          id: item.id,
          name: item.name,
          voltage: item.voltage,
          capacity: item.capacity,
          weight: item.weight,
          dimensions: item.dimensions,
          price: item.price,
          description: item.description,
          imageUrl: item.imageUrl,
          categoryId: item.categoryId,
          categoryCode: item.categoryCode
        }));

        return {
          code: 0,
          message: 'success',
          data: specs
        };
      }

      return {
        code: 0,
        message: 'success',
        data: []
      };
    });
  },



  /**
   * 创建电池规格
   * @param {Object} specData 规格数据
   * @returns {Promise} Promise对象
   */
  createBatterySpec(specData) {
    return request.post('/api/battery-specs', specData);
  },

  /**
   * 更新电池规格
   * @param {Number} id 规格ID
   * @param {Object} specData 规格数据
   * @returns {Promise} Promise对象
   */
  updateBatterySpec(id, specData) {
    return request.put(`/api/battery-specs/${id}`, specData);
  },

  /**
   * 删除电池规格
   * @param {Number} id 规格ID
   * @returns {Promise} Promise对象
   */
  deleteBatterySpec(id) {
    return request.delete(`/api/battery-specs/${id}`);
  },

  /**
   * 获取电池类别详情
   * @param {Number} id 类别ID
   * @returns {Promise} Promise对象
   */
  getBatteryCategoryById(id) {
    if (useMockData) {
      return new Promise((resolve) => {
        this.getBatteryCategories().then(res => {
          const category = res.data.find(c => c.id === id);
          resolve({
            code: 0,
            message: 'success',
            data: category || null
          });
        });
      });
    }

    return request.get(`/api/battery-categories/${id}`);
  },

  /**
   * 根据类别获取电池列表
   * @param {String} categoryCode 类别编码
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getBatteriesByCategory(categoryCode, params = { pageNumber: 1, pageSize: 15 }) {
    if (useMockData) {
      return new Promise((resolve) => {
        this.getBatteries(params).then(res => {
          if (res.data && res.data.batteries) {
            const filteredBatteries = res.data.batteries.filter(b => b.categoryCode === categoryCode);
            resolve({
              code: 0,
              message: 'success',
              data: {
                batteries: filteredBatteries,
                totalItems: filteredBatteries.length,
                pageNumber: params.pageNumber,
                pageSize: params.pageSize,
                totalPages: Math.ceil(filteredBatteries.length / params.pageSize),
                hasNextPage: false,
                hasPreviousPage: params.pageNumber > 1
              }
            });
          } else {
            resolve({
              code: 0,
              message: 'success',
              data: {
                batteries: [],
                totalItems: 0,
                pageNumber: params.pageNumber,
                pageSize: params.pageSize,
                totalPages: 0,
                hasNextPage: false,
                hasPreviousPage: false
              }
            });
          }
        });
      });
    }

    // 构建查询参数
    const queryParams = {
      ...params,
      categoryCode: categoryCode
    };

    console.log('发送请求到 /api/batteries，参数:', queryParams);
    return request.get('/api/batteries', queryParams).then(response => {
      console.log('获取电池列表响应:', response);

      // 将API响应数据转换为前端需要的格式
      if (response && response.items) {
        console.log('使用 response.items 格式');
        const batteries = response.items.map(item => ({
          id: item.id,
          code: item.serialNumber,
          model: item.model,
          spec: `${item.voltage}V${item.capacity}Ah`,
          categoryCode: item.categoryCode,
          status: this._mapStatusFromApi(item.status),
          price: item.price,
          rentPrice: item.rentPrice,
          manufactureDate: new Date(item.manufactureDate).toISOString().split('T')[0],
          lifespan: item.lifespan || 36,
          remainingLife: item.remainingLife || this._calculateRemainingLife(new Date(item.manufactureDate).toISOString().split('T')[0], item.lifespan || 36),
          description: item.description,
          notes: item.notes
        }));

        return {
          code: 0,
          message: 'success',
          data: {
            batteries,
            totalItems: response.totalItems,
            pageNumber: response.pageNumber,
            pageSize: response.pageSize,
            totalPages: response.totalPages,
            hasNextPage: response.hasNextPage,
            hasPreviousPage: response.hasPreviousPage
          }
        };
      }

      // 直接返回原始响应，让 store 处理
      return response;
    });
  },

  /**
   * 获取电池类别详情（根据编码）
   * @param {String} code 类别编码
   * @returns {Promise} Promise对象
   */
  getBatteryCategoryByCode(code) {
    if (useMockData) {
      return new Promise((resolve) => {
        this.getBatteryCategories().then(res => {
          const category = res.data.find(c => c.code === code);
          resolve({
            code: 0,
            message: 'success',
            data: category || null
          });
        });
      });
    }

    return request.get(`/api/battery-categories/code/${code}`);
  },

  /**
   * 创建新电池
   * @param {Object} batteryData 电池数据
   * @returns {Promise} Promise对象
   */
  createBattery(batteryData) {
    console.log('发送创建电池请求，数据:', batteryData);

    // 构建请求数据 - 完全匹配后端 CreateBatteryRequest 格式
    const requestData = {
      // 必填字段
      spec: batteryData.spec || batteryData.model || '默认型号',
      status: batteryData.status || 1, // 1=可用, 2=租出, 3=维修, 4=报废
      price: parseFloat(batteryData.price) || 0,
      rentPrice: batteryData.rentPrice ? batteryData.rentPrice.toString() : '0',
      manufactureDate: batteryData.manufactureDate ? new Date(batteryData.manufactureDate).toISOString() : new Date().toISOString(),
      lifespan: parseInt(batteryData.lifespan) || 36,
      description: batteryData.description || '',
      categoryId: parseInt(batteryData.categoryId) || 1,
      categoryCode: batteryData.categoryCode || '',
      categoryName: batteryData.categoryName || '',

      // 可选字段 - 确保有默认数值
      voltage: batteryData.voltage ? batteryData.voltage.toString() : '12',
      capacity: batteryData.capacity ? batteryData.capacity.toString() : '20',
      cycleCount: batteryData.cycleCount ? batteryData.cycleCount.toString() : '800',
      chargeTime: batteryData.chargeTime || '4-6',

      // 图片、服务和安装费用
      mainImages: batteryData.mainImages || [],
      services: batteryData.services || [],
      installationFees: batteryData.installationFees || [],
      removedImages: batteryData.removedImages || []
    };

    // 验证电池数据
    const validationResult = BatteryValidator.validate(batteryData, false);
    if (!validationResult.isValid) {
      console.error('电池数据验证失败:', validationResult.errors);
      return Promise.reject(new Error(validationResult.getFirstError()));
    }

    // 验证必填字段
    if (!requestData.categoryId || requestData.categoryId <= 0) {
      console.error('分类ID无效:', requestData.categoryId);
      return Promise.reject(new Error('请选择有效的电池分类'));
    }

    if (!requestData.spec || requestData.spec.trim() === '') {
      console.error('规格不能为空');
      return Promise.reject(new Error('规格不能为空'));
    }

    console.log('使用标准 batteries API 创建电池');
    console.log('发送到后端的请求数据:', JSON.stringify(requestData, null, 2));

    // 使用标准 API 端点
    return request.post('/api/batteries', requestData).then(response => {
      console.log('创建电池响应:', response);

      if (response) {
        // 将API响应转换为前端需要的格式
        // 确保 lifespan 字段有默认值
        let lifespan = 36; // 默认值
        if (response.lifespan !== undefined && response.lifespan !== null) {
          lifespan = parseInt(response.lifespan);
          if (isNaN(lifespan) || lifespan <= 0) {
            lifespan = 36; // 如果不是有效数字或者小于等于0，使用默认值
          }
        }
        console.log('创建电池原始 lifespan:', response.lifespan, '处理后:', lifespan);
        const batteryData = {
          id: response.id,
          spec: `${response.voltage}V${response.capacity}Ah`,
          status: this._mapStatusFromApi(response.status),
          price: response.price,
          rentPrice: response.rentPrice,
          manufactureDate: response.manufactureDate ? new Date(response.manufactureDate).toISOString().split('T')[0] : '',
          lifespan: lifespan, // 使用响应中的寿命
          remainingLife: this._calculateRemainingLife(response.manufactureDate, lifespan), // 根据生产日期和寿命计算剩余寿命
          description: response.description || '',
          notes: response.notes || '',
          categoryId: response.categoryId,
          categoryCode: response.categoryCode
        };

        return {
          code: 0,
          message: 'success',
          data: batteryData
        };
      }

      return response;
    }).catch(error => {
      console.error('创建电池失败:', error);

      // 返回错误信息
      return {
        code: -1,
        message: error.message || '创建电池失败',
        data: null
      };
    });
  },

  /**
   * 更新电池
   * @param {Number} id 电池ID
   * @param {Object} batteryData 电池数据
   * @returns {Promise} Promise对象
   */
  updateBattery(id, batteryData) {
    console.log(`发送更新电池请求，ID: ${id}, 数据:`, batteryData);

    // 构建请求数据
    const requestData = {
      model: batteryData.spec || '',
      manufacturer: batteryData.manufacturer || '默认厂商',
      capacity: parseFloat(batteryData.capacity || 20),
      voltage: parseFloat(batteryData.voltage || 48),
      status: this._mapStatusToApi(batteryData.status),
      cycleCount: batteryData.cycleCount || 0,
      lastMaintenanceDate: batteryData.lastMaintenanceDate || batteryData.manufactureDate || new Date().toISOString().split('T')[0], // 确保 LastMaintenanceDate 不为 NULL
      notes: batteryData.notes || '',
      description: batteryData.description || '',
      healthPercentage: batteryData.healthPercentage || 100,
      price: parseFloat(batteryData.price || 0),
      rentPrice: parseFloat(batteryData.rentPrice || 0),
      categoryId: batteryData.categoryId || null,
      lifespan: batteryData.lifespan !== undefined && batteryData.lifespan !== null ? parseInt(batteryData.lifespan) || 36 : 36, // 添加商品寿命字段，默认为36个月

      // BMS 相关字段
      macId: batteryData.macId || null,
      firmware: batteryData.firmware || null,
      batteryType: batteryData.batteryType || null,
      nominalCapacity: batteryData.nominalCapacity || null,
      actualCapacity: batteryData.actualCapacity || null,
      soc: batteryData.soc || null,
      current: batteryData.current || null,
      power: batteryData.power || null,
      maxVoltage: batteryData.maxVoltage || null,
      minVoltage: batteryData.minVoltage || null,
      averageVoltage: batteryData.averageVoltage || null,
      voltageDifference: batteryData.voltageDifference || null,
      maxTemp: batteryData.maxTemp || null,
      minTemp: batteryData.minTemp || null,
      chargeState: batteryData.chargeState || null,
      chargeStateDescription: batteryData.chargeStateDescription || null,
      connectState: batteryData.connectState || null,
      connectStateDescription: batteryData.connectStateDescription || null,
      faultState: batteryData.faultState || null,
      alarmState: batteryData.alarmState || null,
      cellVoltages: batteryData.cellVoltages || null,
      temperatures: batteryData.temperatures || null,
      detailedTemperatures: batteryData.detailedTemperatures || null,
      protectionParameters: batteryData.protectionParameters || null,
      tempProtectionParameters: batteryData.tempProtectionParameters || null,
      balanceControlParameters: batteryData.balanceControlParameters || null,
      otherParameters: batteryData.otherParameters || null,
      deviceInfo: batteryData.deviceInfo || null,
      bmsTimestamp: batteryData.bmsTimestamp || null
    };

    // 从规格中提取电压和容量
    if (batteryData.spec && !requestData.voltage && !requestData.capacity) {
      const specMatch = batteryData.spec.match(/(\d+)V(\d+)Ah/);
      if (specMatch) {
        requestData.voltage = parseFloat(specMatch[1]);
        requestData.capacity = parseFloat(specMatch[2]);
      }
    }

    return request.put(`/api/batteries/${id}`, requestData).then(response => {
      console.log('更新电池响应:', response);

      if (response) {
        // 将API响应转换为前端需要的格式
        // 确保 lifespan 字段有默认值
        let lifespan = 36; // 默认值
        if (response.lifespan !== undefined && response.lifespan !== null) {
          lifespan = parseInt(response.lifespan);
          if (isNaN(lifespan) || lifespan <= 0) {
            lifespan = 36; // 如果不是有效数字或者小于等于0，使用默认值
          }
        }
        console.log('更新电池原始 lifespan:', response.lifespan, '处理后:', lifespan);
        const batteryData = {
          id: response.id,
          spec: `${response.voltage}V${response.capacity}Ah`,
          status: this._mapStatusFromApi(response.status),
          price: response.price,
          rentPrice: response.rentPrice,
          manufactureDate: response.manufactureDate ? new Date(response.manufactureDate).toISOString().split('T')[0] : '',
          lifespan: lifespan, // 使用响应中的寿命
          remainingLife: this._calculateRemainingLife(response.manufactureDate, lifespan), // 根据生产日期和寿命计算剩余寿命
          description: response.description || '',
          notes: response.notes || '',
          categoryId: response.categoryId,
          categoryCode: response.categoryCode
        };

        return {
          code: 0,
          message: 'success',
          data: batteryData
        };
      }

      return response;
    });
  },

  /**
   * 将前端状态映射为API状态
   * @private
   * @param {Number} frontendStatus 前端状态码
   * @returns {String} API状态
   */
  _mapStatusToApi(frontendStatus) {
    const statusMap = {
      1: 'Available',
      2: 'Rented',
      3: 'Sold',
      4: 'Maintenance'
    };
    return statusMap[frontendStatus] || 'Available';
  },

  /**
   * 删除电池
   * @param {Number} id 电池ID
   * @returns {Promise} Promise对象
   */
  deleteBattery(id) {
    console.log(`发送删除电池请求，ID: ${id}`);

    if (useMockData) {
      // 模拟删除成功
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            code: 0,
            message: 'success',
            data: true
          });
        }, 1000);
      });
    }

    // 使用真实API调用
    return request.delete(`/api/batteries/${id}`).then(response => {
      console.log('删除电池响应:', response);

      // 如果响应是空或者没有数据，返回成功
      if (!response) {
        return {
          code: 0,
          message: 'success',
          data: true
        };
      }

      return response;
    }).catch(error => {
      console.error('删除电池失败:', error);
      return Promise.reject(new Error(`删除电池失败: ${error.message || '未知错误'}`));
    });
  },

  /**
   * 计算剩余寿命
   * @private
   * @param {String} manufactureDate 生产日期
   * @param {Number} lifespan 商品寿命（月）
   * @returns {Number} 剩余寿命（月）
   */
  _calculateRemainingLife(manufactureDate, lifespan) {
    // 默认寿命为36个月
    const defaultLifespan = 36;

    // 确保 lifespan 是有效数字
    let totalLifespan = defaultLifespan;
    if (lifespan !== undefined && lifespan !== null) {
      const parsedLifespan = parseInt(lifespan);
      if (!isNaN(parsedLifespan) && parsedLifespan > 0) {
        totalLifespan = parsedLifespan;
      }
    }

    console.log('计算剩余寿命，原始 lifespan:', lifespan, '处理后:', totalLifespan);

    if (!manufactureDate) {
      return totalLifespan; // 如果没有生产日期，返回完整寿命
    }

    try {
      // 将生产日期转换为 Date 对象
      const mfgDate = new Date(manufactureDate);
      const now = new Date();

      // 检查生产日期是否有效
      if (isNaN(mfgDate.getTime())) {
        console.warn('生产日期无效:', manufactureDate);
        return totalLifespan;
      }

      // 计算生产日期到现在的月数
      const ageInMonths = Math.floor((now - mfgDate) / (30 * 24 * 60 * 60 * 1000));

      // 计算剩余寿命
      const remaining = Math.max(0, totalLifespan - ageInMonths);

      console.log(`生产日期: ${manufactureDate}, 寿命: ${totalLifespan} 月, 已使用: ${ageInMonths} 月, 剩余: ${remaining} 月`);

      return remaining;
    } catch (error) {
      console.error('计算剩余寿命出错:', error);
      return totalLifespan; // 出错时返回完整寿命
    }
  },

  /**
   * 获取电池服务信息
   * @param {Number} id 电池ID
   * @returns {Promise} Promise对象
   */
  getBatteryServices(id) {
    if (!id || id <= 0) {
      console.error('无效的电池 ID:', id);
      return Promise.reject(new Error('无效的电池 ID'));
    }

    console.log(`发送请求到 /api/batteries/${id}/services`);

    // 使用真实API调用
    return request.get(`/api/batteries/${id}/services`).then(response => {
      console.log('获取电池服务信息响应:', response);

      // 如果API尚未实现，返回默认数据
      if (!response || response.error) {
        console.warn('API未返回有效数据，使用默认服务信息');
        return {
          code: 0,
          message: 'success',
          data: [
            { id: 1, name: '免费安装', description: '包含电池安装、调试、测试等基础服务' },
            { id: 2, name: '上门维修', description: '提供上门维修服务' },
            { id: 3, name: '售后保障', description: '提供完善的售后保障服务' },
            { id: 4, name: '正品保证', description: '保证产品为原厂正品' }
          ]
        };
      }

      return {
        code: 0,
        message: 'success',
        data: response
      };
    }).catch(error => {
      console.error('获取电池服务信息失败:', error);

      // 如果API调用失败，返回默认数据
      return {
        code: 0,
        message: 'success',
        data: [
          { id: 1, name: '免费安装', description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '上门维修', description: '提供上门维修服务' },
          { id: 3, name: '售后保障', description: '提供完善的售后保障服务' },
          { id: 4, name: '正品保证', description: '保证产品为原厂正品' }
        ]
      };
    });
  },

  /**
   * 获取电池安装费用
   * @param {Number} id 电池ID
   * @returns {Promise} Promise对象
   */
  getBatteryInstallationFees(id) {
    if (!id || id <= 0) {
      console.error('无效的电池 ID:', id);
      return Promise.reject(new Error('无效的电池 ID'));
    }

    console.log(`发送请求到 /api/batteries/${id}/installation-fees`);

    // 使用真实API调用
    return request.get(`/api/batteries/${id}/installation-fees`).then(response => {
      console.log('获取电池安装费用响应:', response);

      // 如果API尚未实现，返回默认数据
      if (!response || response.error) {
        console.warn('API未返回有效数据，使用默认安装费用');
        return {
          code: 0,
          message: 'success',
          data: [
            { id: 1, name: '基础安装费', price: 0, description: '包含电池安装、调试、测试等基础服务' },
            { id: 2, name: '线路改造费', price: 50, description: '如需要线路改造，额外收取费用' },
            { id: 3, name: '远程上门费', price: 30, description: '距离门店10公里以上的上门费用' }
          ]
        };
      }

      return {
        code: 0,
        message: 'success',
        data: response
      };
    }).catch(error => {
      console.error('获取电池安装费用失败:', error);

      // 如果API调用失败，返回默认数据
      return {
        code: 0,
        message: 'success',
        data: [
          { id: 1, name: '基础安装费', price: 0, description: '包含电池安装、调试、测试等基础服务' },
          { id: 2, name: '线路改造费', price: 50, description: '如需要线路改造，额外收取费用' },
          { id: 3, name: '远程上门费', price: 30, description: '距离门店10公里以上的上门费用' }
        ]
      };
    });
  },

  /**
   * 检查商品是否重复
   * @param {Object} params 检查参数
   * @param {Number} params.categoryId 类别ID
   * @param {String} params.spec 规格名称
   * @returns {Promise} Promise对象
   */
  checkDuplicate(params) {
    if (!params.categoryId || !params.spec) {
      return Promise.reject(new Error('类别ID和规格名称不能为空'));
    }

    return request.post('/api/batteries/check-duplicate', params)
      .then(response => {
        return {
          code: 0,
          message: 'success',
          data: {
            isDuplicate: response?.isDuplicate || false
          }
        };
      })
      .catch(error => {
        console.error('检查商品重复失败:', error);
        return {
          code: -1,
          message: error.message || '检查商品重复失败',
          data: {
            isDuplicate: false
          }
        };
      });
  }
};

export default BatteryAPI;
