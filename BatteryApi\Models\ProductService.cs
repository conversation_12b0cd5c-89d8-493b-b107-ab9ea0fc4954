using SqlSugar;

namespace BatteryApi.Models;

/// <summary>
/// 商品服务实体
/// </summary>
[SugarTable("ProductServices")]
public class ProductService
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int ProductId { get; set; }

    /// <summary>
    /// 服务名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 服务描述
    /// </summary>
    [SugarColumn(Length = 500)]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int Sort { get; set; } = 0;

    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 关联的商品（导航属性）
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProductId))]
    public Product? Product { get; set; }
}
