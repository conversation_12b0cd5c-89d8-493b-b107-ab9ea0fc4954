using SqlSugar;

namespace BatteryApi.Models;

/// <summary>
/// 商品规格模型
/// </summary>
[SugarTable("ProductSpecs")]
public class ProductSpec
{
    /// <summary>
    /// 规格ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 规格名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 电压 (V)
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Voltage { get; set; } = 0;

    /// <summary>
    /// 容量 (Ah)
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Capacity { get; set; } = 0;

    /// <summary>
    /// 重量 (kg)
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Weight { get; set; } = 0;

    /// <summary>
    /// 尺寸规格
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = true)]
    public string? Dimensions { get; set; }

    /// <summary>
    /// 价格 (元)
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 描述信息
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true)]
    public string? Description { get; set; }

    /// <summary>
    /// 图片URL
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 所属分类ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int CategoryId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否启用
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 排序顺序
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int DisplayOrder { get; set; } = 0;
}
