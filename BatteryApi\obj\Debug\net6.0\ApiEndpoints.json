[{"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/auth/current-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "LoginWithSms", "RelativePath": "api/auth/login/sms", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.SmsLoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "UpdateOnlineStatus", "RelativePath": "api/auth/online-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "isOnline", "Type": "System.Boolean", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.TokenResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "Register", "RelativePath": "api/auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.RegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "RegisterWithPhone", "RelativePath": "api/auth/register/phone", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.PhoneRegisterRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "RevokeToken", "RelativePath": "api/auth/revoke-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "UpdateUserStatus", "RelativePath": "api/auth/update-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.UpdateUserStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "DeleteUser", "RelativePath": "api/auth/users/{phoneNumber}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetVerificationDetail", "RelativePath": "api/auth/verification/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "CheckIdCardPhotos", "RelativePath": "api/auth/verification/check-idcard-photos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetVerificationCounts", "RelativePath": "api/auth/verification/counts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetVerificationList", "RelativePath": "api/auth/verification/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "NetworkDiagnostic", "RelativePath": "api/auth/verification/network-diagnostic", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetPendingVerifications", "RelativePath": "api/auth/verification/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "RecognizeIdCard", "RelativePath": "api/auth/verification/recognize-idcard", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Side", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "ReviewVerification", "RelativePath": "api/auth/verification/review", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.VerificationUpdateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.VerificationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetVerificationStatus", "RelativePath": "api/auth/verification/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "GetVerificationStatus", "RelativePath": "api/auth/verification/status-old", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.VerificationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "SubmitVerification", "RelativePath": "api/auth/verification/submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "RealName", "Type": "System.String", "IsRequired": false}, {"Name": "IdCardNumber", "Type": "System.String", "IsRequired": false}, {"Name": "IdCardFront", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "IdCardBack", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "FaceImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ValidDate", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "SubmitVerificationInfo", "RelativePath": "api/auth/verification/submit-info", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.VerificationInfoRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "SubmitVerification", "RelativePath": "api/auth/verification/submit-old", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "RealName", "Type": "System.String", "IsRequired": false}, {"Name": "IdCardNumber", "Type": "System.String", "IsRequired": false}, {"Name": "IdCardFront", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "IdCardBack", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "FaceImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ValidDate", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.VerificationResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "TestGetVerificationList", "RelativePath": "api/auth/verification/test-list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "UploadFace", "RelativePath": "api/auth/verification/upload-face", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "UploadIdCardBack", "RelativePath": "api/auth/verification/upload-idcard-back", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "UploadIdCardFront", "RelativePath": "api/auth/verification/upload-idcard-front", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "GetVerificationByUserId", "RelativePath": "api/auth/verification/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.VerificationController", "Method": "VerifyUser", "RelativePath": "api/auth/verification/verify/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Auth.VerificationStatusUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.AuthController", "Method": "WeChatLogin", "RelativePath": "api/auth/wechat-login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Auth.WeChatLoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Auth.LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatteries", "RelativePath": "api/batteries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CategoryCode", "Type": "System.String", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAvailable", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinHealth", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortAscending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "Search", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SearchKeyword", "Type": "System.String", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.PagedResponse`1[[BatteryApi.DTOs.Battery.BatteryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "CreateBattery", "RelativePath": "api/batteries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "CreateBatteryInstallationFee", "RelativePath": "api/batteries/{batteryId}/installation-fees", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryInstallationFeeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryInstallationFeeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "CreateBatteryService", "RelativePath": "api/batteries/{batteryId}/services", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryServiceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryServiceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BatteryApi.Controllers.BatteryServiceController", "Method": "GetBatteryServices", "RelativePath": "api/batteries/{batteryId}/services", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryServiceController", "Method": "AddBatteryService", "RelativePath": "api/batteries/{batteryId}/services", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "service", "Type": "BatteryApi.Models.BatteryServiceFee", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryServiceController", "Method": "UpdateBatteryService", "RelativePath": "api/batteries/{batteryId}/services/{serviceId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "serviceId", "Type": "System.Int32", "IsRequired": true}, {"Name": "service", "Type": "BatteryApi.Models.BatteryServiceFee", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryServiceController", "Method": "DeleteBatteryService", "RelativePath": "api/batteries/{batteryId}/services/{serviceId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "serviceId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBattery", "RelativePath": "api/batteries/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "UpdateBattery", "RelativePath": "api/batteries/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "Spec", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Int32", "IsRequired": false}, {"Name": "Price", "Type": "System.Decimal", "IsRequired": false}, {"Name": "RentPrice", "Type": "System.String", "IsRequired": false}, {"Name": "ManufactureDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Lifespan", "Type": "System.Int32", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Int32", "IsRequired": false}, {"Name": "CategoryCode", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryName", "Type": "System.String", "IsRequired": false}, {"Name": "Voltage", "Type": "System.String", "IsRequired": false}, {"Name": "Capacity", "Type": "System.String", "IsRequired": false}, {"Name": "CycleCount", "Type": "System.String", "IsRequired": false}, {"Name": "ChargeTime", "Type": "System.String", "IsRequired": false}, {"Name": "MainImages", "Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryImageRequest, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "MainImagesFiles", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "Services", "Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryServiceRequest, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "InstallationFees", "Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryInstallationFeeRequest, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "RemovedImages", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "DeleteBattery", "RelativePath": "api/batteries/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatteryInstallationFees", "RelativePath": "api/batteries/{id}/installation-fees", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryInstallationFeeDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatteryServices", "RelativePath": "api/batteries/{id}/services", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryServiceDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "UpdateBatteryWithFiles", "RelativePath": "api/batteries/{id}/update-with-files", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "Spec", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Int32", "IsRequired": false}, {"Name": "Price", "Type": "System.Decimal", "IsRequired": false}, {"Name": "RentPrice", "Type": "System.String", "IsRequired": false}, {"Name": "ManufactureDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Lifespan", "Type": "System.Int32", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Voltage", "Type": "System.String", "IsRequired": false}, {"Name": "Capacity", "Type": "System.String", "IsRequired": false}, {"Name": "ImageFiles", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "ImageType", "Type": "System.String", "IsRequired": false}, {"Name": "RemoveImageIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetAverageBatteryHealth", "RelativePath": "api/batteries/average-health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Double", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "CheckDuplicate", "RelativePath": "api/batteries/check-duplicate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.DuplicateCheckRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.Controllers.DuplicateCheckResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "CreateBatteryWithFiles", "RelativePath": "api/batteries/create-with-files", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Spec", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Price", "Type": "System.Decimal", "IsRequired": false}, {"Name": "RentPrice", "Type": "System.String", "IsRequired": false}, {"Name": "ManufactureDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Lifespan", "Type": "System.Int32", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Voltage", "Type": "System.String", "IsRequired": false}, {"Name": "Capacity", "Type": "System.String", "IsRequired": false}, {"Name": "ImageFiles", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "ImageType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "UpdateBatteryInstallationFee", "RelativePath": "api/batteries/installation-fees/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatteryInstallationFeeRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryInstallationFeeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "DeleteBatteryInstallationFee", "RelativePath": "api/batteries/installation-fees/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "UpdateBatteryService", "RelativePath": "api/batteries/services/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatteryServiceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryServiceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "DeleteBatteryService", "RelativePath": "api/batteries/services/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatterySpecs", "RelativePath": "api/batteries/specs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatterySpecDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatteryStats", "RelativePath": "api/batteries/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "GetBatteryStatusSummary", "RelativePath": "api/batteries/status-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryController", "Method": "TestJsonDeserialization", "RelativePath": "api/batteries/test-json", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "GetAllCategories", "RelativePath": "api/battery-categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryCategoryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "CreateCategory", "RelativePath": "api/battery-categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryCategoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryCategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "GetCategoryById", "RelativePath": "api/battery-categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryCategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "UpdateCategory", "RelativePath": "api/battery-categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatteryCategoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryCategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "DeleteCategory", "RelativePath": "api/battery-categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryCategoryController", "Method": "GetCategoryByCode", "RelativePath": "api/battery-categories/code/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryCategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "GetBatteryImageById", "RelativePath": "api/battery-image-management/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryImageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "UpdateBatteryImage", "RelativePath": "api/battery-image-management/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatteryImageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryImageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "DeleteBatteryImage", "RelativePath": "api/battery-image-management/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "GetBatteryImages", "RelativePath": "api/battery-image-management/battery/{batteryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryImageDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "CreateBatteryImage", "RelativePath": "api/battery-image-management/battery/{batteryId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryImageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryImageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "BatchCreateBatteryImages", "RelativePath": "api/battery-image-management/battery/{batteryId}/batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.BatchCreateBatteryImageRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryImageDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "GetBatteryMainImage", "RelativePath": "api/battery-image-management/battery/{batteryId}/main", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatteryImageDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "SetMainImage", "RelativePath": "api/battery-image-management/battery/{batteryId}/main/{imageId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageManagementController", "Method": "GetBatteryImagesByType", "RelativePath": "api/battery-image-management/battery/{batteryId}/type/{imageType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "batteryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryImageDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "GetAllSpecs", "RelativePath": "api/battery-specs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatterySpecDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "CreateSpec", "RelativePath": "api/battery-specs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatterySpecRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatterySpecDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "GetSpecById", "RelativePath": "api/battery-specs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatterySpecDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "UpdateSpec", "RelativePath": "api/battery-specs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatterySpecRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Battery.BatterySpecDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "DeleteSpec", "RelativePath": "api/battery-specs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatterySpecController", "Method": "GetSpecsByCategoryId", "RelativePath": "api/battery-specs/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatterySpecDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "DeleteImage", "RelativePath": "api/BatteryImage/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "imagePath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadImage", "RelativePath": "api/BatteryImage/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "productId", "Type": "System.String", "IsRequired": false}, {"Name": "imageType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadBatchImages", "RelativePath": "api/BatteryImage/upload-batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MainImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "DetailImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "SpecImage", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "ProductId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadDetailImage", "RelativePath": "api/BatteryImage/upload-detail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "productId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadMainImage", "RelativePath": "api/BatteryImage/upload-main", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "productId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadMultipleImages", "RelativePath": "api/BatteryImage/upload-multiple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "productId", "Type": "System.String", "IsRequired": false}, {"Name": "imageType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryImageController", "Method": "UploadSpecImage", "RelativePath": "api/BatteryImage/upload-spec", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "productId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BatteryManagementController", "Method": "GetCategories", "RelativePath": "api/BatteryManagement/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[BatteryApi.DTOs.Battery.BatteryCategoryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.BatteryManagementController", "Method": "CreateCategory", "RelativePath": "api/BatteryManagement/categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Battery.CreateBatteryCategoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.ApiResponse`1[[BatteryApi.DTOs.Battery.BatteryCategoryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.BatteryManagementController", "Method": "GetCategoryById", "RelativePath": "api/BatteryManagement/categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.ApiResponse`1[[BatteryApi.DTOs.Battery.BatteryCategoryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.BatteryManagementController", "Method": "UpdateCategory", "RelativePath": "api/BatteryManagement/categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Battery.UpdateBatteryCategoryRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.ApiResponse`1[[BatteryApi.DTOs.Battery.BatteryCategoryDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.BatteryManagementController", "Method": "DeleteCategory", "RelativePath": "api/BatteryManagement/categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "BatteryApi.DTOs.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "GetBatteryParams", "RelativePath": "api/BMS/battery-params/{macid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "macid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "GetBatteryStatus", "RelativePath": "api/BMS/battery-status/{macid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "macid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "ClearTokenCache", "RelativePath": "api/BMS/clear-token-cache", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "GenerateBatteryCode", "RelativePath": "api/BMS/generate-battery-code/{macid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "macid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "TestConnection", "RelativePath": "api/BMS/test-connection", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.BMSController", "Method": "TestParse", "RelativePath": "api/BMS/test-parse", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DatabaseController", "Method": "CreateTestOrders", "RelativePath": "api/Database/create-test-orders", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DatabaseController", "Method": "Initialize", "RelativePath": "api/Database/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "initDto", "Type": "BatteryApi.DTOs.InitializeDatabaseDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DatabaseController", "Method": "ResetAdminPassword", "RelativePath": "api/Database/reset-admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "resetDto", "Type": "BatteryApi.DTOs.ResetAdminDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DatabaseController", "Method": "UpdateDatabaseSchema", "RelativePath": "api/Database/update-schema", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DataMigrationController", "Method": "GetImagePathStats", "RelativePath": "api/DataMigration/image-path-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DataMigrationController", "Method": "Migrate", "RelativePath": "api/DataMigration/migrate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.DataMigrationController", "Method": "ValidateImagePaths", "RelativePath": "api/DataMigration/validate-image-paths", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.EnhancedImageController", "Method": "BatchProcess", "RelativePath": "api/EnhancedImage/batch-process", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "productId", "Type": "System.Int32", "IsRequired": false}, {"Name": "processOptions", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.EnhancedImageController", "Method": "GetImageInfo", "RelativePath": "api/EnhancedImage/info/{imageId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.EnhancedImageController", "Method": "SmartUpload", "RelativePath": "api/EnhancedImage/smart-upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "productId", "Type": "System.Int32", "IsRequired": false}, {"Name": "imageType", "Type": "System.String", "IsRequired": false}, {"Name": "autoCompress", "Type": "System.Boolean", "IsRequired": false}, {"Name": "generateThumbnail", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ImageController", "Method": "GetImage", "RelativePath": "api/Image/{category}/{filename}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": true}, {"Name": "filename", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ImageController", "Method": "GetImageByGuid", "RelativePath": "api/Image/guid/{guid}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "guid", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ImageController", "Method": "GetImageInfo", "RelativePath": "api/Image/info/{category}/{filename}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": true}, {"Name": "filename", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ImageController", "Method": "GetProductImage", "RelativePath": "api/Image/product/{productId}/{filename}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "filename", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.InventoryController", "Method": "GetInventories", "RelativePath": "api/Inventory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StoreId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "SpecIds", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LowStock", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.InventoryController", "Method": "ClearInventory", "RelativePath": "api/Inventory/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.ClearRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductInventoryController", "Method": "GetProductInventory", "RelativePath": "api/inventory/product", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StoreId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProductId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Keyword", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductInventoryController", "Method": "GetInventoryHistory", "RelativePath": "api/inventory/product/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "productId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "operationType", "Type": "System.String", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductInventoryController", "Method": "StockIn", "RelativePath": "api/inventory/product/stock-in", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.ProductStockInRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductInventoryController", "Method": "StockOut", "RelativePath": "api/inventory/product/stock-out", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.ProductStockOutRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductInventoryController", "Method": "Transfer", "RelativePath": "api/inventory/product/transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.ProductTransferRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.InventoryController", "Method": "StockIn", "RelativePath": "api/Inventory/stock-in", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.StockInRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.InventoryController", "Method": "TransferInventory", "RelativePath": "api/Inventory/transfer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.TransferRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "GeocodeAddress", "RelativePath": "api/Map/geocode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "address", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "GetLocationByIp", "RelativePath": "api/Map/ip-location", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ip", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "SearchNearby", "RelativePath": "api/Map/nearby", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "latitude", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "longitude", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "radius", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "GetLocationByNetwork", "RelativePath": "api/Map/network-location", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Map.NetworkLocationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "ReverseGeocode", "RelativePath": "api/Map/reverse-geocode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "latitude", "Type": "System.Double", "IsRequired": false}, {"Name": "longitude", "Type": "System.Double", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MapController", "Method": "SearchPlace", "RelativePath": "api/Map/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "region", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MultiImageUploadController", "Method": "QuickUploadMultiple", "RelativePath": "api/MultiImageUpload/quick-upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "productId", "Type": "System.Int32", "IsRequired": false}, {"Name": "imageType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.MultiImageUploadController", "Method": "UploadMultipleImages", "RelativePath": "api/MultiImageUpload/upload-multiple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "productId", "Type": "System.Int32", "IsRequired": false}, {"Name": "imageType", "Type": "System.String", "IsRequired": false}, {"Name": "autoCompress", "Type": "System.Boolean", "IsRequired": false}, {"Name": "generateThumbnail", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OcrController", "Method": "RecognizeIdCard", "RelativePath": "api/ocr/idcard", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "side", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OcrController", "Method": "GetPerformanceStatistics", "RelativePath": "api/ocr/performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "GetOrders", "RelativePath": "api/Order", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BatteryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StoreId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Type", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderNo", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "CreateOrder", "RelativePath": "api/Order", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderDto", "Type": "BatteryApi.DTOs.Order.OrderCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "GetOrderById", "RelativePath": "api/Order/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "GetOrderByOrderNo", "RelativePath": "api/Order/byOrderNo/{orderNo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "CancelOrder", "RelativePath": "api/Order/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cancelDto", "Type": "BatteryApi.DTOs.Order.OrderCancelDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "CompleteOrder", "RelativePath": "api/Order/complete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "completeDto", "Type": "BatteryApi.DTOs.Order.OrderCompleteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "GetOrderCounts", "RelativePath": "api/Order/counts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "PayOrder", "RelativePath": "api/Order/pay", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "payDto", "Type": "BatteryApi.DTOs.Order.OrderPayDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.OrderController", "Method": "GetUserOrders", "RelativePath": "api/Order/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "CreateProduct", "RelativePath": "api/Product", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Product.CreateProductRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "GetProducts", "RelativePath": "api/Product", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "UpdateProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Product.UpdateProductRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "GetProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "DeleteProduct", "RelativePath": "api/Product/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductController", "Method": "TestCreateProduct", "RelativePath": "api/Product/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Product.CreateProductRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "UpdateProductImage", "RelativePath": "api/ProductImage/{imageId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.Controllers.UpdateProductImageRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "DeleteProductImage", "RelativePath": "api/ProductImage/{imageId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "imageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "hardDelete", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "DeleteImageByUrl", "RelativePath": "api/ProductImage/delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.DeleteImageByUrlRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "GetProductImages", "RelativePath": "api/ProductImage/product/{productId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "includeDeleted", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "DeleteAllProductImages", "RelativePath": "api/ProductImage/product/{productId}/all", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "hardDelete", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "GetMainImage", "RelativePath": "api/ProductImage/product/{productId}/main", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "SetMainImage", "RelativePath": "api/ProductImage/product/{productId}/main/{imageId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "GetProductImagesByType", "RelativePath": "api/ProductImage/product/{productId}/type/{imageType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "productId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageType", "Type": "System.String", "IsRequired": true}, {"Name": "includeDeleted", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "UpdateImageOrders", "RelativePath": "api/ProductImage/sort", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.Controllers.UpdateImageOrdersRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.ProductImageController", "Method": "GetImageTypes", "RelativePath": "api/ProductImage/types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "GetAllSettings", "RelativePath": "api/settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "CreateSetting", "RelativePath": "api/settings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Settings.CreateSystemSettingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "GetSettingByKey", "RelativePath": "api/settings/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "UpdateSetting", "RelativePath": "api/settings/{key}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.DTOs.Settings.UpdateSystemSettingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "DeleteSetting", "RelativePath": "api/settings/{key}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "BackupData", "RelativePath": "api/settings/backup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "BatchUpdateSettings", "RelativePath": "api/settings/batch", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Settings.BatchUpdateSystemSettingsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "ClearCache", "RelativePath": "api/settings/clear-cache", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "GetSettingsByGroup", "RelativePath": "api/settings/group/{group}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "group", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "GetAllGroupedSettings", "RelativePath": "api/settings/grouped", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "UpdateMaintenanceSettings", "RelativePath": "api/settings/maintenance", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "settings", "Type": "BatteryApi.DTOs.Settings.MaintenanceSettingsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "UpdatePaymentSettings", "RelativePath": "api/settings/payment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "settings", "Type": "BatteryApi.DTOs.Settings.PaymentSettingsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "UpdateRentalSettings", "RelativePath": "api/settings/rental", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "settings", "Type": "BatteryApi.DTOs.Settings.RentalSettingsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemSettingController", "Method": "UpdateUserManagementSettings", "RelativePath": "api/settings/user-management", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "settings", "Type": "BatteryApi.DTOs.Settings.UserManagementSettingsDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SmsController", "Method": "SendVerificationCode", "RelativePath": "api/sms/send", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Sms.SendSmsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Sms.SendSmsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.SmsController", "Method": "VerifyCode", "RelativePath": "api/sms/verify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "BatteryApi.DTOs.Sms.VerifySmsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Sms.VerifySmsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "GetStores", "RelativePath": "api/Store", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Contact", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Latitude", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Longitude", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxDistance", "Type": "System.Nullable`1[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "CreateStore", "RelativePath": "api/Store", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeDto", "Type": "BatteryApi.DTOs.Store.StoreCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreInventoryController", "Method": "GetStoreInventory", "RelativePath": "api/store-inventory/{storeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "forceRefresh", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreInventoryController", "Method": "UpdateStoreInventory", "RelativePath": "api/store-inventory/{storeId}/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.Controllers.UpdateInventoryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "GetStoreById", "RelativePath": "api/Store/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "UpdateStore", "RelativePath": "api/Store/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "storeDto", "Type": "BatteryApi.DTOs.Store.StoreUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "DeleteStore", "RelativePath": "api/Store/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "CloseAndSoftDeleteStore", "RelativePath": "api/Store/{id}/close-and-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "deleteDto", "Type": "BatteryApi.DTOs.Store.StoreSoftDeleteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "RestoreStore", "RelativePath": "api/Store/{id}/restore", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "restoreDto", "Type": "BatteryApi.DTOs.Store.StoreRestoreDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "SoftDeleteStore", "RelativePath": "api/Store/{id}/soft-delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "deleteDto", "Type": "BatteryApi.DTOs.Store.StoreSoftDeleteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreProductController", "Method": "AssignProductsToStore", "RelativePath": "api/store/{storeId}/assign-products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "BatteryApi.Controllers.AssignProductsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "GetStoreInventory", "RelativePath": "api/Store/{storeId}/inventory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "UpdateStoreInventory", "RelativePath": "api/Store/{storeId}/inventory", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "inventoryDto", "Type": "BatteryApi.DTOs.Store.StoreInventoryUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreProductController", "Method": "GetStoreProductInventory", "RelativePath": "api/store/{storeId}/product-inventory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "GetDeletedStores", "RelativePath": "api/Store/deleted", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.StoreController", "Method": "GetNearbyStores", "RelativePath": "api/Store/nearby", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "latitude", "Type": "System.Double", "IsRequired": false}, {"Name": "longitude", "Type": "System.Double", "IsRequired": false}, {"Name": "maxDistance", "Type": "System.Double", "IsRequired": false}, {"Name": "batteryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "GetApplicationInfo", "RelativePath": "api/SystemMonitor/app-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "ClearPerformanceStats", "RelativePath": "api/SystemMonitor/clear-stats", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "ForceGarbageCollection", "RelativePath": "api/SystemMonitor/gc", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "GetMemoryInfo", "RelativePath": "api/SystemMonitor/memory", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "GetSystemOverview", "RelativePath": "api/SystemMonitor/overview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "GetPerformanceStats", "RelativePath": "api/SystemMonitor/performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "category", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.SystemMonitorController", "Method": "GetSlowOperations", "RelativePath": "api/SystemMonitor/slow-operations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "thresholdMs", "Type": "System.Int32", "IsRequired": false}, {"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "GetUsers", "RelativePath": "api/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "IsVerified", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsOnline", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.Common.PagedResponse`1[[BatteryApi.DTOs.User.UserDto, BatteryApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userDto", "Type": "BatteryApi.DTOs.User.UserCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.User.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "GetUser", "RelativePath": "api/users/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.User.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "userDto", "Type": "BatteryApi.DTOs.User.UserUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.User.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "ResetPassword", "RelativePath": "api/users/{id}/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "passwordDto", "Type": "BatteryApi.DTOs.User.UserPasswordUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "BatteryApi.Controllers.UserController", "Method": "UpdateUserStatus", "RelativePath": "api/users/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "statusDto", "Type": "BatteryApi.DTOs.User.UserStatusUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "BatteryApi.DTOs.User.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}]