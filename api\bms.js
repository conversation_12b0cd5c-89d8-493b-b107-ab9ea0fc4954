/**
 * BMS系统API服务
 */
import request from '@/utils/request';

// API接口
const BMSAPI = {
  /**
   * 根据MAC ID获取电池参数信息
   * @param {String} macid 电池设备的MAC ID
   * @returns {Promise} Promise对象
   */
  getBatteryParams(macid) {
    if (!macid || macid.trim() === '') {
      return Promise.reject(new Error('MAC ID不能为空'));
    }

    console.log('获取电池参数，MAC ID:', macid);
    return request.get(`/api/bms/battery-params/${macid}`);
  },

  /**
   * 获取电池状态信息（简化版）
   * @param {String} macid 电池设备的MAC ID
   * @returns {Promise} Promise对象
   */
  getBatteryStatus(macid) {
    if (!macid || macid.trim() === '') {
      return Promise.reject(new Error('MAC ID不能为空'));
    }

    console.log('获取电池状态，MAC ID:', macid);
    return request.get(`/api/bms/battery-status/${macid}`);
  },

  /**
   * 根据MAC ID生成电池编码和信息（用于电池新增页面）
   * @param {String} macid 电池设备的MAC ID
   * @returns {Promise} Promise对象
   */
  generateBatteryCode(macid) {
    if (!macid || macid.trim() === '') {
      return Promise.reject(new Error('MAC ID不能为空'));
    }

    console.log('生成电池编码，MAC ID:', macid);
    return request.get(`/api/bms/generate-battery-code/${macid}`);
  },

  /**
   * 清除BMS系统Token缓存
   * @returns {Promise} Promise对象
   */
  clearTokenCache() {
    console.log('清除BMS Token缓存');
    return request.post('/api/bms/clear-token-cache');
  },

  /**
   * 测试BMS系统连接
   * @returns {Promise} Promise对象
   */
  testConnection() {
    console.log('测试BMS系统连接');
    return request.get('/api/bms/test-connection');
  },

  /**
   * 测试BMS数据解析
   * @returns {Promise} Promise对象
   */
  testParse() {
    console.log('测试BMS数据解析');
    return request.get('/api/bms/test-parse');
  }
};

export default BMSAPI;
