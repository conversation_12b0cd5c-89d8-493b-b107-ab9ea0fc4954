<template>
  <view class="bms-list-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="header-content">
        <text class="header-title">电池BMS状态监控</text>
        <view class="header-actions">
          <view class="refresh-btn" @click="refreshAllData">
            <text class="refresh-icon">🔄</text>
            <text>刷新全部</text>
          </view>
          <view class="auto-refresh-btn" @click="toggleAutoRefresh" :class="{ active: autoRefreshEnabled }">
            <text class="auto-icon">⏰</text>
            <text>{{ autoRefreshEnabled ? '停止自动刷新' : '开启自动刷新' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-card">
        <view class="stat-value">{{ batteryStats.total }}</view>
        <view class="stat-label">总电池数</view>
      </view>
      <view class="stat-card online">
        <view class="stat-value">{{ batteryStats.online }}</view>
        <view class="stat-label">在线电池</view>
      </view>
      <view class="stat-card offline">
        <view class="stat-value">{{ batteryStats.offline }}</view>
        <view class="stat-label">离线电池</view>
      </view>
      <view class="stat-card fault">
        <view class="stat-value">{{ batteryStats.fault }}</view>
        <view class="stat-label">故障电池</view>
      </view>
    </view>

    <!-- 筛选和搜索 -->
    <view class="filter-section">
      <view class="search-box">
        <text class="search-icon">🔍</text>
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索电池序列号或MAC ID"
          @input="handleSearch"
        />
        <text class="clear-icon" v-if="searchKeyword" @click="clearSearch">×</text>
      </view>
      <view class="filter-tabs">
        <view
          class="filter-tab"
          :class="{ active: activeFilter === 'all' }"
          @click="setFilter('all')"
        >
          全部
        </view>
        <view
          class="filter-tab"
          :class="{ active: activeFilter === 'online' }"
          @click="setFilter('online')"
        >
          在线
        </view>
        <view
          class="filter-tab"
          :class="{ active: activeFilter === 'offline' }"
          @click="setFilter('offline')"
        >
          离线
        </view>
        <view
          class="filter-tab"
          :class="{ active: activeFilter === 'fault' }"
          @click="setFilter('fault')"
        >
          故障
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在获取电池BMS数据...</text>
    </view>

    <!-- 电池BMS列表 -->
    <scroll-view
      v-else
      scroll-y
      class="battery-list"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 空状态 -->
      <view v-if="filteredBatteries.length === 0" class="empty-container">
        <text class="empty-icon">🔋</text>
        <text class="empty-text">{{ searchKeyword ? '未找到匹配的电池' : '暂无电池数据' }}</text>
        <button class="reload-btn" @click="loadBatteryList">重新加载</button>
      </view>

      <!-- 电池卡片列表 -->
      <view
        v-for="(battery, index) in filteredBatteries"
        :key="battery.id || index"
        class="battery-card"
        @click="viewBMSDetail(battery)"
      >
        <!-- 电池基本信息 -->
        <view class="battery-header">
          <view class="battery-info">
            <text class="battery-code">{{ battery.code || battery.serialNumber || `BAT-${battery.id}` }}</text>
            <text class="battery-mac">MAC: {{ battery.serialNumber || 'N/A' }}</text>
          </view>
          <view class="battery-status" :class="getBatteryStatusClass(battery)">
            <text class="status-dot"></text>
            <text class="status-text">{{ getBatteryStatusText(battery) }}</text>
          </view>
        </view>

        <!-- BMS状态信息 -->
        <view v-if="battery.bmsData" class="bms-info">
          <view class="bms-row">
            <view class="bms-item">
              <text class="bms-label">电压</text>
              <text class="bms-value">{{ battery.bmsData.voltageAll || 'N/A' }}V</text>
            </view>
            <view class="bms-item">
              <text class="bms-label">电流</text>
              <text class="bms-value">{{ battery.bmsData.current || 'N/A' }}A</text>
            </view>
            <view class="bms-item">
              <text class="bms-label">SOC</text>
              <text class="bms-value soc" :class="getSocClass(battery.bmsData.soc)">{{ battery.bmsData.soc || 'N/A' }}%</text>
            </view>
          </view>
          <view class="bms-row">
            <view class="bms-item">
              <text class="bms-label">温度</text>
              <text class="bms-value temp" :class="getTempClass(battery.bmsData.maxTemp)">{{ battery.bmsData.maxTemp || 'N/A' }}℃</text>
            </view>
            <view class="bms-item">
              <text class="bms-label">功率</text>
              <text class="bms-value">{{ battery.bmsData.power || 'N/A' }}W</text>
            </view>
            <view class="bms-item">
              <text class="bms-label">循环</text>
              <text class="bms-value">{{ battery.bmsData.cycle || 'N/A' }}</text>
            </view>
          </view>
        </view>

        <!-- 无BMS数据状态 -->
        <view v-else class="no-bms-info">
          <text class="no-bms-icon">⚠️</text>
          <text class="no-bms-text">无BMS数据</text>
        </view>

        <!-- 故障和报警信息 -->
        <view v-if="battery.bmsData && (battery.bmsData.faultState?.length > 0 || battery.bmsData.alarmState?.length > 0)" class="fault-info">
          <view v-if="battery.bmsData.faultState?.length > 0" class="fault-list">
            <text class="fault-icon">🚨</text>
            <text class="fault-text">{{ battery.bmsData.faultState.join(', ') }}</text>
          </view>
          <view v-if="battery.bmsData.alarmState?.length > 0" class="alarm-list">
            <text class="alarm-icon">⚠️</text>
            <text class="alarm-text">{{ battery.bmsData.alarmState.join(', ') }}</text>
          </view>
        </view>

        <!-- 最后更新时间 -->
        <view class="update-time">
          <text class="time-label">最后更新:</text>
          <text class="time-value">{{ formatUpdateTime(battery.bmsData?.timestamp) }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="battery-actions">
          <button class="action-btn detail" @click.stop="viewBMSDetail(battery)">
            详细信息
          </button>
          <button class="action-btn refresh" @click.stop="refreshSingleBattery(battery)">
            刷新数据
          </button>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <text class="load-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-else-if="filteredBatteries.length > 0" class="no-more">
        <text>已显示全部数据</text>
      </view>
    </scroll-view>

    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <view class="fab" @click="showBatchActions = !showBatchActions">
        <text class="fab-icon">⚙️</text>
      </view>

      <!-- 批量操作菜单 -->
      <view v-if="showBatchActions" class="batch-menu">
        <view class="batch-item" @click="exportBMSData">
          <text class="batch-icon">📊</text>
          <text class="batch-text">导出数据</text>
        </view>
        <view class="batch-item" @click="refreshAllData">
          <text class="batch-icon">🔄</text>
          <text class="batch-text">刷新全部</text>
        </view>
        <view class="batch-item" @click="showSettings">
          <text class="batch-icon">⚙️</text>
          <text class="batch-text">设置</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import BMSAPI from '@/api/bms.js'
import BatteryAPI from '@/api/battery.js'

export default {
  name: 'BMSList',
  data() {
    return {
      batteries: [],
      loading: false,
      refreshing: false,
      searchKeyword: '',
      activeFilter: 'all',
      autoRefreshEnabled: false,
      autoRefreshTimer: null,
      showBatchActions: false,
      hasMore: true,
      page: 1,
      pageSize: 20
    }
  },

  computed: {
    // 电池统计信息
    batteryStats() {
      const total = this.batteries.length
      const online = this.batteries.filter(b => this.isBatteryOnline(b)).length
      const offline = total - online
      const fault = this.batteries.filter(b => this.hasBatteryFault(b)).length

      return { total, online, offline, fault }
    },

    // 过滤后的电池列表
    filteredBatteries() {
      let filtered = this.batteries

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(battery => {
          const code = (battery.code || battery.serialNumber || '').toLowerCase()
          const mac = (battery.serialNumber || '').toLowerCase()
          return code.includes(keyword) || mac.includes(keyword)
        })
      }

      // 状态过滤
      switch (this.activeFilter) {
        case 'online':
          filtered = filtered.filter(b => this.isBatteryOnline(b))
          break
        case 'offline':
          filtered = filtered.filter(b => !this.isBatteryOnline(b))
          break
        case 'fault':
          filtered = filtered.filter(b => this.hasBatteryFault(b))
          break
      }

      return filtered
    }
  },

  onLoad() {
    this.loadBatteryList()
  },

  onUnload() {
    this.stopAutoRefresh()
  },

  methods: {
    // 加载电池列表
    async loadBatteryList(refresh = false) {
      if (refresh) {
        this.page = 1
        this.hasMore = true
      }

      this.loading = true

      try {
        console.log('加载电池列表...')

        // 获取电池基础信息
        const response = await BatteryAPI.getBatteryList({
          page: this.page,
          pageSize: this.pageSize
        })

        console.log('电池列表响应:', response)

        if (response && response.data) {
          const newBatteries = Array.isArray(response.data) ? response.data : []

          if (refresh) {
            this.batteries = newBatteries
          } else {
            this.batteries = [...this.batteries, ...newBatteries]
          }

          // 检查是否还有更多数据
          this.hasMore = newBatteries.length === this.pageSize

          // 为每个有序列号的电池获取BMS数据
          await this.loadBMSDataForBatteries()
        }
      } catch (error) {
        console.error('加载电池列表失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 为电池加载BMS数据
    async loadBMSDataForBatteries() {
      const batteriesWithSerial = this.batteries.filter(b => b.serialNumber)

      console.log(`为 ${batteriesWithSerial.length} 个电池加载BMS数据`)

      // 并发获取BMS数据，但限制并发数量
      const batchSize = 5
      for (let i = 0; i < batteriesWithSerial.length; i += batchSize) {
        const batch = batteriesWithSerial.slice(i, i + batchSize)
        const promises = batch.map(battery => this.loadSingleBMSData(battery))

        try {
          await Promise.allSettled(promises)
        } catch (error) {
          console.error('批量加载BMS数据失败:', error)
        }
      }
    },

    // 加载单个电池的BMS数据
    async loadSingleBMSData(battery) {
      if (!battery.serialNumber) return

      try {
        console.log(`获取电池 ${battery.serialNumber} 的BMS数据`)

        const response = await BMSAPI.getBatteryStatus(battery.serialNumber)

        if (response && response.data && response.data.data) {
          // 更新电池的BMS数据
          const index = this.batteries.findIndex(b => b.id === battery.id)
          if (index !== -1) {
            this.$set(this.batteries[index], 'bmsData', {
              ...response.data.data.state,
              timestamp: response.data.data.time,
              sets: response.data.data.sets
            })
          }
        }
      } catch (error) {
        console.error(`获取电池 ${battery.serialNumber} BMS数据失败:`, error)
        // 设置离线状态
        const index = this.batteries.findIndex(b => b.id === battery.id)
        if (index !== -1) {
          this.$set(this.batteries[index], 'bmsData', null)
        }
      }
    },

    // 刷新所有数据
    async refreshAllData() {
      this.refreshing = true
      await this.loadBatteryList(true)
    },

    // 刷新单个电池数据
    async refreshSingleBattery(battery) {
      uni.showLoading({ title: '刷新中...' })

      try {
        await this.loadSingleBMSData(battery)
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 下拉刷新
    async onRefresh() {
      await this.refreshAllData()
    },

    // 加载更多
    async loadMore() {
      if (!this.hasMore || this.loading) return

      this.page++
      await this.loadBatteryList()
    },

    // 搜索处理
    handleSearch() {
      // 实时搜索，无需额外处理
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
    },

    // 设置过滤器
    setFilter(filter) {
      this.activeFilter = filter
    },

    // 切换自动刷新
    toggleAutoRefresh() {
      if (this.autoRefreshEnabled) {
        this.stopAutoRefresh()
      } else {
        this.startAutoRefresh()
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.autoRefreshEnabled = true
      this.autoRefreshTimer = setInterval(() => {
        this.loadBMSDataForBatteries()
      }, 30000) // 每30秒刷新一次

      uni.showToast({
        title: '已开启自动刷新',
        icon: 'success'
      })
    },

    // 停止自动刷新
    stopAutoRefresh() {
      this.autoRefreshEnabled = false
      if (this.autoRefreshTimer) {
        clearInterval(this.autoRefreshTimer)
        this.autoRefreshTimer = null
      }
    },

    // 查看BMS详情
    viewBMSDetail(battery) {
      if (!battery.serialNumber) {
        uni.showToast({
          title: '该电池没有序列号，无法查看BMS状态',
          icon: 'none'
        })
        return
      }

      uni.navigateTo({
        url: `/pages/admin/battery/bms-detail?macId=${battery.serialNumber}`
      })
    },

    // 判断电池是否在线
    isBatteryOnline(battery) {
      if (!battery.bmsData) return false

      // 如果有时间戳，检查是否在5分钟内更新过
      if (battery.bmsData.timestamp) {
        const now = Date.now() / 1000
        const lastUpdate = battery.bmsData.timestamp
        return (now - lastUpdate) < 300 // 5分钟
      }

      // 如果有连接状态，直接使用
      return battery.bmsData.connectState === 1
    },

    // 判断电池是否有故障
    hasBatteryFault(battery) {
      if (!battery.bmsData) return false

      return (battery.bmsData.faultState && battery.bmsData.faultState.length > 0) ||
             (battery.bmsData.alarmState && battery.bmsData.alarmState.length > 0)
    },

    // 获取电池状态样式类
    getBatteryStatusClass(battery) {
      if (this.hasBatteryFault(battery)) return 'fault'
      if (this.isBatteryOnline(battery)) return 'online'
      return 'offline'
    },

    // 获取电池状态文本
    getBatteryStatusText(battery) {
      if (this.hasBatteryFault(battery)) return '故障'
      if (this.isBatteryOnline(battery)) return '在线'
      return '离线'
    },

    // 获取SOC样式类
    getSocClass(soc) {
      if (!soc) return ''
      if (soc > 80) return 'high'
      if (soc > 20) return 'medium'
      return 'low'
    },

    // 获取温度样式类
    getTempClass(temp) {
      if (!temp) return ''
      if (temp > 45) return 'high'
      if (temp < 0) return 'low'
      return 'normal'
    },

    // 格式化更新时间
    formatUpdateTime(timestamp) {
      if (!timestamp) return '未知'

      const now = Date.now() / 1000
      const diff = now - timestamp

      if (diff < 60) return '刚刚'
      if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
      if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`

      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN')
    },

    // 导出BMS数据
    exportBMSData() {
      this.showBatchActions = false

      uni.showActionSheet({
        itemList: ['导出当前筛选结果', '导出所有电池数据', '导出故障电池数据'],
        success: (res) => {
          let exportData = []

          switch (res.tapIndex) {
            case 0:
              exportData = this.filteredBatteries
              break
            case 1:
              exportData = this.batteries
              break
            case 2:
              exportData = this.batteries.filter(b => this.hasBatteryFault(b))
              break
          }

          console.log('导出数据:', exportData)
          uni.showToast({
            title: `导出${exportData.length}条数据`,
            icon: 'success'
          })
        }
      })
    },

    // 显示设置
    showSettings() {
      this.showBatchActions = false

      uni.showActionSheet({
        itemList: ['自动刷新设置', '显示设置', '通知设置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showAutoRefreshSettings()
              break
            case 1:
              this.showDisplaySettings()
              break
            case 2:
              this.showNotificationSettings()
              break
          }
        }
      })
    },

    // 显示自动刷新设置
    showAutoRefreshSettings() {
      uni.showModal({
        title: '自动刷新设置',
        content: '当前每30秒自动刷新一次BMS数据',
        showCancel: true,
        cancelText: '关闭自动刷新',
        confirmText: '保持开启',
        success: (res) => {
          if (res.cancel) {
            this.stopAutoRefresh()
          }
        }
      })
    },

    // 显示显示设置
    showDisplaySettings() {
      uni.showToast({
        title: '显示设置功能开发中',
        icon: 'none'
      })
    },

    // 显示通知设置
    showNotificationSettings() {
      uni.showToast({
        title: '通知设置功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bms-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx;
  color: white;

  .header-content {
    .header-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 15rpx;
    }

    .header-actions {
      display: flex;
      gap: 15rpx;

      .refresh-btn, .auto-refresh-btn {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.2);
        padding: 8rpx 15rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        .refresh-icon, .auto-icon {
          margin-right: 5rpx;
        }

        &.active {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

.stats-section {
  display: flex;
  padding: 20rpx;
  gap: 15rpx;

  .stat-card {
    flex: 1;
    background: white;
    padding: 20rpx;
    border-radius: 10rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .stat-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 5rpx;
    }

    .stat-label {
      font-size: 22rpx;
      color: #666;
    }

    &.online .stat-value {
      color: #4caf50;
    }

    &.offline .stat-value {
      color: #ff9800;
    }

    &.fault .stat-value {
      color: #f44336;
    }
  }
}

.filter-section {
  background: white;
  padding: 20rpx;
  margin-bottom: 10rpx;

  .search-box {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 25rpx;
    padding: 0 20rpx;
    margin-bottom: 20rpx;

    .search-icon {
      font-size: 28rpx;
      color: #999;
      margin-right: 10rpx;
    }

    input {
      flex: 1;
      height: 70rpx;
      font-size: 28rpx;
      background: transparent;
    }

    .clear-icon {
      font-size: 32rpx;
      color: #ccc;
      margin-left: 10rpx;
    }
  }

  .filter-tabs {
    display: flex;
    gap: 10rpx;

    .filter-tab {
      flex: 1;
      text-align: center;
      padding: 15rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      font-size: 26rpx;
      color: #666;

      &.active {
        background: #667eea;
        color: white;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.battery-list {
  flex: 1;
  padding: 0 20rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
  }

  .reload-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
  }
}

.battery-card {
  background: white;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .battery-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;

    .battery-info {
      flex: 1;

      .battery-code {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .battery-mac {
        font-size: 24rpx;
        color: #666;
      }
    }

    .battery-status {
      display: flex;
      align-items: center;
      padding: 8rpx 15rpx;
      border-radius: 15rpx;

      .status-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        margin-right: 8rpx;
      }

      .status-text {
        font-size: 24rpx;
        font-weight: bold;
      }

      &.online {
        background: rgba(76, 175, 80, 0.1);
        color: #4caf50;

        .status-dot {
          background: #4caf50;
        }
      }

      &.offline {
        background: rgba(255, 152, 0, 0.1);
        color: #ff9800;

        .status-dot {
          background: #ff9800;
        }
      }

      &.fault {
        background: rgba(244, 67, 54, 0.1);
        color: #f44336;

        .status-dot {
          background: #f44336;
        }
      }
    }
  }

  .bms-info {
    margin-bottom: 20rpx;

    .bms-row {
      display: flex;
      margin-bottom: 15rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .bms-item {
        flex: 1;
        text-align: center;

        .bms-label {
          display: block;
          font-size: 22rpx;
          color: #999;
          margin-bottom: 5rpx;
        }

        .bms-value {
          display: block;
          font-size: 26rpx;
          font-weight: bold;
          color: #333;

          &.soc {
            &.high {
              color: #4caf50;
            }

            &.medium {
              color: #ff9800;
            }

            &.low {
              color: #f44336;
            }
          }

          &.temp {
            &.high {
              color: #f44336;
            }

            &.low {
              color: #2196f3;
            }

            &.normal {
              color: #4caf50;
            }
          }
        }
      }
    }
  }

  .no-bms-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    background: #f5f5f5;
    border-radius: 10rpx;
    margin-bottom: 20rpx;

    .no-bms-icon {
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    .no-bms-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  .fault-info {
    margin-bottom: 20rpx;

    .fault-list, .alarm-list {
      display: flex;
      align-items: center;
      padding: 10rpx 15rpx;
      border-radius: 8rpx;
      margin-bottom: 10rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .fault-icon, .alarm-icon {
        font-size: 20rpx;
        margin-right: 8rpx;
      }

      .fault-text, .alarm-text {
        flex: 1;
        font-size: 22rpx;
      }
    }

    .fault-list {
      background: rgba(244, 67, 54, 0.1);
      color: #f44336;
    }

    .alarm-list {
      background: rgba(255, 152, 0, 0.1);
      color: #ff9800;
    }
  }

  .update-time {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;

    .time-label {
      font-size: 22rpx;
      color: #999;
      margin-right: 8rpx;
    }

    .time-value {
      font-size: 22rpx;
      color: #666;
    }
  }

  .battery-actions {
    display: flex;
    gap: 15rpx;

    .action-btn {
      flex: 1;
      padding: 15rpx;
      border-radius: 8rpx;
      font-size: 26rpx;
      text-align: center;
      border: none;

      &.detail {
        background: #667eea;
        color: white;
      }

      &.refresh {
        background: rgba(76, 175, 80, 0.1);
        color: #4caf50;
        border: 1rpx solid #4caf50;
      }
    }
  }
}

.load-more, .no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 24rpx;
}

.fab-container {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  z-index: 1000;

  .fab {
    width: 100rpx;
    height: 100rpx;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);

    .fab-icon {
      font-size: 36rpx;
      color: white;
    }
  }

  .batch-menu {
    position: absolute;
    bottom: 120rpx;
    right: 0;
    background: white;
    border-radius: 15rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
    overflow: hidden;
    min-width: 200rpx;

    .batch-item {
      display: flex;
      align-items: center;
      padding: 25rpx 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .batch-icon {
        font-size: 28rpx;
        margin-right: 15rpx;
      }

      .batch-text {
        font-size: 28rpx;
        color: #333;
      }

      &:active {
        background: #f5f5f5;
      }
    }
  }
}
</style>
