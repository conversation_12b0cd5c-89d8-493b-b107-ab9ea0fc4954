<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testBatterySpecs()">Test getBatterySpecs</button>
    <div id="result"></div>

    <script>
        async function testBatterySpecs() {
            try {
                const response = await fetch('http://localhost:8080/api/battery-specs');
                const result = document.getElementById('result');
                
                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = '<p style="color: green;">Success: ' + JSON.stringify(data) + '</p>';
                } else {
                    result.innerHTML = '<p style="color: red;">Error: ' + response.status + ' - ' + response.statusText + '</p>';
                }
            } catch (error) {
                const result = document.getElementById('result');
                result.innerHTML = '<p style="color: red;">Network Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
