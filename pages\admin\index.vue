<template>
  <view class="admin-container">
    <scroll-view
      class="content-container"
      scroll-y
      :style="{ paddingBottom: '100rpx' }"
    >
      <store-Info v-if="value5 === 0" :key="value5"></store-Info>
      <battery-List v-if="value5 === 1" :key="value5"></battery-List>
      <order-List v-if="value5 === 2" :key="value5"></order-List>
      <product-inventory v-if="value5 === 3" :key="value5"></product-inventory>
      <admin-Info v-if="value5 === 4" :key="value5"></admin-Info>
    </scroll-view>

    <view class="tabbar-container">
      <view class="custom-tabbar">
        <view
          class="tabbar-item"
          v-for="(item, index) in tabBarList"
          :key="index"
          :class="{ active: value5 === index }"
          @click="change5(index)"
        >
          <image class="tabbar-icon" :src="item.iconPath" mode="aspectFit"></image>
          <text class="tabbar-text">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import storeInfo from "@/pages/admin/store/inventory.vue"
import batteryList from "@/pages/admin/product/list.vue"
import orderList from "@/pages/admin/order/list.vue"
import productInventory from "@/pages/admin/inventory-management/index.vue"
import adminInfo from "@/pages/admin/admininfo.vue"

export default {
  name: "AdminIndex",
  data() {
    return {
      value5: 0,
      tabBarList: [],
    };
  },
  components: {
    batteryList,
    orderList,
    storeInfo,
    productInventory,
    adminInfo,
  },
  mounted() {
    const that = this;
    uni.getStorage({
      key: "tabBarList",
      success: function (res) {
        that.tabBarList = res.data;
      },
    });
  },
  methods: {
    change5(name) {
      this.value5 = name;
      // 切换tab时滚动到顶部
      // 使用uni-app的API代替document.querySelector
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.admin-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  background-color: #f8f8f8;
}

.content-container {
  flex: 1;
  height: calc(100vh - 100rpx); /* 减去tabbar的高度 */
  position: relative;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
}

.tabbar-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 自定义 tabbar 样式 */
.custom-tabbar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  padding: 10rpx 0;
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tabbar-text {
  font-size: 24rpx;
  color: #666;
}

.tabbar-item.active {
  .tabbar-text {
    color: #3c9cff;
  }
}
</style>
