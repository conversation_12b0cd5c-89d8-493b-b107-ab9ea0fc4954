using BatteryApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BatteryApi.Controllers
{
    [Route("api/store-inventory")]
    [ApiController]
    [Authorize]
    public class StoreInventoryController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<StoreInventoryController> _logger;

        public StoreInventoryController(ISqlSugarClient db, ILogger<StoreInventoryController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取门店商品库存
        /// </summary>
        /// <param name="storeId">门店ID</param>
        /// <param name="keyword">关键词（可选）</param>
        /// <param name="forceRefresh">是否强制刷新（可选）</param>
        /// <returns>门店商品库存列表</returns>
        [HttpGet("{storeId}")]
        public async Task<IActionResult> GetStoreInventory(int storeId, string keyword = null, bool forceRefresh = false)
        {
            try
            {
                _logger.LogInformation("获取门店商品库存，门店ID：{StoreId}，关键词：{Keyword}，强制刷新：{ForceRefresh}",
                    storeId, keyword, forceRefresh);

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == storeId);
                if (store == null)
                {
                    _logger.LogWarning("门店不存在，ID：{StoreId}", storeId);
                    return NotFound(new { success = false, message = $"门店ID {storeId} 不存在" });
                }

                // 获取门店所有商品库存
                var inventoryItems = await _db.Queryable<StoreInventory>()
                    .Where(i => i.StoreId == storeId)
                    .ToListAsync();

                _logger.LogInformation("门店 {StoreId} 共有 {Count} 条库存记录", storeId, inventoryItems.Count);

                // 如果没有库存记录，返回空列表
                if (inventoryItems.Count == 0)
                {
                    _logger.LogInformation("门店 {StoreId} 没有库存记录，返回空列表", storeId);
                    return Ok(new { success = true, data = new List<StoreInventoryDto>() });
                }

                // 获取所有相关商品信息
                var productIds = inventoryItems.Select(i => i.ProductId).Distinct().ToList();
                var products = await _db.Queryable<Product>()
                    .Where(p => productIds.Contains(p.Id))
                    .ToListAsync();

                _logger.LogInformation("找到 {Count} 个相关商品", products.Count);

                // 构建返回结果
                var inventoryDtoList = new List<StoreInventoryDto>();
                foreach (var item in inventoryItems)
                {
                    var product = products.FirstOrDefault(p => p.Id == item.ProductId);
                    if (product != null)
                    {
                        inventoryDtoList.Add(new StoreInventoryDto
                        {
                            Id = item.Id,
                            StoreId = item.StoreId,
                            StoreName = store.Name,
                            ProductId = item.ProductId,
                            ProductName = product.Model,
                            ProductSpec = $"{product.Voltage}V{product.Capacity}Ah",
                            CategoryId = product.CategoryId,
                            Quantity = item.Quantity,
                            AvailableQuantity = item.AvailableQuantity,
                            UpdatedAt = item.UpdatedAt
                        });
                    }
                    else
                    {
                        _logger.LogWarning("找不到商品信息，ID：{ProductId}", item.ProductId);
                    }
                }

                // 记录前5条记录的详细信息
                var top5 = inventoryDtoList.Take(5).ToList();
                foreach (var item in top5)
                {
                    _logger.LogInformation("库存记录：商品ID={ProductId}，商品名称={ProductName}，数量={Quantity}",
                        item.ProductId, item.ProductName, item.Quantity);
                }

                _logger.LogInformation("返回门店 {StoreId} 的商品库存，共 {Count} 条记录", storeId, inventoryDtoList.Count);
                return Ok(new { success = true, data = inventoryDtoList });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取门店商品库存失败，门店ID：{StoreId}", storeId);
                return StatusCode(500, new { success = false, message = "获取门店商品库存失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 更新门店商品库存
        /// </summary>
        /// <param name="storeId">门店ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPost("{storeId}/update")]
        [Authorize] // 允许所有已认证的用户调用
        public async Task<IActionResult> UpdateStoreInventory(int storeId, [FromBody] UpdateInventoryRequest request)
        {
            try
            {
                _logger.LogInformation("更新门店商品库存，门店ID：{StoreId}，商品数量：{ProductCount}，用户：{User}",
                    storeId, request?.Products?.Count ?? 0, User.Identity?.Name ?? "未知用户");

                // 验证参数
                if (request == null || request.Products == null || !request.Products.Any())
                {
                    return BadRequest(new { success = false, message = "请提供有效的商品数据" });
                }

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == storeId);
                if (store == null)
                {
                    _logger.LogWarning("门店不存在，ID：{StoreId}", storeId);
                    return NotFound(new { success = false, message = $"门店ID {storeId} 不存在" });
                }

                // 预处理商品数据，确保每个商品只处理一次
                var uniqueProducts = request.Products
                    .GroupBy(p => p.ProductId)
                    .Select(g => g.First())
                    .ToList();

                _logger.LogInformation("预处理后的商品数据: {@UniqueProducts}", uniqueProducts);

                // 预先获取所有相关商品信息
                var productIds = uniqueProducts.Select(p => p.ProductId).ToList();
                var products = await _db.Queryable<Product>()
                    .Where(p => productIds.Contains(p.Id))
                    .ToListAsync();

                _logger.LogInformation("找到商品信息: {Count} 个", products.Count);

                // 预先获取所有相关库存记录
                var existingInventories = await _db.Queryable<StoreInventory>()
                    .Where(i => i.StoreId == storeId && productIds.Contains(i.ProductId))
                    .ToListAsync();

                _logger.LogInformation("找到现有库存记录: {Count} 个", existingInventories.Count);

                // 开始事务
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var updatedProducts = new List<UpdatedProductDto>();
                    var newInventories = new List<StoreInventory>();
                    var updateInventories = new List<StoreInventory>();
                    var inventoryHistories = new List<InventoryHistory>();

                    foreach (var product in uniqueProducts)
                    {
                        _logger.LogInformation("处理商品 ID: {ProductId}, 数量: {Quantity}", product.ProductId, product.Quantity);

                        // 查找商品信息
                        var productEntity = products.FirstOrDefault(p => p.Id == product.ProductId);
                        string productName = "未知商品";
                        string productSpec = "未知规格";

                        if (productEntity == null)
                        {
                            _logger.LogWarning("商品不存在，ID: {ProductId}，但仍将创建库存记录", product.ProductId);
                        }
                        else
                        {
                            productName = productEntity.Name;
                            productSpec = $"{productEntity.Voltage}V{productEntity.Capacity}Ah";
                        }

                        // 查找库存记录
                        var inventory = existingInventories.FirstOrDefault(i => i.ProductId == product.ProductId);
                        int oldQuantity = 0;
                        bool isNewInventory = false;

                        if (inventory == null)
                        {
                            _logger.LogInformation("创建新的库存记录，商品ID: {ProductId}", product.ProductId);
                            isNewInventory = true;

                            // 创建新的库存记录
                            inventory = new StoreInventory
                            {
                                StoreId = storeId,
                                ProductId = product.ProductId,
                                Quantity = product.Quantity,
                                AvailableQuantity = product.Quantity,
                                UpdatedAt = DateTime.Now
                            };

                            newInventories.Add(inventory);
                        }
                        else
                        {
                            _logger.LogInformation("更新现有库存记录，商品ID: {ProductId}, 旧数量: {OldQuantity}, 新数量: {NewQuantity}",
                                product.ProductId, inventory.Quantity, product.Quantity);

                            // 更新现有库存记录
                            oldQuantity = inventory.Quantity;
                            inventory.Quantity = product.Quantity;
                            inventory.AvailableQuantity = product.Quantity;
                            inventory.UpdatedAt = DateTime.Now;

                            updateInventories.Add(inventory);
                        }

                        // 准备库存历史记录
                        string operationType = isNewInventory ? "Create" : "Update";
                        var history = new InventoryHistory
                        {
                            StoreId = storeId,
                            ProductId = product.ProductId,
                            OldQuantity = oldQuantity,
                            NewQuantity = product.Quantity,
                            Quantity = product.Quantity - oldQuantity,
                            OperationType = operationType,
                            OperatedBy = User.Identity.Name ?? "admin",
                            OperationTime = DateTime.Now,
                            Remark = $"{operationType} operation by {User.Identity.Name ?? "admin"}"
                        };

                        inventoryHistories.Add(history);

                        // 添加到返回结果
                        updatedProducts.Add(new UpdatedProductDto
                        {
                            ProductId = product.ProductId,
                            ProductName = productName,
                            ProductSpec = productSpec,
                            OldQuantity = oldQuantity,
                            NewQuantity = product.Quantity,
                            UpdatedAt = DateTime.Now
                        });
                    }

                    // 批量插入新库存记录
                    if (newInventories.Any())
                    {
                        _logger.LogInformation("准备批量插入 {Count} 条新库存记录", newInventories.Count);
                        _logger.LogInformation("事务状态: 活动");

                        // 记录每条记录的详细信息
                        foreach (var item in newInventories)
                        {
                            _logger.LogInformation("新库存记录: StoreId={StoreId}, ProductId={ProductId}, Quantity={Quantity}",
                                item.StoreId, item.ProductId, item.Quantity);
                        }

                        _logger.LogInformation("执行批量插入操作...");
                        var insertResult = await _db.Insertable(newInventories).ExecuteCommandAsync();
                        _logger.LogInformation("批量插入结果: 影响行数 {AffectedRows}", insertResult);

                        // 记录SQL语句（如果可能）
                        _logger.LogDebug("执行的SQL: INSERT INTO StoreInventory (StoreId, ProductId, Quantity, AvailableQuantity, UpdatedAt) VALUES ...");

                        if (insertResult != newInventories.Count)
                        {
                            _logger.LogWarning("插入行数不匹配，预期: {Expected}, 实际: {Actual}",
                                newInventories.Count, insertResult);
                        }
                    }

                    // 批量更新现有库存记录
                    if (updateInventories.Any())
                    {
                        _logger.LogInformation("批量更新 {Count} 条现有库存记录", updateInventories.Count);

                        // 记录每条记录的详细信息
                        foreach (var item in updateInventories)
                        {
                            _logger.LogInformation("更新库存记录: Id={Id}, StoreId={StoreId}, ProductId={ProductId}, Quantity={Quantity}",
                                item.Id, item.StoreId, item.ProductId, item.Quantity);
                        }

                        var updateResult = await _db.Updateable(updateInventories).ExecuteCommandAsync();
                        _logger.LogInformation("批量更新结果: 影响行数 {AffectedRows}", updateResult);

                        if (updateResult != updateInventories.Count)
                        {
                            _logger.LogWarning("更新行数不匹配，预期: {Expected}, 实际: {Actual}",
                                updateInventories.Count, updateResult);
                        }
                    }

                    // 批量插入库存历史记录
                    if (inventoryHistories.Any())
                    {
                        _logger.LogInformation("批量插入 {Count} 条库存历史记录", inventoryHistories.Count);

                        try
                        {
                            var historyResult = await _db.Insertable(inventoryHistories).ExecuteCommandAsync();
                            _logger.LogInformation("批量插入历史记录结果: 影响行数 {AffectedRows}", historyResult);
                        }
                        catch (Exception ex)
                        {
                            // 捕获历史记录插入错误，但不影响主流程
                            _logger.LogError(ex, "插入库存历史记录失败，可能是数据库表结构不匹配");
                            _logger.LogWarning("请执行 Scripts/AddMissingColumnsToInventoryHistory.sql 脚本添加缺失的列");
                        }
                    }

                    return updatedProducts;
                });

                if (!result.IsSuccess)
                {
                    _logger.LogError(result.ErrorException, "事务执行失败");
                    throw result.ErrorException;
                }

                _logger.LogInformation("事务已成功提交");

                var updatedProducts = result.Data as List<UpdatedProductDto>;

                if (updatedProducts == null)
                {
                    updatedProducts = new List<UpdatedProductDto>();
                }

                _logger.LogInformation("库存更新成功，更新商品数量: {Count}", updatedProducts.Count);

                // 验证更新是否成功
                var verifyInventories = await _db.Queryable<StoreInventory>()
                    .Where(i => i.StoreId == storeId && productIds.Contains(i.ProductId))
                    .ToListAsync();

                _logger.LogInformation("验证更新结果: 找到库存记录 {Count} 条", verifyInventories.Count);

                // 记录每条验证的库存记录
                foreach (var item in verifyInventories)
                {
                    _logger.LogInformation("验证库存记录: Id={Id}, StoreId={StoreId}, ProductId={ProductId}, Quantity={Quantity}",
                        item.Id, item.StoreId, item.ProductId, item.Quantity);
                }

                return Ok(new
                {
                    success = true,
                    message = "商品库存更新成功",
                    data = new
                    {
                        storeId,
                        storeName = store.Name,
                        updatedProducts,
                        updateTime = DateTime.Now
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新门店商品库存失败，门店ID：{StoreId}", storeId);
                return StatusCode(500, new { success = false, message = "更新门店商品库存失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 记录库存历史
        /// </summary>
        private async Task RecordInventoryHistory(int storeId, int productId, int oldQuantity, int newQuantity,
            string operationType, string operatedBy)
        {
            try
            {
                // 检查参数
                if (productId <= 0)
                {
                    _logger.LogWarning("记录库存历史失败：无效的商品ID {ProductId}", productId);
                    return;
                }

                var history = new InventoryHistory
                {
                    StoreId = storeId,
                    ProductId = productId,
                    OldQuantity = oldQuantity,
                    NewQuantity = newQuantity,
                    Quantity = newQuantity - oldQuantity, // 计算变化量
                    OperationType = operationType,
                    OperatedBy = operatedBy,
                    OperationTime = DateTime.Now,
                    Remark = $"{operationType} operation by {operatedBy}"
                };

                // 使用当前事务上下文插入历史记录
                // 这样可以确保历史记录与库存更新在同一个事务中
                try
                {
                    await _db.Insertable(history).ExecuteCommandAsync();
                    _logger.LogInformation("记录库存历史成功，门店ID：{StoreId}，商品ID：{ProductId}，操作类型：{OperationType}",
                        storeId, productId, operationType);
                }
                catch (Exception ex)
                {
                    // 捕获历史记录插入错误，但不影响主流程
                    _logger.LogError(ex, "插入库存历史记录失败，可能是数据库表结构不匹配");
                    _logger.LogWarning("请执行 Scripts/AddMissingColumnsToInventoryHistory.sql 脚本添加缺失的列");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录库存历史失败，门店ID：{StoreId}，商品ID：{ProductId}", storeId, productId);
                // 在开发环境中，可以考虑重新抛出异常以便调试
                // 在生产环境中，我们选择记录错误但不中断主流程
                // 注意：这可能导致库存更新成功但历史记录失败的情况
            }
        }
    }

    /// <summary>
    /// 更新库存请求
    /// </summary>
    public class UpdateInventoryRequest
    {
        /// <summary>
        /// 商品列表
        /// </summary>
        public List<ProductInventory> Products { get; set; }
    }

    /// <summary>
    /// 商品库存
    /// </summary>
    public class ProductInventory
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
    }

    /// <summary>
    /// 已更新商品DTO
    /// </summary>
    public class UpdatedProductDto
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        public string ProductSpec { get; set; }

        /// <summary>
        /// 原数量
        /// </summary>
        public int OldQuantity { get; set; }

        /// <summary>
        /// 新数量
        /// </summary>
        public int NewQuantity { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 门店商品库存DTO
    /// </summary>
    public class StoreInventoryDto
    {
        /// <summary>
        /// 库存ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        public int StoreId { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品规格
        /// </summary>
        public string ProductSpec { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 可用数量
        /// </summary>
        public int AvailableQuantity { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
}
