using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using BatteryApi.DTOs;
using BatteryApi.DTOs.Battery;
using BatteryApi.DTOs.Common;
using SqlSugar;
using System.Security.Policy;
using Microsoft.AspNetCore.Hosting;

namespace BatteryApi.Services.Implementations
{
    public partial class BatteryService : IBatteryService
    {
        private readonly ISqlSugarClient _db;
        private readonly IConfiguration _configuration;
        private readonly string _batteryPath;
        private readonly string _baseUrl; 

        public BatteryService(ISqlSugarClient db,
        IConfiguration configuration,
        IHttpContextAccessor httpContextAccessor )
        {
            _db = db;
            _configuration = configuration;

            // 获取配置的相对路径
            _batteryPath = _configuration.GetValue<string>("FileStorage:BatteryPath") ?? "uploads/batteries";
            // 构建基础URL
            var request = httpContextAccessor.HttpContext?.Request;
            if (request != null)
            {
                _baseUrl = $"{request.Scheme}://{request.Host}";
            }
            else
            {
                // 从配置中获取基础URL
                _baseUrl = _configuration.GetValue<string>("AppSettings:BaseUrl") ?? "http://localhost:5424";
            }

           
        }

        public async Task<PagedResponse<BatteryDto>> GetBatteriesAsync(BatteryQueryParameters queryParams)
        {
            // 使用 SqlSugar 查询构建器
            var query = _db.Queryable<Battery>()
                .LeftJoin<BatteryCategory>((b, c) => b.CategoryId == c.Id);

            // 应用查询条件
            if (queryParams != null)
            {
                if (!string.IsNullOrEmpty(queryParams.SearchTerm))
                {
                    query = query.Where((b, c) =>
                        b.Model.Contains(queryParams.SearchTerm) ||
                        b.Manufacturer.Contains(queryParams.SearchTerm) ||
                        b.Description.Contains(queryParams.SearchTerm));
                }

                if (!string.IsNullOrEmpty(queryParams.Status))
                {
                    query = query.Where((b, c) => b.Status == queryParams.Status);
                }

                if (queryParams.CategoryId.HasValue)
                {
                    query = query.Where((b, c) => b.CategoryId == queryParams.CategoryId.Value);
                }

                if (!string.IsNullOrEmpty(queryParams.CategoryCode))
                {
                    query = query.Where((b, c) => c.Code == queryParams.CategoryCode);
                }

                if (queryParams.FromDate.HasValue)
                {
                    query = query.Where((b, c) => b.ManufactureDate >= queryParams.FromDate.Value);
                }

                if (queryParams.ToDate.HasValue)
                {
                    query = query.Where((b, c) => b.ManufactureDate <= queryParams.ToDate.Value);
                }

                if (queryParams.IsAvailable.HasValue)
                {
                    var availableStatus = queryParams.IsAvailable.Value ? "Available" : "Unavailable";
                    query = query.Where((b, c) => b.Status == availableStatus);
                }
            }

            // 获取总记录数
            var totalCount = await query.CountAsync();

            // 应用排序
            if (!string.IsNullOrEmpty(queryParams?.SortBy))
            {
                if (queryParams.SortAscending)
                {
                    query = query.OrderBy($"b.{queryParams.SortBy} ASC");
                }
                else
                {
                    query = query.OrderBy($"b.{queryParams.SortBy} DESC");
                }
            }
            else
            {
                query = query.OrderBy("b.Id ASC");
            }

            // 应用分页
            var pageSize = Math.Max(1, queryParams?.PageSize ?? 10);
            var pageNumber = Math.Max(1, queryParams?.PageNumber ?? 1);
            var skip = (pageNumber - 1) * pageSize;

            // 投影到 DTO 并应用分页
            var batteries = await query
                .Select((b, c) => new BatteryDto
                {
                    Id = b.Id,
                    Model = b.Model,
                    Manufacturer = b.Manufacturer,
                    Voltage = b.Voltage,
                    Capacity = b.Capacity,
                    Spec = b.Spec,
                    Status = b.Status,
                    Price = b.Price,
                    RentPrice = b.RentPrice,
                    ManufactureDate = b.ManufactureDate,
                    Lifespan = b.Lifespan,
                    Description = b.Description,
                    Notes = b.Notes,
                    CategoryId = b.CategoryId,
                    CategoryName = c.Name,
                    CategoryCode = c.Code
                })
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync();

            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            return new PagedResponse<BatteryDto>(
                items: batteries,
                totalCount: totalCount,
                pageNumber: pageNumber,
                pageSize: pageSize);
        }

        public async Task<BatteryDto> GetBatteryByIdAsync(int id)
        {
            var battery = await _db.Queryable<Battery>()
                .LeftJoin<BatteryCategory>((b, c) => b.CategoryId == c.Id)
                .Where((b, c) => b.Id == id)
                .Select((b, c) => new BatteryDto
                {
                    Id = b.Id,
                    SerialNumber = b.SerialNumber,
                    Model = b.Model,
                    Manufacturer = b.Manufacturer,
                    Voltage = b.Voltage,
                    Capacity = b.Capacity,
                    Spec = b.Spec,
                    Status = b.Status,
                    Price = b.Price,
                    RentPrice = b.RentPrice,
                    ManufactureDate = b.ManufactureDate,
                    Lifespan = b.Lifespan,
                    Description = b.Description ?? string.Empty,
                    Notes = b.Notes ?? string.Empty,
                    CategoryId = b.CategoryId,
                    CategoryCode = c.Code ?? string.Empty,
                    CategoryName = c.Name ?? string.Empty,
                    CycleCount = b.CycleCount ?? string.Empty,
                    ChargeTime = b.ChargeTime ?? string.Empty,
                    CreatedAt = b.CreatedAt,
                    UpdatedAt = b.UpdatedAt
                })
                .FirstAsync();

            if (battery == null)
            {
                throw new KeyNotFoundException($"电池 ID {id} 不存在");
            }

            // 获取关联的数据
            var images = await _db.Queryable<BatteryImage>()
                .Where(img => img.BatteryId == id)
                .OrderBy(img => img.SortOrder)
                .ToListAsync();

            var services = await _db.Queryable<BatteryServiceEntity>()
                .Where(svc => svc.BatteryId == id)
                .OrderBy(svc => svc.SortOrder)
                .ToListAsync();

            var installationFees = await _db.Queryable<BatteryInstallationFee>()
                .Where(fee => fee.BatteryId == id)
                .ToListAsync();

            // 映射关联数据
            battery.Images = images.Select(img => new BatteryImageDto
            {
                Id = img.Id,
                BatteryId = img.BatteryId,
                ImageUrl = img.ImageUrl,
                RelativePath = img.RelativePath ?? string.Empty,
                ImageType = img.ImageType,
                IsMain = img.IsMain,
                SortOrder = img.SortOrder,
                FileName = img.FileName,
                FileSize = img.FileSize,
                Width = img.Width,
                Height = img.Height,
                CreatedAt = img.CreatedAt,
                UpdatedAt = img.UpdatedAt
            }).ToList();

            battery.Services = services.Select(svc => new BatteryServiceDto
            {
                Id = svc.Id,
                BatteryId = svc.BatteryId,
                Name = svc.Name,
                Description = svc.Description,
                Price = svc.Price,
                ServiceType = svc.ServiceType,
                SortOrder = svc.SortOrder,
                IsEnabled = svc.IsEnabled,
                CreatedAt = svc.CreatedAt,
                UpdatedAt = svc.UpdatedAt
            }).ToList();

            battery.InstallationFees = installationFees.Select(fee => new BatteryInstallationFeeDto
            {
                Id = fee.Id,
                BatteryId = fee.BatteryId,
                Name = fee.Name,
                Price = fee.Price,
                Description = fee.Description,
                CreatedAt = fee.CreatedAt,
                UpdatedAt = fee.UpdatedAt
            }).ToList();

            return battery;
        }

        public async Task<BatteryDto> CreateBatteryAsync(CreateBatteryRequest request)
        {
            try
            {
                Console.WriteLine($"🔍 开始创建电池，规格: {request.Spec}, 分类ID: {request.CategoryId}");

                // 验证分类是否存在
                var category = await _db.Queryable<BatteryCategory>()
                    .FirstAsync(c => c.Id == request.CategoryId);

                if (category == null)
                {
                    Console.WriteLine($"❌ 分类不存在，ID: {request.CategoryId}");
                    throw new ArgumentException($"分类 ID {request.CategoryId} 不存在");
                }

                Console.WriteLine($"✅ 找到分类: {category.Name} ({category.Code})");

                // 检查规格是否重复
                var specExists = await _db.Queryable<Battery>()
                    .AnyAsync(b => b.Spec == request.Spec && b.CategoryId == request.CategoryId);

                if (specExists)
                {
                    Console.WriteLine($"❌ 规格重复: {request.Spec} 在分类 {request.CategoryId} 下已存在");
                    throw new ArgumentException($"规格 {request.Spec} 在该分类下已存在");
                }

                Console.WriteLine($"✅ 规格检查通过: {request.Spec}");

                // 生成序列号
                var serialNumber = $"BAT{DateTime.Now:yyyyMMdd}{new Random().Next(1000, 9999)}";
                Console.WriteLine($"🔢 生成序列号: {serialNumber}");

                // 解析租赁价格
                decimal rentPrice = 0;
                if (!string.IsNullOrEmpty(request.RentPrice) && decimal.TryParse(request.RentPrice, out var parsedRentPrice))
                {
                    rentPrice = parsedRentPrice;
                }

            
                var battery = new Battery
                {
                    SerialNumber = serialNumber,
                    Model = request.Spec,
                    Manufacturer = "默认厂商", 
                    Spec = request.Spec,
                    Status = request.Status,
                    Price = request.Price,
                    RentPrice = rentPrice,
                    ManufactureDate = request.ManufactureDate ?? DateTime.Now,
                    Lifespan = request.Lifespan,
                    Description = request.Description,
                    Notes = string.Empty,
                    CategoryId = request.CategoryId, 
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                Console.WriteLine($"💾 准备插入电池数据到数据库");

                // 插入新电池并获取ID
                var newId = await _db.Insertable(battery).ExecuteReturnIdentityAsync();
                battery.Id = newId;

                Console.WriteLine($"✅ 电池插入成功，ID: {newId}");

                // 处理图片列表
                if (request.MainImages?.Any() == true)
                {
                    Console.WriteLine($"🖼️ 处理 {request.MainImages.Count} 张图片");
                    await ProcessBatteryImages(newId, request.MainImages);
                }

                // 处理服务列表
                if (request.Services?.Any() == true)
                {
                    Console.WriteLine($"🛠️ 处理 {request.Services.Count} 个服务");
                    await ProcessBatteryServices(newId, request.Services);
                }

                // 处理安装费用列表
                if (request.InstallationFees?.Any() == true)
                {
                    Console.WriteLine($"💰 处理 {request.InstallationFees.Count} 个安装费用");
                    await ProcessBatteryInstallationFees(newId, request.InstallationFees);
                }

                // 获取刚创建的关联数据
                var images = await _db.Queryable<BatteryImage>()
                    .Where(img => img.BatteryId == newId)
                    .OrderBy(img => img.SortOrder)
                    .ToListAsync();

                var services = await _db.Queryable<BatteryServiceEntity>()
                    .Where(svc => svc.BatteryId == newId)
                    .OrderBy(svc => svc.SortOrder)
                    .ToListAsync();

                var installationFees = await _db.Queryable<BatteryInstallationFee>()
                    .Where(fee => fee.BatteryId == newId)
                    .ToListAsync();

                var batteryDto = new BatteryDto
                {
                    Id = battery.Id,
                    SerialNumber = battery.SerialNumber,
                    Model = battery.Model,
                    Manufacturer = battery.Manufacturer,
                    Voltage = battery.Voltage,
                    Capacity = battery.Capacity,
                    Spec = battery.Spec,
                    Status = battery.Status,
                    Price = battery.Price,
                    RentPrice = battery.RentPrice,
                    ManufactureDate = battery.ManufactureDate,
                    Lifespan = battery.Lifespan,
                    Description = battery.Description,
                    Notes = battery.Notes,
                    CategoryId = battery.CategoryId,
                    CategoryCode = category?.Code ?? string.Empty,
                    CategoryName = category?.Name ?? string.Empty,
                    CycleCount = battery.CycleCount,
                    ChargeTime = battery.ChargeTime,
                    Images = images.Select(img => new BatteryImageDto
                    {
                        Id = img.Id,
                        BatteryId = img.BatteryId,
                        ImageUrl = img.ImageUrl,
                        RelativePath = img.RelativePath ?? string.Empty,
                        ImageType = img.ImageType,
                        IsMain = img.IsMain,
                        SortOrder = img.SortOrder,
                        FileName = img.FileName,
                        FileSize = img.FileSize,
                        Width = img.Width,
                        Height = img.Height,
                        CreatedAt = img.CreatedAt,
                        UpdatedAt = img.UpdatedAt
                    }).ToList(),
                    Services = services.Select(svc => new BatteryServiceDto
                    {
                        Id = svc.Id,
                        BatteryId = svc.BatteryId,
                        Name = svc.Name,
                        Description = svc.Description,
                        Price = svc.Price,
                        ServiceType = svc.ServiceType,
                        SortOrder = svc.SortOrder,
                        IsEnabled = svc.IsEnabled,
                        CreatedAt = svc.CreatedAt,
                        UpdatedAt = svc.UpdatedAt
                    }).ToList(),
                    InstallationFees = installationFees.Select(fee => new BatteryInstallationFeeDto
                    {
                        Id = fee.Id,
                        BatteryId = fee.BatteryId,
                        Name = fee.Name,
                        Price = fee.Price,
                        Description = fee.Description,
                        CreatedAt = fee.CreatedAt,
                        UpdatedAt = fee.UpdatedAt
                    }).ToList(),
                    CreatedAt = battery.CreatedAt,
                    UpdatedAt = battery.UpdatedAt
                };

                Console.WriteLine($"✅ 成功创建电池，ID: {battery.Id}, 序列号: {battery.SerialNumber}");
                return batteryDto;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建电池失败: {ex.Message}");
                Console.WriteLine($"异常详情: {ex.StackTrace}");
                throw;
            }
        }

     

        /// <summary>
        /// 处理电池图片（从 IFormFile 列表）
        /// </summary>
        private async Task ProcessBatteryImagesFromFiles(int batteryId, List<IFormFile> files, string imageType = "main")
        {
            if (files == null || !files.Any())
            {
                Console.WriteLine("📸 没有图片文件需要处理");
                return;
            }

            Console.WriteLine($"📸 开始处理 {files.Count} 个图片文件，电池ID: {batteryId}");

            // 确保目录存在
            string batteryIdStr = batteryId.ToString();
            string physicalPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", _batteryPath, batteryIdStr);
            if (!Directory.Exists(physicalPath))
            {
                Directory.CreateDirectory(physicalPath);
                Console.WriteLine($"📁 创建目录: {physicalPath}");
            }

            int sortOrder = 0;
            bool isFirstImage = true;

            foreach (var file in files)
            {
                try
                {
                    // 验证文件
                    if (file == null || file.Length == 0)
                    {
                        Console.WriteLine("⚠️ 跳过空文件");
                        continue;
                    }

                    // 验证文件类型
                    var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
                    var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        Console.WriteLine($"⚠️ 跳过不支持的文件类型: {file.FileName}");
                        continue;
                    }

                    // 生成唯一文件名
                    var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
                    var random = new Random().Next(1000, 9999);
                    var fileName = $"product_{batteryId}_{imageType}_{timestamp}_{random}{fileExtension}";

                    // 构建路径
                    string relativePath = $"{_batteryPath}/{batteryId}/{fileName}".Replace('\\', '/');
                    string imageUrl = $"{_baseUrl}/{relativePath}";
                    string imagePhysicalPath = Path.Combine(physicalPath, fileName);

                    // 保存文件
                    using (var stream = new FileStream(imagePhysicalPath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    // 创建数据库记录
                    var image = new BatteryImage
                    {
                        BatteryId = batteryId,
                        ImageUrl = imageUrl,
                        RelativePath = relativePath,
                        FileName = fileName,
                        FileSize = file.Length,
                        IsMain = isFirstImage, // 第一张图片设为主图
                        ImageType = imageType,
                        SortOrder = sortOrder++,
                        Width = null,
                        Height = null,
                        Description = $"{imageType}图片",
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    await _db.Insertable(image).ExecuteCommandAsync();
                    Console.WriteLine($"✅ 成功处理图片: {fileName}, 主图: {image.IsMain}");

                    isFirstImage = false;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 处理图片失败: {file.FileName}, 错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理电池图片（原有方法，处理 BatteryImageRequest）
        /// </summary>
        private async Task ProcessBatteryImages(int batteryId, List<BatteryImageRequest> images)
        {
            int sortOrder = 0;

            foreach (BatteryImageRequest imageRequest in images)
            {
                // 跳过空的图片请求
                if (string.IsNullOrWhiteSpace(imageRequest.Name) &&
                    string.IsNullOrWhiteSpace(imageRequest.Url) &&
                    string.IsNullOrWhiteSpace(imageRequest.TempPath))
                {
                    continue;
                }

                // 如果图片已经有URL（已上传），直接保存到数据库
                if (!string.IsNullOrWhiteSpace(imageRequest.Url))
                {
                    var image = new BatteryImage
                    {
                        BatteryId = batteryId,
                        ImageUrl = imageRequest.Url,
                        RelativePath = imageRequest.RelativePath ?? string.Empty,
                        FileName = imageRequest.Name ?? string.Empty,
                        FileSize = imageRequest.Size,
                        IsMain = imageRequest.IsMain,
                        ImageType = imageRequest.ImageType ?? "main",
                        SortOrder = sortOrder++,
                        Width = null,
                        Height = null,
                        Description = null,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    Console.WriteLine($"📸 插入已上传图片: {image.FileName}, 类型: {image.ImageType}, 主图: {image.IsMain}");
                    await _db.Insertable(image).ExecuteCommandAsync();
                }
                // 如果有临时路径，需要从临时路径复制文件
                else if (!string.IsNullOrWhiteSpace(imageRequest.TempPath) && File.Exists(imageRequest.TempPath))
                {
                    var contentRootPath = Directory.GetCurrentDirectory();
                    // 确保目标目录存在
                    string physicalPath = Path.Combine(contentRootPath, _batteryPath, batteryId.ToString());
                    if (!Directory.Exists(physicalPath))
                    {
                        Directory.CreateDirectory(physicalPath);
                    }

                    // 构建相对路径和完整URL
                    string relativePath = $"{_batteryPath}/{batteryId}/{imageRequest.Name}".Replace('\\', '/');
                    string imageUrl = $"{_baseUrl}/{relativePath}";

                    // 保存图片文件到本地
                    string imagePhysicalPath = Path.Combine(physicalPath, imageRequest.Name);
                    byte[] fileBytes = await File.ReadAllBytesAsync(imageRequest.TempPath);
                    using (var stream = new FileStream(imagePhysicalPath, FileMode.Create))
                    {
                        await stream.WriteAsync(fileBytes, 0, fileBytes.Length);
                    }

                    var image = new BatteryImage
                    {
                        BatteryId = batteryId,
                        ImageUrl = imageUrl,
                        RelativePath = relativePath,
                        FileName = imageRequest.Name ?? string.Empty,
                        FileSize = imageRequest.Size,
                        IsMain = imageRequest.IsMain,
                        ImageType = imageRequest.ImageType ?? "main",
                        SortOrder = sortOrder++,
                        Width = null,
                        Height = null,
                        Description = null,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    Console.WriteLine($"📸 插入临时路径图片: {image.FileName}, 类型: {image.ImageType}, 主图: {image.IsMain}");
                    await _db.Insertable(image).ExecuteCommandAsync();
                }
            }
        }

        /// <summary>
        /// 处理电池服务
        /// </summary>
        private async Task ProcessBatteryServices(int batteryId, List<BatteryServiceRequest> services)
        {
            int sortOrder = 0;

            foreach (var serviceRequest in services)
            {
                // 跳过空的服务名称
                if (string.IsNullOrWhiteSpace(serviceRequest.Name))
                {
                    continue;
                }

                var service = new BatteryServiceEntity
                {
                    BatteryId = batteryId,
                    Name = serviceRequest.Name,
                    Description = serviceRequest.Description ?? string.Empty,
                    Price = null, // 前端暂时不传价格
                    ServiceType = "default",
                    SortOrder = sortOrder++,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                Console.WriteLine($"🛠️ 插入服务: {service.Name}, 描述: {service.Description}");
                await _db.Insertable(service).ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// 处理电池安装费用
        /// </summary>
        private async Task ProcessBatteryInstallationFees(int batteryId, List<BatteryInstallationFeeRequest> fees)
        {
            foreach (var feeRequest in fees)
            {
                // 跳过空的费用名称
                if (string.IsNullOrWhiteSpace(feeRequest.Name))
                {
                    continue;
                }

                decimal price = 0;
                if (!string.IsNullOrEmpty(feeRequest.Price) && decimal.TryParse(feeRequest.Price, out var parsedPrice))
                {
                    price = parsedPrice;
                }

                var fee = new BatteryInstallationFee
                {
                    BatteryId = batteryId,
                    Name = feeRequest.Name,
                    Price = price,
                    Description = feeRequest.Description ?? string.Empty,
                    CreatedAt = DateTime.Now
                };

                await _db.Insertable(fee).ExecuteCommandAsync();
            }
        }

        public async Task<BatteryDto> UpdateBatteryAsync(int id, UpdateBatteryRequest request)
        {
            try
            {
                Console.WriteLine($"🔄 开始更新电池，ID: {id}");

                // 检查电池是否存在
                var existingBattery = await _db.Queryable<Battery>()
                    .FirstAsync(b => b.Id == id);

                if (existingBattery == null)
                {
                    throw new Exception($"电池ID {id} 不存在");
                }

                // 更新电池基本信息
                existingBattery.Spec = request.Spec;
                existingBattery.Status = GetStatusString(request.Status);
                existingBattery.Price = request.Price;
                existingBattery.RentPrice = ParseDecimal(request.RentPrice);
                existingBattery.ManufactureDate = request.ManufactureDate ?? DateTime.Now;
                existingBattery.Lifespan = request.Lifespan;
                existingBattery.Description = request.Description ?? string.Empty;
                existingBattery.CategoryId = request.CategoryId;
                existingBattery.Voltage = ParseDecimal(request.Voltage);
                existingBattery.Capacity = ParseDecimal(request.Capacity);
                existingBattery.CycleCount = request.CycleCount;
                existingBattery.ChargeTime = request.ChargeTime;
                existingBattery.UpdatedAt = DateTime.Now;

                Console.WriteLine($"💾 准备更新电池数据到数据库");

                // 更新电池基本信息
                await _db.Updateable(existingBattery).ExecuteCommandAsync();

                Console.WriteLine($"✅ 电池更新成功，ID: {id}");

                // 处理图片列表 - 如果有新图片需要处理
                if (request.MainImages?.Any() == true)
                {
                    Console.WriteLine($"🖼️ 处理 {request.MainImages.Count} 张图片");
                    await ProcessBatteryImages(id, request.MainImages);
                }

                // 处理服务列表 - 先删除现有服务，再添加新服务
                if (request.Services?.Any() == true)
                {
                    Console.WriteLine($"🛠️ 更新 {request.Services.Count} 个服务");
                    // 删除现有服务
                    await _db.Deleteable<BatteryServiceEntity>()
                        .Where(s => s.BatteryId == id)
                        .ExecuteCommandAsync();
                    // 添加新服务
                    await ProcessBatteryServices(id, request.Services);
                }

                // 处理安装费用列表 - 先删除现有费用，再添加新费用
                if (request.InstallationFees?.Any() == true)
                {
                    Console.WriteLine($"💰 更新 {request.InstallationFees.Count} 个安装费用");
                    // 删除现有安装费用
                    await _db.Deleteable<BatteryInstallationFee>()
                        .Where(f => f.BatteryId == id)
                        .ExecuteCommandAsync();
                    // 添加新安装费用
                    await ProcessBatteryInstallationFees(id, request.InstallationFees);
                }

                // 重新获取完整的电池信息并返回
                return await GetBatteryByIdAsync(id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 更新电池失败: {ex.Message}");
                throw new Exception($"更新电池失败: {ex.Message}", ex);
            }
        }
        public Task<bool> DeleteBatteryAsync(int id) => throw new NotImplementedException();
        public Task<Dictionary<string, int>> GetBatteryStatusSummaryAsync() => throw new NotImplementedException();
        public Task<double> GetAverageBatteryHealthAsync() => throw new NotImplementedException();
        public Task<List<BatterySpecDto>> GetBatterySpecsAsync() => throw new NotImplementedException();
        public async Task<List<BatteryServiceDto>> GetBatteryServicesAsync(int id)
        {
            // 检查电池是否存在
            var batteryExists = await _db.Queryable<Battery>()
                .AnyAsync(b => b.Id == id);

            if (!batteryExists)
            {
                throw new KeyNotFoundException($"电池 ID {id} 不存在");
            }

            // 获取电池服务
            var services = await _db.Queryable<BatteryServiceEntity>()
                .Where(svc => svc.BatteryId == id)
                .OrderBy(svc => svc.SortOrder)
                .ToListAsync();

            // 如果没有服务，返回空列表
            if (services == null || services.Count == 0)
            {
                return new List<BatteryServiceDto>();
            }

            // 转换为DTO
            return services.Select(service => new BatteryServiceDto
            {
                Id = service.Id,
                BatteryId = service.BatteryId,
                Name = service.Name,
                Description = service.Description,
                SortOrder = service.SortOrder,
                CreatedAt = service.CreatedAt,
                UpdatedAt = service.UpdatedAt
            }).ToList();
        }
        public Task<BatteryServiceDto> CreateBatteryServiceAsync(int batteryId, CreateBatteryServiceRequest request) => throw new NotImplementedException();
        public Task<BatteryServiceDto> UpdateBatteryServiceAsync(int id, UpdateBatteryServiceRequest request) => throw new NotImplementedException();
        public Task DeleteBatteryServiceAsync(int id) => throw new NotImplementedException();
        public async Task<List<BatteryInstallationFeeDto>> GetBatteryInstallationFeesAsync(int id)
        {
            // 检查电池是否存在
            var batteryExists = await _db.Queryable<Battery>()
                .AnyAsync(b => b.Id == id);

            if (!batteryExists)
            {
                throw new KeyNotFoundException($"电池 ID {id} 不存在");
            }

            // 获取电池安装费用
            var fees = await _db.Queryable<BatteryInstallationFee>()
                .Where(fee => fee.BatteryId == id)
                .ToListAsync();

            // 如果没有安装费用，返回空列表
            if (fees == null || fees.Count == 0)
            {
                return new List<BatteryInstallationFeeDto>();
            }

            // 转换为DTO
            return fees.Select(fee => new BatteryInstallationFeeDto
            {
                Id = fee.Id,
                BatteryId = fee.BatteryId,
                Name = fee.Name,
                Price = fee.Price,
                Description = fee.Description,
                CreatedAt = fee.CreatedAt,
                UpdatedAt = fee.UpdatedAt
            }).ToList();
        }
        public Task<BatteryInstallationFeeDto> CreateBatteryInstallationFeeAsync(int batteryId, CreateBatteryInstallationFeeRequest request) => throw new NotImplementedException();
        public Task<BatteryInstallationFeeDto> UpdateBatteryInstallationFeeAsync(int id, UpdateBatteryInstallationFeeRequest request) => throw new NotImplementedException();
        public Task DeleteBatteryInstallationFeeAsync(int id) => throw new NotImplementedException();
        public Task<List<BatteryCategoryDto>> GetBatteryCategoriesAsync() => throw new NotImplementedException();

        public async Task<bool> CheckDuplicateAsync(int categoryId, string spec)
        {
            if (categoryId <= 0 || string.IsNullOrWhiteSpace(spec))
            {
                throw new ArgumentException("类别ID和规格名称不能为空");
            }

            return await _db.Queryable<Battery>()
                .Where(b => b.CategoryId == categoryId && b.Spec == spec)
                .AnyAsync();
        }

        /// <summary>
        /// 将状态整数转换为状态字符串
        /// </summary>
        private string GetStatusString(int status)
        {
            return status switch
            {
                1 => "Available",
                2 => "Rented",
                3 => "Sold",
                4 => "Maintenance",
                _ => "Available"
            };
        }

        /// <summary>
        /// 解析字符串为decimal，失败时返回0
        /// </summary>
        private decimal ParseDecimal(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return 0;

            if (decimal.TryParse(value, out var result))
                return result;

            return 0;
        }

        /// <summary>
        /// 创建电池（支持 IFormFile 图片上传）
        /// </summary>
        public async Task<BatteryDto> CreateBatteryWithFilesAsync(CreateBatteryWithFilesRequest request)
        {
            Console.WriteLine($"📝 开始创建电池（支持文件上传）: {request.Spec}");

            // 创建电池实体
            var battery = new Battery
            {
                Spec = request.Spec,
                Status = request.Status,
                Price = request.Price,
                RentPrice = ParseDecimal(request.RentPrice),
                ManufactureDate = request.ManufactureDate,
                Lifespan = request.Lifespan,
                Description = request.Description ?? string.Empty,
                CategoryId = request.CategoryId,
                Voltage = ParseDecimal(request.Voltage),
                Capacity = ParseDecimal(request.Capacity),
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 保存电池到数据库
            var batteryId = await _db.Insertable(battery).ExecuteReturnIdentityAsync();
            Console.WriteLine($"✅ 电池创建成功，ID: {batteryId}");

            // 处理图片文件
            if (request.ImageFiles != null && request.ImageFiles.Any())
            {
                await ProcessBatteryImagesFromFiles(batteryId, request.ImageFiles, request.ImageType);
            }

            // 返回创建的电池信息
            return await GetBatteryByIdAsync(batteryId);
        }

        /// <summary>
        /// 更新电池（支持 IFormFile 图片上传）
        /// </summary>
        public async Task<BatteryDto> UpdateBatteryWithFilesAsync(int id, UpdateBatteryWithFilesRequest request)
        {
            Console.WriteLine($"📝 开始更新电池（支持文件上传）: ID={id}, Spec={request.Spec}");

            // 检查电池是否存在
            var existingBattery = await _db.Queryable<Battery>().FirstAsync(b => b.Id == id);
            if (existingBattery == null)
            {
                throw new KeyNotFoundException($"未找到ID为 {id} 的电池");
            }

            // 更新电池基本信息
            existingBattery.Spec = request.Spec;
            existingBattery.Status = GetStatusString(request.Status);
            existingBattery.Price = request.Price;
            existingBattery.RentPrice = ParseDecimal(request.RentPrice);
            existingBattery.ManufactureDate = request.ManufactureDate ?? DateTime.Now;
            existingBattery.Lifespan = request.Lifespan;
            existingBattery.Description = request.Description ?? string.Empty;
            existingBattery.CategoryId = request.CategoryId;
            existingBattery.Voltage = ParseDecimal(request.Voltage);
            existingBattery.Capacity = ParseDecimal(request.Capacity);
            existingBattery.UpdatedAt = DateTime.Now;

            // 更新电池信息
            await _db.Updateable(existingBattery).ExecuteCommandAsync();

            // 处理要删除的图片
            if (request.RemoveImageIds != null && request.RemoveImageIds.Any())
            {
                foreach (var imageId in request.RemoveImageIds)
                {
                    await DeleteBatteryImageAsync(imageId);
                }
            }

            // 处理新增图片文件
            if (request.ImageFiles != null && request.ImageFiles.Any())
            {
                await ProcessBatteryImagesFromFiles(id, request.ImageFiles, request.ImageType);
            }

            Console.WriteLine($"✅ 电池更新成功: ID={id}");
            return await GetBatteryByIdAsync(id);
        }

        /// <summary>
        /// 删除电池图片
        /// </summary>
        private async Task DeleteBatteryImageAsync(int imageId)
        {
            try
            {
                var image = await _db.Queryable<BatteryImage>().FirstAsync(i => i.Id == imageId);
                if (image != null)
                {
                    // 删除物理文件
                    if (!string.IsNullOrEmpty(image.RelativePath))
                    {
                        var physicalPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", image.RelativePath.TrimStart('/'));
                        if (File.Exists(physicalPath))
                        {
                            File.Delete(physicalPath);
                            Console.WriteLine($"🗑️ 删除图片文件: {physicalPath}");
                        }
                    }

                    // 删除数据库记录
                    await _db.Deleteable<BatteryImage>().Where(i => i.Id == imageId).ExecuteCommandAsync();
                    Console.WriteLine($"✅ 删除图片记录: ID={imageId}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 删除图片失败: ID={imageId}, 错误: {ex.Message}");
            }
        }
    }
}