/**
 * 订单API服务
 */
import request from '@/utils/request';

// API接口
const OrderAPI = {
  /**
   * 获取订单列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getOrderList(params = {}) {
    // 将前端参数转换为后端需要的格式
    const apiParams = {};

    // 必需参数 - 确保这些参数一定有值
    apiParams.Page = params.Page || 1;
    apiParams.PageSize = params.PageSize || 10;
    apiParams.OrderNo = params.OrderNo || ''; // OrderNo是必需的，即使是空字符串

    // 可选参数 - 只有当值存在时才添加
    if (params.Status !== undefined && params.Status !== null && params.Status !== '') {
      apiParams.Status = params.Status;
    }

    if (params.StartDate) {
      apiParams.StartDate = params.StartDate;
    }

    if (params.EndDate) {
      apiParams.EndDate = params.EndDate;
    }

    if (params.Type !== undefined && params.Type !== null && params.Type !== '') {
      apiParams.Type = params.Type;
    }

    console.log('发送订单列表请求参数:', apiParams);

    // 使用简单的URL和参数对象
    const url = '/api/order';

    console.log('发送请求到:', url, '参数:', apiParams);

    // 使用第二个参数传递查询参数
    return request.get(url, apiParams).then(response => {
      console.log('订单列表响应数据:', response);

      // 处理响应数据
      if (response && response.items) {
        // 将后端返回的数据转换为前端需要的格式
        return {
          code: 0,
          message: 'success',
          data: {
            items: response.items,
            totalItems: response.totalItems,
            page: response.page,
            pageSize: response.pageSize,
            totalPages: response.totalPages,
            hasPreviousPage: response.hasPreviousPage,
            hasNextPage: response.hasNextPage
          },
          total: response.totalItems,
          page: response.page,
          pageSize: response.pageSize,
          totalPages: response.totalPages,
          hasMore: response.hasNextPage
        };
      }
      return {
        code: 0,
        message: 'success',
        data: response || [],
        hasMore: false
      };
    });
  },

  /**
   * 获取订单详情
   * @param {Number} id 订单ID
   * @returns {Promise} Promise对象
   */
  getOrderDetail(id) {
    return request.get(`/api/order/${id}`).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 根据订单号获取订单
   * @param {String} orderNo 订单号
   * @returns {Promise} Promise对象
   */
  getOrderByOrderNo(orderNo) {
    return request.get(`/api/order/byOrderNo/${orderNo}`).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 获取用户订单
   * @param {Number} userId 用户ID
   * @returns {Promise} Promise对象
   */
  getUserOrders(userId) {
    return request.get(`/api/order/user/${userId}`).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 获取订单数量统计
   * @returns {Promise} Promise对象
   */
  getOrderCounts() {
    return request.get('/api/order/counts').then(response => {
      console.log('订单数量统计响应数据:', response);
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 创建订单
   * @param {Object} data 订单数据
   * @returns {Promise} Promise对象
   */
  createOrder(data) {
    return request.post('/api/order', data).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 支付订单
   * @param {Object} data 支付数据
   * @returns {Promise} Promise对象
   */
  payOrder(data) {
    return request.post('/api/order/pay', data).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 取消订单
   * @param {Object} data 取消数据
   * @returns {Promise} Promise对象
   */
  cancelOrder(data) {
    return request.post('/api/order/cancel', data).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 完成订单（管理员）
   * @param {Object} data 完成数据
   * @returns {Promise} Promise对象
   */
  completeOrder(data) {
    return request.post('/api/order/complete', data).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  },

  /**
   * 删除订单
   * @param {Number} id 订单ID
   * @returns {Promise} Promise对象
   */
  deleteOrder(id) {
    return request.delete(`/api/order/${id}`).then(response => {
      return {
        code: 0,
        message: 'success',
        data: response
      };
    });
  }
};

export default OrderAPI;
