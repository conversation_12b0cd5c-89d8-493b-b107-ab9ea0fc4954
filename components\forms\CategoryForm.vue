<!--
  分类表单组件
  精简版本，移除冗余代码

  <AUTHOR> System Team
  @version 2.0.0
  @since 2024-01-01
-->

<template>
  <u-popup
    v-model="visible"
    mode="center"
    border-radius="12"
    width="90%"
    @close="handleClose"
  >
    <view class="category-form">
      <!-- 表单标题 -->
      <view class="form-header">
        <text class="form-title">{{ isEditing ? '编辑分类' : '添加分类' }}</text>
        <u-icon name="close" @click="handleClose" />
      </view>

      <!-- 表单内容 -->
      <view class="form-body">
        <u-form
          :model="formData"
          :rules="formRules"
          ref="categoryForm"
          label-position="top"
        >
          <!-- 分类代码 -->
          <u-form-item
            label="分类代码"
            prop="code"
            v-if="!isEditing"
          >
            <u-input
              v-model="formData.code"
              placeholder="请输入分类代码（大写字母、数字、下划线）"
              maxlength="20"
            />
            <text class="form-tip">分类代码创建后不可修改</text>
          </u-form-item>

          <!-- 分类名称 -->
          <u-form-item
            label="分类名称"
            prop="name"
          >
            <u-input
              v-model="formData.name"
              placeholder="请输入分类名称"
              maxlength="50"
            />
          </u-form-item>

          <!-- 分类描述 -->
          <u-form-item label="分类描述">
            <u-textarea
              v-model="formData.description"
              placeholder="请输入分类描述（可选）"
              maxlength="500"
              height="120"
            />
          </u-form-item>

          <!-- 显示顺序 -->
          <u-form-item label="显示顺序">
            <u-input
              v-model="formData.displayOrder"
              placeholder="请输入显示顺序"
              type="number"
            />
            <text class="form-tip">数字越小排序越靠前</text>
          </u-form-item>

          <!-- 是否启用 -->
          <u-form-item label="状态">
            <u-switch
              v-model="formData.isActive"
              active-text="启用"
              inactive-text="禁用"
            />
          </u-form-item>
        </u-form>
      </view>

      <!-- 表单按钮 -->
      <view class="form-footer">
        <u-button @click="handleClose">取消</u-button>
        <u-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          {{ isEditing ? '更新' : '创建' }}
        </u-button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { BatteryCategoryValidator } from '@/utils/validators/BatteryValidators'

/**
 * 分类表单组件
 */
export default {
  name: 'CategoryForm',

  props: {
    // 显示状态
    value: {
      type: Boolean,
      default: false
    },

    // 分类数据
    category: {
      type: Object,
      default: null
    },

    // 是否编辑模式
    isEditing: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      saving: false,

      // 表单数据
      formData: {
        code: '',
        name: '',
        description: '',
        displayOrder: 0,
        isActive: true
      },

      // 表单验证规则
      formRules: {
        code: [
          {
            required: true,
            message: '请输入分类代码',
            trigger: 'blur'
          },
          {
            pattern: /^[A-Z0-9_]+$/,
            message: '分类代码只能包含大写字母、数字和下划线',
            trigger: 'blur'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur'
          },
          {
            min: 1,
            max: 50,
            message: '分类名称长度在1到50个字符之间',
            trigger: 'blur'
          }
        ]
      }
    }
  },

  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },

  watch: {
    // 监听分类数据变化
    category: {
      handler(newCategory) {
        if (newCategory) {
          this.formData = {
            code: newCategory.code || '',
            name: newCategory.name || '',
            description: newCategory.description || '',
            displayOrder: newCategory.displayOrder || 0,
            isActive: newCategory.isActive !== false
          }
        } else {
          this.resetForm()
        }
      },
      immediate: true
    },

    // 监听显示状态
    visible(newVal) {
      if (!newVal) {
        this.resetForm()
      }
    }
  },

  methods: {
    /**
     * 处理保存
     */
    async handleSave() {
      try {
        // 表单验证
        const valid = await this.validateForm()
        if (!valid) return

        // 业务验证
        const validationResult = BatteryCategoryValidator.validate(this.formData, this.isEditing)
        if (!validationResult.isValid) {
          uni.showToast({
            title: validationResult.getFirstError(),
            icon: 'none'
          })
          return
        }

        this.saving = true

        // 触发保存事件
        this.$emit('save', { ...this.formData })

      } catch (error) {
        console.error('保存分类失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },

    /**
     * 处理关闭
     */
    handleClose() {
      this.$emit('cancel')
      this.visible = false
    },

    /**
     * 表单验证
     */
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.categoryForm.validate((valid) => {
          resolve(valid)
        })
      })
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        code: '',
        name: '',
        description: '',
        displayOrder: 0,
        isActive: true
      }

      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.categoryForm) {
          this.$refs.categoryForm.clearValidate()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.category-form {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .form-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .form-body {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-tip {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
      display: block;
    }
  }

  .form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 20rpx;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    background: #f8f9fa;
  }
}
</style>
