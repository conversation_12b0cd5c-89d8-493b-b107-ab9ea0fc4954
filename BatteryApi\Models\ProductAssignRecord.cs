using SqlSugar;
using System;

namespace BatteryApi.Models
{
    /// <summary>
    /// 商品分配记录
    /// </summary>
    [SugarTable("ProductAssignRecords")]
    public class ProductAssignRecord
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int StoreId { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(StoreId))]
        public Store Store { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int ProductId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ProductId))]
        public Product Product { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Quantity { get; set; }

        /// <summary>
        /// 分配人
        /// </summary>
        [SugarColumn(IsNullable = false, Length = 50)]
        public string AssignedBy { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime AssignTime { get; set; } = DateTime.Now;
    }
}
