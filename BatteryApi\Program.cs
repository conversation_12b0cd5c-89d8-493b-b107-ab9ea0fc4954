using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Microsoft.Extensions.FileProviders;
using SqlSugar;
using System.Text;
using BatteryApi.Services.Interfaces;
using BatteryApi.Services.Implementations;
using BatteryApi.Services;
using BatteryApi.Services.Base;
using BatteryApi.Models;
using BatteryApi.Middleware;
using BatteryApi.Utils; 

var builder = WebApplication.CreateBuilder(args);

// Configure Kestrel to listen on all network interfaces
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.ListenAnyIP(5247); // Listen on port 5247 on all network interfaces
});

// Add services to the container.
builder.Services.AddControllers(options =>
{
    // 注册自定义模型绑定器
    options.ModelBinderProviders.Insert(0, new BatteryApi.ModelBinders.IntListModelBinderProvider());
});
builder.Services.AddHttpContextAccessor();

// 添加会话支持
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(1); // 会话超时时间
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Configure Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Battery API",
        Version = "v1",
        Description = "Battery Management System API with User Verification"
    });

    // Disable conflict resolution for now
    c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
});

// Register Services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IBatteryCategoryService, BatteryCategoryService>();
builder.Services.AddScoped<IStoreService, StoreService>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<ISmsService, AliSmsService>();
builder.Services.AddScoped<IVerificationService, VerificationService>();
builder.Services.AddScoped<ISystemSettingService, SystemSettingService>();
builder.Services.AddScoped<IOcrService, OcrService>();
// 新增商品管理服务
builder.Services.AddScoped<IProductService, BatteryApi.Services.Implementations.ProductService>();
// 移除旧的ProductImageService注册，因为已经使用了BatteryImageService

builder.Services.AddScoped<DatabaseInitializationService>();
builder.Services.AddScoped<DatabaseMigrationService>();
builder.Services.AddScoped<DataMigrationService>();

// 缓存服务
builder.Services.AddMemoryCache();
builder.Services.AddScoped<IAppCacheService, MemoryCacheService>();

// 图片处理服务
builder.Services.AddScoped<IImageProcessingService, ImageProcessingService>();

// 性能监控服务
builder.Services.AddSingleton<IPerformanceMonitorService, PerformanceMonitorService>();

// 注册腾讯地图服务
builder.Services.AddHttpClient<ITencentMapService, TencentMapService>();

// 注册BMS系统助手服务
builder.Services.AddHttpClient<BMSSystemHelper>();
builder.Services.AddScoped<BMSSystemHelper>();

// Configure JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

// Configure SqlSugar
builder.Services.AddScoped<ISqlSugarClient>(sp =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    var client = new SqlSugarClient(new ConnectionConfig
    {
        ConnectionString = connectionString,
        DbType = DbType.SqlServer,
        IsAutoCloseConnection = true,
        InitKeyType = InitKeyType.Attribute
    });

    client.Aop.OnLogExecuting = (sql, parameters) =>
    {
        if (builder.Environment.IsDevelopment())
        {
            Console.WriteLine(sql);
        }
    };

    // Add global filter for soft delete
    client.QueryFilter.AddTableFilter<User>(it => !it.IsDeleted);

    return client;
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

var app = builder.Build();

// Ensure uploads directories exist
var basePath = builder.Configuration["FileStorage:BasePath"] ?? "uploads";
var uploadsRootPath = Path.Combine(app.Environment.ContentRootPath, basePath);
Directory.CreateDirectory(uploadsRootPath);

// 从配置文件读取路径
var verificationsPathConfig = builder.Configuration["FileStorage:VerificationsPath"] ?? "uploads/verifications";
var verificationPathConfig = builder.Configuration["FileStorage:VerificationPath"] ?? "uploads/verification";
var idcardPathConfig = builder.Configuration["FileStorage:IdCardPath"] ?? "uploads/idcard";
var storePathConfig = builder.Configuration["FileStorage:StorePath"] ?? "uploads/stores";
var productPathConfig = builder.Configuration["FileStorage:ProductPath"] ?? "uploads/products";
var batteryPathConfig = builder.Configuration["FileStorage:BatteryPath"] ?? "uploads/batteries";
var tempPathConfig = builder.Configuration["FileStorage:TempPath"] ?? "uploads/temp";

// 创建目录（使用相对于ContentRootPath的绝对路径）
var verificationsPath = Path.Combine(app.Environment.ContentRootPath, verificationsPathConfig);
Directory.CreateDirectory(verificationsPath);

var verificationPath = Path.Combine(app.Environment.ContentRootPath, verificationPathConfig);
Directory.CreateDirectory(verificationPath);

var idcardPath = Path.Combine(app.Environment.ContentRootPath, idcardPathConfig);
Directory.CreateDirectory(idcardPath);

var storePath = Path.Combine(app.Environment.ContentRootPath, storePathConfig);
Directory.CreateDirectory(storePath);

var productPath = Path.Combine(app.Environment.ContentRootPath, productPathConfig);
Directory.CreateDirectory(productPath);

var batteryPath = Path.Combine(app.Environment.ContentRootPath, batteryPathConfig);
Directory.CreateDirectory(batteryPath);

var tempPath = Path.Combine(app.Environment.ContentRootPath, tempPathConfig);
Directory.CreateDirectory(tempPath);

// 记录目录创建信息
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("上传目录已创建:");
logger.LogInformation("- 根目录: {Path}", uploadsRootPath);
logger.LogInformation("- 认证目录 (verifications): {Path}", verificationsPath);
logger.LogInformation("- 认证目录 (verification): {Path}", verificationPath);
logger.LogInformation("- 身份证目录: {Path}", idcardPath);
logger.LogInformation("- 门店图片目录: {Path}", storePath);
logger.LogInformation("- 产品图片目录: {Path}", productPath);
logger.LogInformation("- 电池图片目录: {Path}", batteryPath);
logger.LogInformation("- 临时文件目录: {Path}", tempPath);

// 基本数据库连接检查和初始化
using (var scope = app.Services.CreateScope())
{
    var dbClient = scope.ServiceProvider.GetRequiredService<ISqlSugarClient>();
    var dbInitService = scope.ServiceProvider.GetRequiredService<DatabaseInitializationService>();

    try
    {
        // 创建数据库（如果不存在）
        dbClient.DbMaintenance.CreateDatabase();
        logger.LogInformation("Database connection checked successfully.");

        // 初始化数据库表
        await dbInitService.InitializeAsync();
        logger.LogInformation("Database tables initialized successfully.");

        // 初始化系统设置
        await InitializeSystemSettings(dbClient, logger);
        logger.LogInformation("System settings initialized successfully.");

        // 执行数据库迁移（使用SqlSugar）
        try
        {
            var migrationService = scope.ServiceProvider.GetRequiredService<DatabaseMigrationService>();

            // 检查数据库连接
            var connectionOk = await migrationService.CheckDatabaseConnectionAsync();
            if (connectionOk)
            {
                logger.LogInformation("数据库连接正常");

                // 获取数据库版本信息
                var dbVersion = await migrationService.GetDatabaseVersionAsync();
                logger.LogInformation("数据库版本: {Version}", dbVersion);

                // 执行所有迁移
                await migrationService.ExecuteAllMigrationsAsync();
                logger.LogInformation("数据库迁移执行完成");
            }
            else
            {
                logger.LogWarning("数据库连接失败，跳过迁移");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行数据库迁移时发生错误");
        }

        // 仍然保留手动初始化的选项
        logger.LogInformation("Note: Additional data initialization is available via POST /api/database/initialize");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Database initialization error");
        throw;
    }
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Battery API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

// Use exception handling middleware (should be first in the pipeline)
app.UseMiddleware<ExceptionHandlingMiddleware>();

// Use health check middleware
app.UseMiddleware<HealthCheckMiddleware>();

// Use performance monitoring middleware
app.UsePerformanceMonitoring();

// 启用会话
app.UseSession();

// Configure static file serving for all uploads
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadsRootPath),
    RequestPath = "/uploads"
});

// 记录静态文件服务配置
logger.LogInformation("静态文件服务已配置:");
logger.LogInformation("- 主目录: {Path} => {RequestPath}", uploadsRootPath, "/uploads");

// Configure static file serving for verification images (legacy)
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(verificationsPath),
    RequestPath = "/verifications"
});
logger.LogInformation("- 认证目录: {Path} => {RequestPath}", verificationsPath, "/verifications");

// Configure static file serving for ID card images (legacy)
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(idcardPath),
    RequestPath = "/uploads/idcard"
});
logger.LogInformation("- 身份证目录: {Path} => {RequestPath}", idcardPath, "/uploads/idcard");

// Configure static file serving for store images
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(storePath),
    RequestPath = "/uploads/stores"
});
logger.LogInformation("- 门店图片目录: {Path} => {RequestPath}", storePath, "/uploads/stores");

// Configure static file serving for product images
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(productPath),
    RequestPath = "/uploads/products"
});
logger.LogInformation("- 产品图片目录: {Path} => {RequestPath}", productPath, "/uploads/products");

// Configure static file serving for battery images
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(batteryPath),
    RequestPath = "/uploads/batteries"
});
logger.LogInformation("- 电池图片目录: {Path} => {RequestPath}", batteryPath, "/uploads/batteries");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

 
// 添加应用程序关闭处理
var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();
lifetime.ApplicationStopping.Register(() =>
{
    try
    {
        Console.WriteLine("应用程序正在关闭...");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"关闭处理异常: {ex.Message}");
    }
});

lifetime.ApplicationStopped.Register(() =>
{
    try
    {
        Console.WriteLine("应用程序已关闭");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"关闭后处理异常: {ex.Message}");
    }
});

Console.WriteLine($"Application started. Swagger UI available at: {app.Urls.FirstOrDefault()}/swagger");

try
{
    app.Run();
}
catch (Exception ex)
{
    if (ex.InnerException != null && ex.InnerException.Message.Contains("address already in use"))
    {
        logger.LogError("端口 5242 已被占用，尝试使用备用端口...");

        // 尝试使用备用端口
        var urls = app.Urls.ToList();
        var newUrls = new List<string>();

        foreach (var url in urls)
        {
            if (url.Contains(":5242"))
            {
                // 使用备用端口 5243
                var newUrl = url.Replace(":5242", ":5243");
                newUrls.Add(newUrl);
                logger.LogInformation($"使用备用端口: {newUrl}");
            }
            else
            {
                newUrls.Add(url);
            }
        }

        // 清除原有URLs并添加新URLs
        app.Urls.Clear();
        foreach (var url in newUrls)
        {
            app.Urls.Add(url);
        }

        Console.WriteLine($"Application started with fallback port. Swagger UI available at: {app.Urls.FirstOrDefault()}/swagger");
        app.Run();
    }
    else
    {
        // 安全的日志记录，避免在应用程序关闭时出现异常
        try
        {
            logger?.LogError(ex, "应用程序启动失败");
        }
        catch (ObjectDisposedException)
        {
            // 忽略日志记录器已释放的异常
            Console.WriteLine($"应用程序启动失败: {ex.Message}");
        }
        catch (Exception logEx)
        {
            // 记录日志异常到控制台
            Console.WriteLine($"日志记录失败: {logEx.Message}");
            Console.WriteLine($"原始异常: {ex.Message}");
        }
        throw;
    }
}

// 初始化系统设置
async Task InitializeSystemSettings(ISqlSugarClient dbClient, ILogger logger)
{
    try
    {
        // 检查是否已经存在系统设置
        var settingsCount = await dbClient.Queryable<SystemSetting>().CountAsync();

        if (settingsCount > 0)
        {
            logger.LogInformation("System settings already exist. Skipping initialization.");
            return;
        }

        logger.LogInformation("Initializing default system settings...");

        // 准备默认系统设置
        var defaultSettings = new List<SystemSetting>
        {
            // 系统信息
            new SystemSetting { Key = "system.version", Value = "v1.0.0", Group = "System", Description = "系统版本", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "system.lastUpdate", Value = DateTime.Now.ToString("yyyy-MM-dd"), Group = "System", Description = "最后更新时间", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "system.status", Value = "正常", Group = "System", Description = "系统状态", UpdatedAt = DateTime.Now },

            // 用户管理设置
            new SystemSetting { Key = "user.allowRegister", Value = "true", Group = "UserManagement", Description = "允许用户注册", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "user.requireApproval", Value = "false", Group = "UserManagement", Description = "注册需要审核", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "user.forceRealAuth", Value = "false", Group = "UserManagement", Description = "强制实名认证", UpdatedAt = DateTime.Now },

            // 支付设置
            new SystemSetting { Key = "payment.enableAlipay", Value = "false", Group = "Payment", Description = "启用支付宝支付", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "payment.alipayMerchantId", Value = "", Group = "Payment", Description = "支付宝商户ID", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "payment.enableWechat", Value = "false", Group = "Payment", Description = "启用微信支付", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "payment.wechatMerchantId", Value = "", Group = "Payment", Description = "微信商户ID", UpdatedAt = DateTime.Now },

            // 租赁设置
            new SystemSetting { Key = "rental.timeout", Value = "24", Group = "Rental", Description = "租赁超时时间（小时）", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "rental.deposit", Value = "200", Group = "Rental", Description = "租赁押金（元）", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "rental.overtimeFee", Value = "10", Group = "Rental", Description = "超时费用（元/小时）", UpdatedAt = DateTime.Now },

            // 系统维护设置
            new SystemSetting { Key = "system.maintenanceMode", Value = "false", Group = "System", Description = "系统维护模式", UpdatedAt = DateTime.Now },
            new SystemSetting { Key = "system.maintenanceMessage", Value = "系统维护中，请稍后再试...", Group = "System", Description = "维护提示信息", UpdatedAt = DateTime.Now }
        };

        // 批量插入默认设置
        await dbClient.Insertable(defaultSettings).ExecuteCommandAsync();

        logger.LogInformation($"Successfully initialized {defaultSettings.Count} system settings.");
    }
    catch (Exception ex)
    {
        // 安全的日志记录
        try
        {
            logger?.LogError(ex, "Error initializing system settings");
        }
        catch (ObjectDisposedException)
        {
            Console.WriteLine($"Error initializing system settings: {ex.Message}");
        }
        catch (Exception logEx)
        {
            Console.WriteLine($"日志记录失败: {logEx.Message}");
            Console.WriteLine($"原始异常: {ex.Message}");
        }
        throw;
    }
}
