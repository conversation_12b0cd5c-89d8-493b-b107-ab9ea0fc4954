using BatteryApi.DTOs.Battery;
using BatteryApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;

namespace BatteryApi.Controllers;

/// <summary>
/// 电池规格管理控制器
/// </summary>
[ApiController]
[Route("api/battery-specs")]
[Authorize]
public class BatterySpecController : ControllerBase
{
    private readonly ISqlSugarClient _db;
    private readonly ILogger<BatterySpecController> _logger;

    public BatterySpecController(ISqlSugarClient db, ILogger<BatterySpecController> logger)
    {
        _db = db;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有电池规格
    /// </summary>
    /// <returns>电池规格列表</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<ActionResult<List<BatterySpecDto>>> GetBatterySpecs()
    {
        try
        {
            _logger.LogInformation("开始获取电池规格列表");

            var specs = await _db.Queryable<BatterySpec>()
                .OrderBy(s => s.CategoryId)
                .OrderBy(s => s.Name)
                .ToListAsync();

            var specDtos = specs.Select(MapToDto).ToList();

            _logger.LogInformation("成功获取电池规格列表，数量: {Count}", specDtos.Count);
            return Ok(specDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取电池规格列表失败");
            return StatusCode(500, new { message = "获取电池规格列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 根据ID获取电池规格详情
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <returns>电池规格详情</returns>
    [HttpGet("{id:int}")]
    public async Task<ActionResult<BatterySpecDto>> GetBatterySpecById(int id)
    {
        try
        {
            _logger.LogInformation("开始获取电池规格详情，ID: {Id}", id);

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的电池规格" });
            }

            var specDto = MapToDto(spec);
            _logger.LogInformation("成功获取电池规格详情: {Name}", spec.Name);
            return Ok(specDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取电池规格详情失败，ID: {Id}", id);
            return StatusCode(500, new { message = "获取电池规格详情失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 创建新的电池规格
    /// </summary>
    /// <param name="request">创建电池规格请求</param>
    /// <returns>创建的电池规格</returns>
    [HttpPost]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BatterySpecDto>> CreateBatterySpec(CreateBatterySpecRequest request)
    {
        try
        {
            _logger.LogInformation("开始创建电池规格: {Name}", request.Name);

            // 检查规格名称是否已存在
            var existingSpec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Name == request.Name && s.CategoryId == request.CategoryId);

            if (existingSpec != null)
            {
                return BadRequest(new { message = $"分类中已存在名称为 '{request.Name}' 的规格" });
            }

            // 创建新规格
            var spec = new BatterySpec
            {
                Name = request.Name,
                Voltage = request.Voltage,
                Capacity = request.Capacity,
                Weight = request.Weight,
                Dimensions = request.Dimensions,
                Price = request.Price,
                Description = request.Description,
                ImageUrl = request.ImageUrl,
                CategoryId = request.CategoryId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            await _db.Insertable(spec).ExecuteCommandAsync();

            var specDto = MapToDto(spec);
            _logger.LogInformation("成功创建电池规格: {Name}, ID: {Id}", spec.Name, spec.Id);

            return CreatedAtAction(nameof(GetBatterySpecById), new { id = spec.Id }, specDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建电池规格失败: {Name}", request.Name);
            return StatusCode(500, new { message = "创建电池规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 更新电池规格
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <param name="request">更新电池规格请求</param>
    /// <returns>更新后的电池规格</returns>
    [HttpPut("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<BatterySpecDto>> UpdateBatterySpec(int id, UpdateBatterySpecRequest request)
    {
        try
        {
            _logger.LogInformation("开始更新电池规格，ID: {Id}", id);

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的电池规格" });
            }

            // 检查规格名称是否与其他规格冲突
            var existingSpec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Name == request.Name && s.Id != id);

            if (existingSpec != null)
            {
                return BadRequest(new { message = $"已存在名称为 '{request.Name}' 的规格" });
            }

            // 更新规格信息
            spec.Name = request.Name;
            spec.Voltage = request.Voltage;
            spec.Capacity = request.Capacity;
            spec.Weight = request.Weight;
            spec.Dimensions = request.Dimensions;
            spec.Price = request.Price;
            spec.Description = request.Description;
            spec.ImageUrl = request.ImageUrl;
            spec.UpdatedAt = DateTime.Now;

            await _db.Updateable(spec).ExecuteCommandAsync();

            var specDto = MapToDto(spec);
            _logger.LogInformation("成功更新电池规格: {Name}", spec.Name);

            return Ok(specDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新电池规格失败，ID: {Id}", id);
            return StatusCode(500, new { message = "更新电池规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 删除电池规格
    /// </summary>
    /// <param name="id">规格ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteBatterySpec(int id)
    {
        try
        {
            _logger.LogInformation("开始删除电池规格，ID: {Id}", id);

            var spec = await _db.Queryable<BatterySpec>()
                .FirstAsync(s => s.Id == id);

            if (spec == null)
            {
                return NotFound(new { message = $"未找到ID为 {id} 的电池规格" });
            }

            // 检查是否有商品使用此规格
            var productCount = await _db.Queryable<Product>()
                .CountAsync(p => p.Model == spec.Name);

            if (productCount > 0)
            {
                return BadRequest(new { message = $"无法删除规格 '{spec.Name}'，因为有 {productCount} 个商品正在使用此规格" });
            }

            await _db.Deleteable<BatterySpec>().Where(s => s.Id == id).ExecuteCommandAsync();

            _logger.LogInformation("成功删除电池规格: {Name}", spec.Name);
            return Ok(new { message = "电池规格删除成功", id = id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除电池规格失败，ID: {Id}", id);
            return StatusCode(500, new { message = "删除电池规格失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 根据分类ID获取电池规格列表
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>电池规格列表</returns>
    [HttpGet("category/{categoryId:int}")]
    public async Task<ActionResult<List<BatterySpecDto>>> GetBatterySpecsByCategoryId(int categoryId)
    {
        try
        {
            _logger.LogInformation("开始获取分类电池规格列表，分类ID: {CategoryId}", categoryId);

            var specs = await _db.Queryable<BatterySpec>()
                .Where(s => s.CategoryId == categoryId)
                .OrderBy(s => s.Name)
                .ToListAsync();

            var specDtos = specs.Select(MapToDto).ToList();

            _logger.LogInformation("成功获取分类电池规格列表，分类ID: {CategoryId}, 数量: {Count}", categoryId, specDtos.Count);
            return Ok(specDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取分类电池规格列表失败，分类ID: {CategoryId}", categoryId);
            return StatusCode(500, new { message = "获取分类电池规格列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 将BatterySpec实体映射为DTO
    /// </summary>
    /// <param name="spec">BatterySpec实体</param>
    /// <returns>BatterySpecDto</returns>
    private static BatterySpecDto MapToDto(BatterySpec spec)
    {
        return new BatterySpecDto
        {
            Id = spec.Id,
            Name = spec.Name,
            Voltage = spec.Voltage,
            Capacity = spec.Capacity,
            Weight = spec.Weight,
            Dimensions = spec.Dimensions,
            Price = spec.Price,
            Description = spec.Description,
            ImageUrl = spec.ImageUrl,
            CategoryId = spec.CategoryId,
            CreatedAt = spec.CreatedAt,
            UpdatedAt = spec.UpdatedAt,
            IsActive = spec.IsActive,
            DisplayOrder = spec.DisplayOrder
        };
    }
}
