<template>
  <view v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <view class="modal-container" @click.stop>
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">BMS信息</text>
        <view class="close-btn" @click="closeModal">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在获取配置参数...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{ error }}</text>
        <button class="retry-btn" @click="loadConfigData">重试</button>
      </view>

      <!-- BMS信息内容 -->
      <scroll-view v-else-if="configData" scroll-y class="modal-content">
        <!-- 实时状态 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">📊</text>
            <text class="section-title">实时状态</text>
          </view>

          <view class="status-grid">
            <view class="status-item">
              <text class="status-label">SOC</text>
              <text class="status-value highlight">{{ getSOC() }}%</text>
            </view>
            <view class="status-item">
              <text class="status-label">总电压</text>
              <text class="status-value">{{ getTotalVoltage() }}V</text>
            </view>
            <view class="status-item">
              <text class="status-label">电流</text>
              <text class="status-value">{{ getCurrent() }}A</text>
            </view>
            <view class="status-item">
              <text class="status-label">功率</text>
              <text class="status-value">{{ getPower() }}W</text>
            </view>
            <view class="status-item">
              <text class="status-label">充电状态</text>
              <text class="status-value status" :class="getChargeStatusClass()">{{ getChargeStatus() }}</text>
            </view>
            <view class="status-item">
              <text class="status-label">连接状态</text>
              <text class="status-value status" :class="getConnectStatusClass()">{{ getConnectStatus() }}</text>
            </view>
          </view>
        </view>

        <!-- 电压信息 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">⚡</text>
            <text class="section-title">电压信息</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">最高电压</text>
              <text class="param-value">{{ getMaxVoltage() }}V</text>
            </view>
            <view class="param-row">
              <text class="param-label">最低电压</text>
              <text class="param-value">{{ getMinVoltage() }}V</text>
            </view>
            <view class="param-row">
              <text class="param-label">平均电压</text>
              <text class="param-value">{{ getAverageVoltage() }}V</text>
            </view>
            <view class="param-row">
              <text class="param-label">压差</text>
              <text class="param-value">{{ getVoltageDifference() }}V</text>
            </view>
            <view class="param-row">
              <text class="param-label">单体电压数量</text>
              <text class="param-value">{{ getCellCount() }}节</text>
            </view>
          </view>

          <!-- 单体电压详情 -->
          <view v-if="getCellVoltages().length > 0" class="cell-voltages">
            <text class="cell-title">单体电压详情</text>
            <view class="cell-grid">
              <view
                v-for="(voltage, index) in getCellVoltages()"
                :key="index"
                class="cell-item"
                :class="getCellVoltageClass(voltage)"
              >
                <text class="cell-label">{{ index + 1 }}#</text>
                <text class="cell-value">{{ voltage.toFixed(3) }}V</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 温度信息 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">🌡️</text>
            <text class="section-title">温度信息</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">最高温度</text>
              <text class="param-value">{{ getMaxTemp() }}°C</text>
            </view>
            <view class="param-row">
              <text class="param-label">最低温度</text>
              <text class="param-value">{{ getMinTemp() }}°C</text>
            </view>
            <view class="param-row">
              <text class="param-label">温度传感器数量</text>
              <text class="param-value">{{ getTempCount() }}个</text>
            </view>
          </view>

          <!-- 温度传感器详情 -->
          <view v-if="getDetailedTemperatures().length > 0" class="temp-details">
            <text class="temp-title">温度传感器详情</text>
            <view class="temp-list">
              <view
                v-for="(temp, index) in getDetailedTemperatures()"
                :key="index"
                class="temp-item"
              >
                <text class="temp-label">{{ temp.label || `传感器${index + 1}` }}</text>
                <text class="temp-value" :class="getTempClass(temp.value)">{{ temp.value }}°C</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 系统信息 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">💻</text>
            <text class="section-title">系统信息</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">固件版本</text>
              <text class="param-value">{{ getFirmware() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">协议版本</text>
              <text class="param-value">{{ getProtocol() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">容量</text>
              <text class="param-value">{{ getCapacity() }}Ah</text>
            </view>
            <view class="param-row">
              <text class="param-label">循环次数</text>
              <text class="param-value">{{ getCycleCount() }}次</text>
            </view>
            <view class="param-row">
              <text class="param-label">充电MOS</text>
              <text class="param-value status" :class="getChargeMOSClass()">{{ getChargeMOSStatus() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">放电MOS</text>
              <text class="param-value status" :class="getDischargeMOSClass()">{{ getDischargeMOSStatus() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">均衡状态</text>
              <text class="param-value status" :class="getEquilibriumClass()">{{ getEquilibriumStatus() }}</text>
            </view>
          </view>
        </view>

        <!-- 故障和报警 -->
        <view class="config-section" v-if="getFaultStates().length > 0 || getAlarmStates().length > 0">
          <view class="section-header">
            <text class="section-icon">⚠️</text>
            <text class="section-title">故障和报警</text>
          </view>

          <view v-if="getFaultStates().length > 0" class="fault-section">
            <text class="fault-title">故障状态</text>
            <view class="fault-list">
              <view
                v-for="(fault, index) in getFaultStates()"
                :key="index"
                class="fault-item error"
              >
                <text class="fault-icon">❌</text>
                <text class="fault-text">{{ fault }}</text>
              </view>
            </view>
          </view>

          <view v-if="getAlarmStates().length > 0" class="alarm-section">
            <text class="alarm-title">报警状态</text>
            <view class="alarm-list">
              <view
                v-for="(alarm, index) in getAlarmStates()"
                :key="index"
                class="alarm-item warning"
              >
                <text class="alarm-icon">⚠️</text>
                <text class="alarm-text">{{ alarm }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 电芯特征 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">🔋</text>
            <text class="section-title">电芯特征</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">电池类型</text>
              <text class="param-value highlight">{{ getBatteryType() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">电池标称容量</text>
              <text class="param-value">{{ getNominalCapacity() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">电池实际容量</text>
              <text class="param-value">{{ getActualCapacity() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">休眠等待时间</text>
              <text class="param-value">{{ getSleepWaitTime() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">电池编号</text>
              <text class="param-value">{{ getBatteryNumber() }}</text>
            </view>
          </view>
        </view>

        <!-- 保护参数 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">🛡️</text>
            <text class="section-title">保护参数</text>
          </view>

          <view class="param-grid">
            <!-- 电压保护 -->
            <view class="param-group">
              <text class="group-title">电压保护</text>
              <view class="param-item">
                <text class="param-label">单体过压保护</text>
                <text class="param-value">{{ getOverVoltageProtection() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">单体过压保护延时</text>
                <text class="param-value">{{ getOverVoltageDelay() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">单体过压恢复电压</text>
                <text class="param-value">{{ getOverVoltageRecovery() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">单体欠压保护</text>
                <text class="param-value">{{ getUnderVoltageProtection() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">单体欠压保护延时</text>
                <text class="param-value">{{ getUnderVoltageDelay() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">单体欠压恢复电压</text>
                <text class="param-value">{{ getUnderVoltageRecovery() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">总压过压保护</text>
                <text class="param-value">{{ getTotalOverVoltage() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">总压欠压保护</text>
                <text class="param-value">{{ getTotalUnderVoltage() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">压差保护</text>
                <text class="param-value">{{ getVoltageDiffProtection() }}</text>
              </view>
            </view>

            <!-- 电流保护 -->
            <view class="param-group">
              <text class="group-title">电流保护</text>
              <view class="param-item">
                <text class="param-label">充电过流保护</text>
                <text class="param-value">{{ getChargeOverCurrent() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">放电过流保护</text>
                <text class="param-value">{{ getDischargeOverCurrent() }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 温度保护 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">🌡️</text>
            <text class="section-title">温度保护</text>
          </view>

          <view class="param-grid">
            <!-- 充电温度保护 -->
            <view class="param-group">
              <text class="group-title">充电温度保护</text>
              <view class="param-item">
                <text class="param-label">充电高温保护</text>
                <text class="param-value">{{ getChargeHighTemp() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">充电低温保护</text>
                <text class="param-value">{{ getChargeLowTemp() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">充电低温保护恢复值</text>
                <text class="param-value">{{ getChargeLowTempRecovery() }}</text>
              </view>
            </view>

            <!-- 放电温度保护 -->
            <view class="param-group">
              <text class="group-title">放电温度保护</text>
              <view class="param-item">
                <text class="param-label">放电高温保护</text>
                <text class="param-value">{{ getDischargeHighTemp() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">放电低温保护</text>
                <text class="param-value">{{ getDischargeLowTemp() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">放电低温保护恢复值</text>
                <text class="param-value">{{ getDischargeLowTempRecovery() }}</text>
              </view>
            </view>

            <!-- 其他温度保护 -->
            <view class="param-group">
              <text class="group-title">其他温度保护</text>
              <view class="param-item">
                <text class="param-label">温差保护</text>
                <text class="param-value">{{ getTempDiffProtection() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">功率管温度保护</text>
                <text class="param-value">{{ getMosfetTempProtection() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">功率管温度保护恢复值</text>
                <text class="param-value">{{ getMosfetTempRecovery() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">电池箱内温度保护</text>
                <text class="param-value">{{ getBatteryBoxTempProtection() }}</text>
              </view>
              <view class="param-item">
                <text class="param-label">电池箱内温度保护恢复值</text>
                <text class="param-value">{{ getBatteryBoxTempRecovery() }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 均衡控制 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">⚖️</text>
            <text class="section-title">均衡控制</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">均衡开启电压</text>
              <text class="param-value">{{ getBalanceStartVoltage() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">均衡开启压差</text>
              <text class="param-value">{{ getBalanceStartDiff() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">主动均衡控制开关</text>
              <text class="param-value status" :class="getBalanceControlStatus()">
                {{ getBalanceControlText() }}
              </text>
            </view>
          </view>
        </view>

        <!-- 其他设置 -->
        <view class="config-section">
          <view class="section-header">
            <text class="section-icon">⚙️</text>
            <text class="section-title">其他</text>
          </view>

          <view class="param-list">
            <view class="param-row">
              <text class="param-label">是否启动电流校准</text>
              <text class="param-value status" :class="getCurrentCalibrationStatus()">
                {{ getCurrentCalibrationText() }}
              </text>
            </view>
            <view class="param-row">
              <text class="param-label">电流校准</text>
              <text class="param-value">{{ getCurrentCalibration() }}</text>
            </view>
            <view class="param-row">
              <text class="param-label">加热开关</text>
              <text class="param-value status" :class="getHeatingStatus()">
                {{ getHeatingText() }}
              </text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 弹窗底部操作按钮 -->
      <view v-if="configData" class="modal-footer">
        <button class="footer-btn secondary" @click="closeModal">关闭</button>
        <button class="footer-btn primary" @click="viewFullPage">查看详情</button>
      </view>
    </view>
  </view>
</template>

<script>
import BMSAPI from '@/api/bms.js'

export default {
  name: 'BMSConfigModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    macId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      configData: null,
      loading: false,
      error: null
    }
  },

  watch: {
    visible(newVal) {
      if (newVal && this.macId) {
        this.loadConfigData()
      }
    },

    macId(newVal) {
      if (this.visible && newVal) {
        this.loadConfigData()
      }
    }
  },

  methods: {
    // 关闭弹窗
    closeModal() {
      this.$emit('close')
    },

    // 点击遮罩层关闭弹窗
    handleOverlayClick() {
      this.closeModal()
    },

    // 查看完整页面
    viewFullPage() {
      uni.navigateTo({
        url: `/pages/admin/battery/bms-config?macId=${this.macId}`
      })
      this.closeModal()
    },

    // 加载配置数据
    async loadConfigData() {
      if (!this.macId) {
        this.error = '缺少MAC ID参数'
        return
      }

      this.loading = true
      this.error = null

      try {
        console.log('获取BMS配置数据，MAC ID:', this.macId)
        const response = await BMSAPI.getBatteryStatus(this.macId)

        console.log('BMS配置数据响应:', response)

        if (response && response.data && response.data.data) {
          this.configData = response.data.data
        } else {
          throw new Error('获取BMS配置数据失败')
        }
      } catch (error) {
        console.error('获取BMS配置数据失败:', error)
        this.error = error.message || '获取BMS配置数据失败，请检查网络连接'
      } finally {
        this.loading = false
      }
    },

    // ========== 实时状态数据获取方法 ==========

    // 获取SOC
    getSOC() {
      return this.getStateValue('soc', 0)
    },

    // 获取总电压
    getTotalVoltage() {
      const voltageAll = this.getStateValue('voltageAll', '0V')
      return voltageAll.replace('V', '') || '0'
    },

    // 获取电流
    getCurrent() {
      return this.getStateValue('current', 0).toFixed(2)
    },

    // 获取功率
    getPower() {
      return this.getStateValue('power', 0).toFixed(2)
    },

    // 获取充电状态
    getChargeStatus() {
      const chargeState = this.getStateValue('chargeState', 0)
      const statusMap = {
        0: '未充电',
        1: '充电中',
        2: '充电完成',
        3: '充电异常'
      }
      return statusMap[chargeState] || '未知'
    },

    // 获取充电状态样式类
    getChargeStatusClass() {
      const chargeState = this.getStateValue('chargeState', 0)
      return chargeState === 1 ? 'enabled' : 'disabled'
    },

    // 获取连接状态
    getConnectStatus() {
      const connectState = this.getStateValue('connectState', 0)
      return connectState === 1 ? '已连接' : '未连接'
    },

    // 获取连接状态样式类
    getConnectStatusClass() {
      const connectState = this.getStateValue('connectState', 0)
      return connectState === 1 ? 'enabled' : 'disabled'
    },

    // 获取最高电压
    getMaxVoltage() {
      return this.getStateValue('maxVoltage', 0).toFixed(3)
    },

    // 获取最低电压
    getMinVoltage() {
      return this.getStateValue('minVoltage', 0).toFixed(3)
    },

    // 获取平均电压
    getAverageVoltage() {
      return this.getStateValue('averageVoltage', 0).toFixed(3)
    },

    // 获取压差
    getVoltageDifference() {
      return this.getStateValue('voltageDifference', 0).toFixed(3)
    },

    // 获取单体电压数量
    getCellCount() {
      return this.getStateValue('count', 0)
    },

    // 获取单体电压数组
    getCellVoltages() {
      return this.getStateValue('voltage', [])
    },

    // 获取单体电压样式类
    getCellVoltageClass(voltage) {
      const maxVoltage = this.getStateValue('maxVoltage', 0)
      const minVoltage = this.getStateValue('minVoltage', 0)

      if (voltage === maxVoltage) {
        return 'cell-max'
      } else if (voltage === minVoltage) {
        return 'cell-min'
      }
      return 'cell-normal'
    },

    // 获取最高温度
    getMaxTemp() {
      return this.getStateValue('maxTemp', 0)
    },

    // 获取最低温度
    getMinTemp() {
      return this.getStateValue('minTemp', 0)
    },

    // 获取温度传感器数量
    getTempCount() {
      return this.getStateValue('tempCount', 0)
    },

    // 获取详细温度信息
    getDetailedTemperatures() {
      const tempV2 = this.getStateValue('tempV2', [])
      if (tempV2.length > 0) {
        return tempV2.map(t => ({
          label: t.label || t.label_eng || '',
          value: t.value || 0
        }))
      }

      // 如果没有详细温度信息，使用基本温度数据
      const temps = this.getStateValue('temp', [])
      return temps.map((temp, index) => ({
        label: `温度传感器${index + 1}`,
        value: temp
      }))
    },

    // 获取温度样式类
    getTempClass(temp) {
      if (temp > 50) {
        return 'temp-high'
      } else if (temp < 0) {
        return 'temp-low'
      }
      return 'temp-normal'
    },

    // 获取固件版本
    getFirmware() {
      return this.getStateValue('firmware', 'N/A')
    },

    // 获取协议版本
    getProtocol() {
      return this.getStateValue('protocol', 'N/A')
    },

    // 获取容量
    getCapacity() {
      return this.getStateValue('capacity', 0)
    },

    // 获取循环次数
    getCycleCount() {
      return this.getStateValue('cycle', 0)
    },

    // 获取充电MOS状态
    getChargeMOSStatus() {
      const chargingMOS = this.getStateValue('chargingMOS', 0)
      return chargingMOS === 1 ? '开启' : '关闭'
    },

    // 获取充电MOS样式类
    getChargeMOSClass() {
      const chargingMOS = this.getStateValue('chargingMOS', 0)
      return chargingMOS === 1 ? 'enabled' : 'disabled'
    },

    // 获取放电MOS状态
    getDischargeMOSStatus() {
      const dischargeMOS = this.getStateValue('dischargeMOS', 0)
      return dischargeMOS === 1 ? '开启' : '关闭'
    },

    // 获取放电MOS样式类
    getDischargeMOSClass() {
      const dischargeMOS = this.getStateValue('dischargeMOS', 0)
      return dischargeMOS === 1 ? 'enabled' : 'disabled'
    },

    // 获取均衡状态
    getEquilibriumStatus() {
      const equilibrium = this.getStateValue('equilibrium', 0)
      return equilibrium === 1 ? '均衡中' : '未均衡'
    },

    // 获取均衡状态样式类
    getEquilibriumClass() {
      const equilibrium = this.getStateValue('equilibrium', 0)
      return equilibrium === 1 ? 'enabled' : 'disabled'
    },

    // 获取故障状态
    getFaultStates() {
      return this.getStateValue('faultState', [])
    },

    // 获取报警状态
    getAlarmStates() {
      return this.getStateValue('alarmState', [])
    },

    // 通用方法：从状态数据中获取值
    getStateValue(key, defaultValue) {
      if (!this.configData || !this.configData.state) {
        return defaultValue
      }

      const value = this.configData.state[key]
      return value !== undefined && value !== null ? value : defaultValue
    },

    // ========== 配置参数获取方法 ==========

    // 获取电池类型
    getBatteryType() {
      return this.getConfigValue('电池类型', '磷酸铁锂')
    },

    // 获取标称容量
    getNominalCapacity() {
      return this.getConfigValue('电池标称容量', '40AH')
    },

    // 获取实际容量
    getActualCapacity() {
      return this.getConfigValue('电池实际容量', '40AH')
    },

    // 获取休眠等待时间
    getSleepWaitTime() {
      return this.getConfigValue('休眠等待时间', '10s')
    },

    // 获取电池编号
    getBatteryNumber() {
      return this.getConfigValue('电池编号', this.macId || 'InputUserdata60322504290')
    },

    // 获取单体过压保护
    getOverVoltageProtection() {
      return this.getConfigValue('单体过压保护', '3.63V')
    },

    // 获取单体过压保护延时
    getOverVoltageDelay() {
      return this.getConfigValue('单体过压保护延时', '5s')
    },

    // 获取单体过压恢复电压
    getOverVoltageRecovery() {
      return this.getConfigValue('单体过压恢复电压', '3.55V')
    },

    // 获取单体欠压保护
    getUnderVoltageProtection() {
      return this.getConfigValue('单体欠压保护', '2.6V')
    },

    // 获取单体欠压保护延时
    getUnderVoltageDelay() {
      return this.getConfigValue('单体欠压保护延时', '5s')
    },

    // 获取单体欠压恢复电压
    getUnderVoltageRecovery() {
      return this.getConfigValue('单体欠压恢复电压', '2.65V')
    },

    // 获取总压过压保护
    getTotalOverVoltage() {
      return this.getConfigValue('总压过压保护', '72.6V')
    },

    // 获取总压欠压保护
    getTotalUnderVoltage() {
      return this.getConfigValue('总压欠压保护', '52V')
    },

    // 获取压差保护
    getVoltageDiffProtection() {
      return this.getConfigValue('压差保护', '0.3V')
    },

    // 获取充电过流保护
    getChargeOverCurrent() {
      return this.getConfigValue('充电过流保护', '25A')
    },

    // 获取放电过流保护
    getDischargeOverCurrent() {
      return this.getConfigValue('放电过流保护', '40A')
    },

    // 获取充电高温保护
    getChargeHighTemp() {
      return this.getConfigValue('充电高温保护', '70°C')
    },

    // 获取充电低温保护
    getChargeLowTemp() {
      return this.getConfigValue('充电低温保护', '-20°C')
    },

    // 获取充电低温保护恢复值
    getChargeLowTempRecovery() {
      return this.getConfigValue('充电低温保护恢复值', '-10°C')
    },

    // 获取放电高温保护
    getDischargeHighTemp() {
      return this.getConfigValue('放电高温保护', '70°C')
    },

    // 获取放电低温保护
    getDischargeLowTemp() {
      return this.getConfigValue('放电低温保护', '-20°C')
    },

    // 获取放电低温保护恢复值
    getDischargeLowTempRecovery() {
      return this.getConfigValue('放电低温保护恢复值', '-10°C')
    },

    // 获取温差保护
    getTempDiffProtection() {
      return this.getConfigValue('温差保护', '20°C')
    },

    // 获取功率管温度保护
    getMosfetTempProtection() {
      return this.getConfigValue('功率管温度保护', '100°C')
    },

    // 获取功率管温度保护恢复值
    getMosfetTempRecovery() {
      return this.getConfigValue('功率管温度保护恢复值', '80°C')
    },

    // 获取电池箱内温度保护
    getBatteryBoxTempProtection() {
      return this.getConfigValue('电池箱内温度保护', '100°C')
    },

    // 获取电池箱内温度保护恢复值
    getBatteryBoxTempRecovery() {
      return this.getConfigValue('电池箱内温度保护恢复值', '100°C')
    },

    // 获取均衡开启电压
    getBalanceStartVoltage() {
      return this.getConfigValue('均衡开启电压', '3V')
    },

    // 获取均衡开启压差
    getBalanceStartDiff() {
      return this.getConfigValue('均衡开启压差', '0.003V')
    },

    // 获取均衡控制状态
    getBalanceControlStatus() {
      const value = this.getConfigValue('主动均衡控制开关', '开启')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取均衡控制文本
    getBalanceControlText() {
      const value = this.getConfigValue('主动均衡控制开关', '开启')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 获取电流校准状态
    getCurrentCalibrationStatus() {
      const value = this.getConfigValue('是否启动电流校准', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取电流校准文本
    getCurrentCalibrationText() {
      const value = this.getConfigValue('是否启动电流校准', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 获取电流校准值
    getCurrentCalibration() {
      return this.getConfigValue('电流校准', '952MA')
    },

    // 获取加热开关状态
    getHeatingStatus() {
      const value = this.getConfigValue('加热开关', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? 'enabled' : 'disabled'
    },

    // 获取加热开关文本
    getHeatingText() {
      const value = this.getConfigValue('加热开关', '关闭')
      return value === '开启' || value === 'true' || value === '1' ? '开启' : '关闭'
    },

    // 通用方法：从配置数据中获取值
    getConfigValue(paramName, defaultValue = 'N/A') {
      if (!this.configData || !this.configData.sets) {
        return defaultValue
      }

      // 遍历所有配置集合
      for (const set of this.configData.sets) {
        if (set.vars && Array.isArray(set.vars)) {
          // 在当前配置集合中查找参数
          const param = set.vars.find(v =>
            v.label === paramName ||
            v.name === paramName ||
            v.label?.includes(paramName) ||
            paramName.includes(v.label || '')
          )

          if (param) {
            // 如果是radio类型，返回选中的选项标签
            if (param.type === 'radio' && param.option && param.option.length > 0) {
              const selectedOption = param.option.find(opt => opt.value === param.value)
              return selectedOption ? selectedOption.label : param.value
            }

            // 返回参数值，如果有单位则添加单位
            let value = param.value
            if (param.unit && param.unit !== '') {
              value += param.unit
            }
            return value
          }
        }
      }

      return defaultValue
    }
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
}

.modal-container {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 800rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .modal-title {
    font-size: 32rpx;
    font-weight: bold;
  }

  .close-btn {
    width: 50rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);

    .close-icon {
      font-size: 36rpx;
      font-weight: bold;
    }
  }
}

.modal-content {
  flex: 1;
  padding: 20rpx;
  max-height: 60vh;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;

  .footer-btn {
    flex: 1;
    padding: 25rpx;
    border-radius: 15rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;

    &.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    &.secondary {
      background: #f8f9fa;
      color: #666;
      border: 1rpx solid #e9ecef;
    }
  }
}

// 实时状态样式
.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 30rpx 30rpx;

  .status-item {
    background: #f8f9fa;
    border-radius: 15rpx;
    padding: 25rpx;
    text-align: center;
    border: 2rpx solid transparent;

    .status-label {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 10rpx;
    }

    .status-value {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;

      &.highlight {
        color: #667eea;
        font-size: 36rpx;
      }
    }
  }
}

// 单体电压样式
.cell-voltages {
  margin-top: 30rpx;
  padding: 0 30rpx 30rpx;

  .cell-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .cell-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15rpx;

    .cell-item {
      background: #f8f9fa;
      border-radius: 10rpx;
      padding: 15rpx 10rpx;
      text-align: center;
      border: 2rpx solid transparent;

      &.cell-max {
        background: #fff2f0;
        border-color: #ff4d4f;

        .cell-value {
          color: #ff4d4f;
        }
      }

      &.cell-min {
        background: #e6f7ff;
        border-color: #1890ff;

        .cell-value {
          color: #1890ff;
        }
      }

      &.cell-normal {
        background: #f6ffed;
        border-color: #52c41a;

        .cell-value {
          color: #52c41a;
        }
      }

      .cell-label {
        display: block;
        font-size: 20rpx;
        color: #666;
        margin-bottom: 5rpx;
      }

      .cell-value {
        display: block;
        font-size: 22rpx;
        font-weight: bold;
      }
    }
  }
}

// 温度详情样式
.temp-details {
  margin-top: 30rpx;
  padding: 0 30rpx 30rpx;

  .temp-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .temp-list {
    .temp-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      margin-bottom: 10rpx;
      background: #f8f9fa;
      border-radius: 10rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .temp-label {
        font-size: 26rpx;
        color: #333;
      }

      .temp-value {
        font-size: 26rpx;
        font-weight: bold;

        &.temp-high {
          color: #ff4d4f;
        }

        &.temp-low {
          color: #1890ff;
        }

        &.temp-normal {
          color: #52c41a;
        }
      }
    }
  }
}

// 故障和报警样式
.fault-section, .alarm-section {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .fault-title, .alarm-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
    padding: 0 30rpx;
  }

  .fault-list, .alarm-list {
    padding: 0 30rpx;

    .fault-item, .alarm-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      margin-bottom: 10rpx;
      border-radius: 10rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.error {
        background: #fff2f0;
        border: 1rpx solid #ffccc7;
      }

      &.warning {
        background: #fffbe6;
        border: 1rpx solid #ffe58f;
      }

      .fault-icon, .alarm-icon {
        margin-right: 15rpx;
        font-size: 24rpx;
      }

      .fault-text, .alarm-text {
        flex: 1;
        font-size: 26rpx;
        color: #333;
      }
    }
  }
}

// 复用页面样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #e0e0e0;
    border-top: 4rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    color: #666;
    font-size: 28rpx;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;

  .error-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .error-text {
    color: #ff4757;
    font-size: 28rpx;
    text-align: center;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .retry-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 40rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
  }
}

.config-section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30rpx;
    display: flex;
    align-items: center;

    .section-icon {
      font-size: 32rpx;
      margin-right: 15rpx;
    }

    .section-title {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.param-list {
  padding: 0 30rpx 30rpx;

  .param-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .param-label {
      color: #333;
      font-size: 28rpx;
      flex: 1;
    }

    .param-value {
      color: #666;
      font-size: 28rpx;
      font-weight: 500;

      &.highlight {
        color: #667eea;
        font-weight: bold;
      }

      &.status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.enabled {
          background: #e8f5e8;
          color: #52c41a;
        }

        &.disabled {
          background: #fff2f0;
          color: #ff4d4f;
        }
      }
    }
  }
}

.param-grid {
  padding: 0 30rpx 30rpx;

  .param-group {
    margin-bottom: 40rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      display: block;
      color: #667eea;
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      padding-bottom: 10rpx;
      border-bottom: 2rpx solid #667eea;
    }

    .param-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .param-label {
        color: #333;
        font-size: 26rpx;
        flex: 1;
      }

      .param-value {
        color: #666;
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
