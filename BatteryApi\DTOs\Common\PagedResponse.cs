using System;
using System.Collections.Generic;

namespace BatteryApi.DTOs.Common
{
    /// <summary>
    /// 分页响应类
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResponse<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码 (从1开始)
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageNumber > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageNumber < TotalPages;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PagedResponse()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="items">数据列表</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageNumber">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        public PagedResponse(List<T> items, int totalCount, int pageNumber, int pageSize)
        {
            Items = items ?? new List<T>();
            TotalCount = totalCount;
            PageNumber = pageNumber;
            PageSize = pageSize;
        }

        /// <summary>
        /// 创建分页响应
        /// </summary>
        /// <param name="items">数据列表</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageNumber">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>分页响应</returns>
        public static PagedResponse<T> Create(List<T> items, int totalCount, int pageNumber, int pageSize)
        {
            return new PagedResponse<T>
            {
                Items = items ?? new List<T>(),
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// 创建空的分页响应
        /// </summary>
        /// <param name="pageNumber">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>空的分页响应</returns>
        public static PagedResponse<T> Empty(int pageNumber = 1, int pageSize = 10)
        {
            return new PagedResponse<T>
            {
                Items = new List<T>(),
                TotalCount = 0,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
    }

    /// <summary>
    /// 分页结果类（别名）
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PagedResult()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="items">数据列表</param>
        /// <param name="total">总记录数</param>
        /// <param name="page">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        public PagedResult(List<T> items, int total, int page, int pageSize)
        {
            Items = items ?? new List<T>();
            Total = total;
            Page = page;
            PageSize = pageSize;
            TotalPages = (int)Math.Ceiling((double)total / pageSize);
        }
    }
}
