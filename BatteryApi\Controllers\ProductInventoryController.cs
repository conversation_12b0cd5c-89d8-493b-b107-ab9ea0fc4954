using BatteryApi.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BatteryApi.Controllers
{
    [Route("api/inventory/product")]
    [ApiController]
    [Authorize]
    public class ProductInventoryController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<ProductInventoryController> _logger;

        public ProductInventoryController(ISqlSugarClient db, ILogger<ProductInventoryController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取商品库存列表
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>商品库存列表</returns>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetProductInventory([FromQuery] ProductInventoryQueryParameters parameters)
        {
            try
            {
                _logger.LogInformation("获取商品库存列表，参数：storeId={StoreId}, productId={ProductId}, categoryId={CategoryId}, keyword={Keyword}, page={Page}, pageSize={PageSize}",
                    parameters.StoreId, parameters.ProductId, parameters.CategoryId, parameters.Keyword, parameters.Page, parameters.PageSize);

                // 构建查询
                var query = _db.Queryable<StoreInventory>()
                    .LeftJoin<Store>((i, s) => i.StoreId == s.Id)
                    .LeftJoin<Product>((i, s, p) => i.ProductId == p.Id)
                    .LeftJoin<BatteryCategory>((i, s, p, c) => p.CategoryId == c.Id)
                    .Select((i, s, p, c) => new
                    {
                        i.Id,
                        i.StoreId,
                        StoreName = s.Name,
                        i.ProductId,
                        ProductName = p.Name,
                        ProductSpec = $"{p.Voltage}V{p.Capacity}Ah",
                        p.CategoryId,
                        CategoryName = c.Name,
                        i.Quantity,
                        i.AvailableQuantity,
                        i.UpdatedAt
                    });

                // 应用筛选条件
                if (parameters.StoreId.HasValue)
                {
                    query = query.Where(i => i.StoreId == parameters.StoreId.Value);
                }

                if (parameters.ProductId.HasValue)
                {
                    query = query.Where(i => i.ProductId == parameters.ProductId.Value);
                }

                // 使用SqlSugar的WhereIF方法替代原生SQL
                if (parameters.CategoryId.HasValue)
                {
                    query = query.Where(i => i.CategoryId == parameters.CategoryId.Value);
                }

                // 关键词搜索，使用SqlSugar的条件查询
                string kw = parameters.Keyword ?? string.Empty;

                // 如果关键词是特殊值 'empty'，则不应用筛选条件
                if (!string.IsNullOrEmpty(kw) && kw != "empty")
                {
                    query = query.Where(i =>
                        i.StoreName.Contains(kw) ||
                        i.ProductName.Contains(kw) ||
                        i.ProductSpec.Contains(kw));
                }

                // 获取总记录数
                var total = await query.CountAsync();

                // 分页查询
                var items = await query
                    .OrderBy(i => i.StoreId)
                    .OrderBy(i => i.ProductId)
                    .Skip((parameters.Page - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();

                // 返回结果
                return Ok(new
                {
                    items,
                    totalItems = total,
                    page = parameters.Page,
                    pageSize = parameters.PageSize,
                    totalPages = (int)Math.Ceiling(total / (double)parameters.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取商品库存列表失败");
                return StatusCode(500, new { message = "获取商品库存列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取库存历史记录
        /// </summary>
        /// <param name="storeId">门店ID</param>
        /// <param name="productId">商品ID</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>库存历史记录</returns>
        [HttpGet("history")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetInventoryHistory(
            [FromQuery] int? storeId = null,
            [FromQuery] int? productId = null,
            [FromQuery] string operationType = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string keyword = "",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("获取库存历史记录，参数：storeId={StoreId}, productId={ProductId}, operationType={OperationType}, startDate={StartDate}, endDate={EndDate}, page={Page}, pageSize={PageSize}",
                    storeId, productId, operationType, startDate, endDate, page, pageSize);

                // 构建查询
                var query = _db.Queryable<InventoryHistory>()
                    .LeftJoin<Store>((h, s) => h.StoreId == s.Id)
                    .LeftJoin<Product>((h, s, p) => h.ProductId == p.Id)
                    .Select((h, s, p) => new
                    {
                        h.Id,
                        h.StoreId,
                        StoreName = s.Name,
                        h.ProductId,
                        ProductName = p.Name,
                        ProductSpec = $"{p.Voltage}V{p.Capacity}Ah",
                        h.OperationType,
                        h.Quantity,
                        h.Reason,
                        h.Remark,
                        h.OperatedBy,
                        h.OperationTime,
                        h.FromStoreId,
                        h.ToStoreId
                    });

                // 应用筛选条件
                if (storeId.HasValue)
                {
                    query = query.Where(h => h.StoreId == storeId.Value || h.FromStoreId == storeId.Value || h.ToStoreId == storeId.Value);
                }

                if (productId.HasValue)
                {
                    query = query.Where(h => h.ProductId == productId.Value);
                }

                if (!string.IsNullOrEmpty(operationType))
                {
                    query = query.Where(h => h.OperationType == operationType);
                }

                if (startDate.HasValue)
                {
                    query = query.Where(h => h.OperationTime >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(h => h.OperationTime <= endDate.Value.AddDays(1).AddSeconds(-1));
                }

                // 获取总记录数
                var total = await query.CountAsync();

                // 分页查询
                var items = await query
                    .OrderByDescending(h => h.OperationTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 处理调拨记录的门店名称
                foreach (var item in items)
                {
                    if (item.OperationType == "Transfer" && item.FromStoreId.HasValue && item.ToStoreId.HasValue)
                    {
                        var fromStore = await _db.Queryable<Store>().FirstAsync(s => s.Id == item.FromStoreId.Value);
                        var toStore = await _db.Queryable<Store>().FirstAsync(s => s.Id == item.ToStoreId.Value);

                        if (fromStore != null)
                        {
                            item.GetType().GetProperty("FromStoreName").SetValue(item, fromStore.Name);
                        }

                        if (toStore != null)
                        {
                            item.GetType().GetProperty("ToStoreName").SetValue(item, toStore.Name);
                        }
                    }
                }

                // 返回结果
                return Ok(new
                {
                    items,
                    totalItems = total,
                    page,
                    pageSize,
                    totalPages = (int)Math.Ceiling(total / (double)pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存历史记录失败");
                return StatusCode(500, new { message = "获取库存历史记录失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 商品入库
        /// </summary>
        /// <param name="request">入库请求</param>
        /// <returns>入库结果</returns>
        [HttpPost("stock-in")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> StockIn([FromBody] ProductStockInRequest request)
        {
            try
            {
                _logger.LogInformation("商品入库，参数：storeId={StoreId}, productId={ProductId}, quantity={Quantity}",
                    request.StoreId, request.ProductId, request.Quantity);

                // 验证参数
                if (request.StoreId <= 0 || request.ProductId <= 0 || request.Quantity <= 0)
                {
                    return BadRequest(new { message = "无效的参数" });
                }

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == request.StoreId);
                if (store == null)
                {
                    return NotFound(new { message = $"门店ID {request.StoreId} 不存在" });
                }

                // 检查商品是否存在
                var product = await _db.Queryable<Product>().FirstAsync(p => p.Id == request.ProductId);
                if (product == null)
                {
                    return NotFound(new { message = $"商品ID {request.ProductId} 不存在" });
                }

                // 检查库存记录是否存在
                var inventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.StoreId && i.ProductId == request.ProductId);

                int oldQuantity = 0;
                int oldAvailableQuantity = 0;

                if (inventory == null)
                {
                    // 创建新库存记录
                    inventory = new StoreInventory
                    {
                        StoreId = request.StoreId,
                        ProductId = request.ProductId,
                        Quantity = request.Quantity,
                        AvailableQuantity = request.Quantity,
                        UpdatedAt = DateTime.Now
                    };

                    await _db.Insertable(inventory).ExecuteCommandAsync();
                }
                else
                {
                    // 更新现有库存记录
                    oldQuantity = inventory.Quantity;
                    oldAvailableQuantity = inventory.AvailableQuantity;

                    inventory.Quantity += request.Quantity;
                    inventory.AvailableQuantity += request.Quantity;
                    inventory.UpdatedAt = DateTime.Now;

                    await _db.Updateable(inventory).ExecuteCommandAsync();
                }

                // 记录库存历史
                var history = new InventoryHistory
                {
                    StoreId = request.StoreId,
                    ProductId = request.ProductId,
                    OperationType = "StockIn",
                    Quantity = request.Quantity,
                    Remark = request.Remark,
                    OperatedBy = User.Identity.Name ?? "admin",
                    OperationTime = DateTime.Now
                };

                await _db.Insertable(history).ExecuteCommandAsync();

                // 返回结果
                return Ok(new
                {
                    success = true,
                    message = "商品入库成功",
                    data = new
                    {
                        storeId = request.StoreId,
                        storeName = store.Name,
                        productId = request.ProductId,
                        productName = product.Model,
                        oldQuantity,
                        newQuantity = inventory.Quantity,
                        oldAvailableQuantity,
                        newAvailableQuantity = inventory.AvailableQuantity,
                        operatorName = history.OperatedBy,
                        operationTime = history.OperationTime
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "商品入库失败");
                return StatusCode(500, new { message = "商品入库失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 商品出库
        /// </summary>
        /// <param name="request">出库请求</param>
        /// <returns>出库结果</returns>
        [HttpPost("stock-out")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> StockOut([FromBody] ProductStockOutRequest request)
        {
            try
            {
                _logger.LogInformation("商品出库，参数：storeId={StoreId}, productId={ProductId}, quantity={Quantity}, reason={Reason}",
                    request.StoreId, request.ProductId, request.Quantity, request.Reason);

                // 验证参数
                if (request.StoreId <= 0 || request.ProductId <= 0 || request.Quantity <= 0)
                {
                    return BadRequest(new { message = "无效的参数" });
                }

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == request.StoreId);
                if (store == null)
                {
                    return NotFound(new { message = $"门店ID {request.StoreId} 不存在" });
                }

                // 检查商品是否存在
                var product = await _db.Queryable<Product>().FirstAsync(p => p.Id == request.ProductId);
                if (product == null)
                {
                    return NotFound(new { message = $"商品ID {request.ProductId} 不存在" });
                }

                // 检查库存记录是否存在
                var inventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.StoreId && i.ProductId == request.ProductId);

                if (inventory == null)
                {
                    return BadRequest(new { message = $"门店 {request.StoreId} 没有商品 {request.ProductId} 的库存记录" });
                }

                // 检查库存是否足够
                if (inventory.AvailableQuantity < request.Quantity)
                {
                    return BadRequest(new { message = $"可用库存不足，当前可用库存: {inventory.AvailableQuantity}, 请求出库数量: {request.Quantity}" });
                }

                // 更新库存记录
                int oldQuantity = inventory.Quantity;
                int oldAvailableQuantity = inventory.AvailableQuantity;

                inventory.Quantity -= request.Quantity;
                inventory.AvailableQuantity -= request.Quantity;
                inventory.UpdatedAt = DateTime.Now;

                await _db.Updateable(inventory).ExecuteCommandAsync();

                // 记录库存历史
                var history = new InventoryHistory
                {
                    StoreId = request.StoreId,
                    ProductId = request.ProductId,
                    OperationType = "StockOut",
                    Quantity = request.Quantity,
                    Reason = request.Reason,
                    Remark = request.Remark,
                    OperatedBy = User.Identity.Name ?? "admin",
                    OperationTime = DateTime.Now
                };

                await _db.Insertable(history).ExecuteCommandAsync();

                // 返回结果
                return Ok(new
                {
                    success = true,
                    message = "商品出库成功",
                    data = new
                    {
                        storeId = request.StoreId,
                        storeName = store.Name,
                        productId = request.ProductId,
                        productName = product.Model,
                        oldQuantity,
                        newQuantity = inventory.Quantity,
                        oldAvailableQuantity,
                        newAvailableQuantity = inventory.AvailableQuantity,
                        operatorName = history.OperatedBy,
                        operationTime = history.OperationTime
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "商品出库失败");
                return StatusCode(500, new { message = "商品出库失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 商品调拨
        /// </summary>
        /// <param name="request">调拨请求</param>
        /// <returns>调拨结果</returns>
        [HttpPost("transfer")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Transfer([FromBody] ProductTransferRequest request)
        {
            try
            {
                _logger.LogInformation("商品调拨，参数：fromStoreId={FromStoreId}, toStoreId={ToStoreId}, productId={ProductId}, quantity={Quantity}",
                    request.FromStoreId, request.ToStoreId, request.ProductId, request.Quantity);

                // 验证参数
                if (request.FromStoreId <= 0 || request.ToStoreId <= 0 || request.ProductId <= 0 || request.Quantity <= 0)
                {
                    return BadRequest(new { message = "无效的参数" });
                }

                if (request.FromStoreId == request.ToStoreId)
                {
                    return BadRequest(new { message = "源门店和目标门店不能相同" });
                }

                // 检查源门店是否存在
                var fromStore = await _db.Queryable<Store>().FirstAsync(s => s.Id == request.FromStoreId);
                if (fromStore == null)
                {
                    return NotFound(new { message = $"源门店ID {request.FromStoreId} 不存在" });
                }

                // 检查目标门店是否存在
                var toStore = await _db.Queryable<Store>().FirstAsync(s => s.Id == request.ToStoreId);
                if (toStore == null)
                {
                    return NotFound(new { message = $"目标门店ID {request.ToStoreId} 不存在" });
                }

                // 检查商品是否存在
                var product = await _db.Queryable<Product>().FirstAsync(p => p.Id == request.ProductId);
                if (product == null)
                {
                    return NotFound(new { message = $"商品ID {request.ProductId} 不存在" });
                }

                // 检查源门店库存记录是否存在
                var fromInventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.FromStoreId && i.ProductId == request.ProductId);

                if (fromInventory == null)
                {
                    return BadRequest(new { message = $"源门店 {request.FromStoreId} 没有商品 {request.ProductId} 的库存记录" });
                }

                // 检查源门店库存是否足够
                if (fromInventory.AvailableQuantity < request.Quantity)
                {
                    return BadRequest(new { message = $"源门店可用库存不足，当前可用库存: {fromInventory.AvailableQuantity}, 请求调拨数量: {request.Quantity}" });
                }

                // 检查目标门店库存记录是否存在
                var toInventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.ToStoreId && i.ProductId == request.ProductId);

                int toOldQuantity = 0;
                int toOldAvailableQuantity = 0;

                if (toInventory == null)
                {
                    // 创建目标门店库存记录
                    toInventory = new StoreInventory
                    {
                        StoreId = request.ToStoreId,
                        ProductId = request.ProductId,
                        Quantity = request.Quantity,
                        AvailableQuantity = request.Quantity,
                        UpdatedAt = DateTime.Now
                    };

                    await _db.Insertable(toInventory).ExecuteCommandAsync();
                }
                else
                {
                    // 更新目标门店库存记录
                    toOldQuantity = toInventory.Quantity;
                    toOldAvailableQuantity = toInventory.AvailableQuantity;

                    toInventory.Quantity += request.Quantity;
                    toInventory.AvailableQuantity += request.Quantity;
                    toInventory.UpdatedAt = DateTime.Now;

                    await _db.Updateable(toInventory).ExecuteCommandAsync();
                }

                // 更新源门店库存记录
                int fromOldQuantity = fromInventory.Quantity;
                int fromOldAvailableQuantity = fromInventory.AvailableQuantity;

                fromInventory.Quantity -= request.Quantity;
                fromInventory.AvailableQuantity -= request.Quantity;
                fromInventory.UpdatedAt = DateTime.Now;

                await _db.Updateable(fromInventory).ExecuteCommandAsync();

                // 记录库存历史
                var history = new InventoryHistory
                {
                    StoreId = request.FromStoreId,
                    ProductId = request.ProductId,
                    OperationType = "Transfer",
                    Quantity = request.Quantity,
                    Remark = request.Remark,
                    OperatedBy = User.Identity.Name ?? "admin",
                    OperationTime = DateTime.Now,
                    FromStoreId = request.FromStoreId,
                    ToStoreId = request.ToStoreId
                };

                await _db.Insertable(history).ExecuteCommandAsync();

                // 返回结果
                return Ok(new
                {
                    success = true,
                    message = "商品调拨成功",
                    data = new
                    {
                        fromStoreId = request.FromStoreId,
                        fromStoreName = fromStore.Name,
                        toStoreId = request.ToStoreId,
                        toStoreName = toStore.Name,
                        productId = request.ProductId,
                        productName = product.Model,
                        fromStoreOldQuantity = fromOldQuantity,
                        fromStoreNewQuantity = fromInventory.Quantity,
                        fromStoreOldAvailableQuantity = fromOldAvailableQuantity,
                        fromStoreNewAvailableQuantity = fromInventory.AvailableQuantity,
                        toStoreOldQuantity = toOldQuantity,
                        toStoreNewQuantity = toInventory.Quantity,
                        toStoreOldAvailableQuantity = toOldAvailableQuantity,
                        toStoreNewAvailableQuantity = toInventory.AvailableQuantity,
                        operatorName = history.OperatedBy,
                        operationTime = history.OperationTime
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "商品调拨失败");
                return StatusCode(500, new { message = "商品调拨失败", error = ex.Message });
            }
        }
    }

    /// <summary>
    /// 产品入库请求
    /// </summary>
    public class ProductStockInRequest
    {
        /// <summary>
        /// 门店ID
        /// </summary>
        public int StoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 产品出库请求
    /// </summary>
    public class ProductStockOutRequest
    {
        /// <summary>
        /// 门店ID
        /// </summary>
        public int StoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 出库原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 产品调拨请求
    /// </summary>
    public class ProductTransferRequest
    {
        /// <summary>
        /// 源门店ID
        /// </summary>
        public int FromStoreId { get; set; }

        /// <summary>
        /// 目标门店ID
        /// </summary>
        public int ToStoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 商品库存查询参数
    /// </summary>
    public class ProductInventoryQueryParameters
    {
        /// <summary>
        /// 门店ID
        /// </summary>
        public int? StoreId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public int? ProductId { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// 关键词
        /// </summary>
        public string Keyword { get; set; } = string.Empty;

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
}
