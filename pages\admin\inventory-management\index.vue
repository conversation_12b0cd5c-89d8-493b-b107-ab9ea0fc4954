<template>
  <page-wrapper :pageLoadingProp="isPageLoading" :pageLoadingTextProp="loadingText">
    <view class="inventory-container">
    <!-- 页面头部 - 从门店详情页进入时隐藏 -->
    <view class="page-header" v-if="!_fromStoreDetail">
      <view class="header-back" @click="goBack">
        <u-icon name="arrow-left" size="20" color="#333"></u-icon>
      </view>
      <view class="header-title">商品库存管理</view>
    </view>


    <!-- 门店选择区域 - 从门店详情页进入时隐藏 -->
    <view class="card store-card" v-if="!_fromStoreDetail">
      <view class="card-header">
        <view class="card-title">选择门店</view>
        <view class="card-subtitle">共 {{ storeList.length }} 家门店</view>
      </view>
      <scroll-view scroll-x class="store-scroll" show-scrollbar="false">
        <view class="store-list">
          <view
            v-for="store in storeList"
            :key="store.id"
            class="store-item"
            :class="{ active: selectedStore && selectedStore.id === store.id }"
            @click="selectStore(store)"
          >
            <u-icon
              name="home"
              size="16"
              :color="
                selectedStore && selectedStore.id === store.id
                  ? '#ffffff'
                  : '#3c9cff'
              "
            ></u-icon>
            <text>{{ store.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 商品库存区域 -->
    <block v-if="selectedStore">
      <!-- 门店信息和统计 -->
      <view class="card info-card">
        <view class="store-header">
          <view class="store-info">
            <text class="store-name">{{ selectedStore.name }}</text>
            <text class="store-subtitle">{{
              _fromStoreDetail ? "门店商品库存管理" : "商品库存管理"
            }}</text>
          </view>
          <view class="refresh-btn">
            <u-button
              type="primary"
              size="mini"
              @click="refreshInventory"
              :loading="refreshing"
              icon="reload"
              shape="circle"
            ></u-button>
          </view>
        </view>

        <view class="stats-panel">
          <view class="stat-box">
            <text class="stat-value">{{ totalProducts }}</text>
            <text class="stat-label">商品总数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-box">
            <text class="stat-value">{{ totalInventory }}</text>
            <text class="stat-label">库存总量</text>
          </view>
        </view>
      </view>

      <!-- 搜索和分类 -->
      <view class="card filter-card">
        <view class="search-box">
          <u-search
            v-model="keyword"
            placeholder="搜索商品名称、编码、规格或分类"
            :show-action="false"
            @search="onSearch"
            @clear="onSearch"
            :height="60"
            :clearabled="true"
            shape="round"
          ></u-search>
        </view>

        <scroll-view scroll-x class="category-scroll" show-scrollbar="false">
          <view class="category-list">
            <view
              v-for="(tab, idx) in categoryTabs"
              :key="idx"
              class="category-item"
              :class="{ active: categoryIndex === idx }"
              @click="onTabClick(idx)"
            >
              <text>{{ tab.name }}</text>
              <text v-if="idx > 0" class="category-badge">{{
                getCategoryProductCount(tab.id)
              }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 商品列表 -->
      <scroll-view
        scroll-y
        class="product-scroll"
        @scrolltolower="loadMore"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
        scroll-with-animation
        :style="
          _fromStoreDetail
            ? 'height: calc(100vh - 220px);'
            : 'height: calc(100vh - 320px);'
        "
      >
        <view
          class="empty-tip"
          v-if="filteredProducts.length === 0 && !isPageLoading"
        >
          <u-empty mode="data" text="暂无商品数据"></u-empty>
        </view>

        <view
          v-for="(product, index) in filteredProducts"
          :key="'product-' + product.id + '-' + index"
          class="card product-card"
        >
          <view class="product-header">
            <view class="product-title">
              <text class="product-name"
                >编码:{{ product.code || `#${product.id}` }}</text
              >
            </view>
            <view
              class="stock-tag"
              :class="getInventoryStatusClass(product.id)"
            >
              <text>当前库存:{{ getProductInventory(product.id, true) }}</text>
            </view>
          </view>

          <view class="product-info">
            <view class="info-row">
              <view class="info-item">
                <text class="info-label">分类</text>
                <text class="info-value">{{
                  getCategoryName(product.categoryId) || "未分类"
                }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">规格</text>
                <text class="info-value">{{ product.model || "无" }}</text>
              </view>
            </view>
            <view class="info-row">
              <view class="info-item">
                <text class="info-label">售价</text>
                <text class="info-value price"
                  >¥{{ formatAmount(parseFloat(product.price) || 0) }}</text
                >
              </view>
              <view class="info-item" v-if="product.rentPrice">
                <text class="info-label">日租金</text>
                <text class="info-value price"
                  >¥{{ formatAmount(parseFloat(product.rentPrice) || 0) }}</text
                >
              </view>
            </view>
          </view>

          <view class="inventory-panel">
            <!-- <view class="inventory-header">
              <text class="inventory-title">库存管理</text>
              <text class="inventory-count" :class="getInventoryStatusClass(product.id)">
                当前: {{ getProductInventory(product.id, true) }}
              </text>
            </view> -->
            <view class="inventory-controls">
              <view class="quantity-group">
                <u-button
                  type="success"
                  size="mini"
                  @click="addInventory(product)"
                  class="control-btn"
                  >增加</u-button
                >
                <u-number-box
                  v-model="product.newQuantity"
                  :min="0"
                  :max="999"
                  :step="1"
                  class="number-input"
                ></u-number-box>
                <u-button
                  type="warning"
                  size="mini"
                  @click="subtractInventory(product)"
                  :disabled="
                    getProductInventory(product.id) < product.newQuantity
                  "
                  class="control-btn"
                  >减少</u-button
                >
              </view>
              <u-button
                type="primary"
                size="mini"
                @click="setInventory(product)"
                :disabled="!isValidQuantity(product.newQuantity)"
                class="set-btn"
                >设置库存</u-button
              >
            </view>
          </view>
        </view>

        <view class="loading-more" v-if="isPageLoading">
          <u-loading-icon mode="circle" size="24"></u-loading-icon>
          <text>加载中...</text>
        </view>

        <view class="no-more" v-if="noMore && filteredProducts.length > 0">
          <u-divider text="没有更多数据了"></u-divider>
        </view>
      </scroll-view>
    </block>

    <!-- 未选择门店提示 - 从门店详情页进入时不显示 -->
    <view class="empty-state" v-if="!selectedStore && !_fromStoreDetail">
      <u-empty mode="list" text="请先选择门店"></u-empty>
    </view>

    <!-- 不再需要单独的 Toast 和 Loading 组件，由 page-wrapper 提供 -->
    </view>
  </page-wrapper>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex";

export default {
  name: "InventoryManagement",
  data() {
    return {
      selectedStore: null,
      initialStoreId: null, // 从页面参数接收的门店ID
      _fromStoreDetail: false, // 标记是否从门店详情页跳转过来
      categoryIndex: 0, // 确保是数字类型
      keyword: "",
      productList: [], // 当前分类的商品列表
      allProducts: [], // 所有商品列表，用于计算各分类的商品数量
      categoryProductCounts: {}, // 缓存各分类的商品数量
      refreshing: false,
      autoRefreshTimer: null,
      tabsReady: false, // 标记 tabs 组件是否已准备好
      loadingText: "加载中...", // 加载提示文本
      loadingResetTimer: null, // 加载状态重置定时器
      page: 1, // 当前页码
      pageSize: 10, // 每页数量
      noMore: false, // 是否没有更多数据
      loadMoreTimer: null, // 加载更多的定时器
    };
  },
  computed: {
    ...mapState("store", ["storeList"]),
    ...mapState("product", ["categoryList"]),
    ...mapState("storeInventory", {
      storeLoading: (state) => state.loading,
      currentStoreInventory: (state) => state.currentStoreInventory,
    }),
    ...mapGetters("storeInventory", ["productInventory"]),

    // 监听加载状态
    isPageLoading: {
      get() {
        return this.storeLoading;
      },
      set(value) {
        // 不直接修改 Vuex 状态，仅用于监听
      },
    },

    // 分类选项卡
    categoryTabs() {
      try {
        // 确保返回正确的格式，uView 的 u-tabs 组件需要 name 属性
        const tabs = [{ name: "全部", id: 0 }];

        if (this.categoryList && this.categoryList.length > 0) {
          // 打印分类列表，帮助调试
          console.log("原始分类列表:", JSON.stringify(this.categoryList));

          this.categoryList.forEach((category) => {
            if (category && category.name) {
              // 确保分类ID是数字类型
              const categoryId =
                typeof category.id === "string"
                  ? parseInt(category.id)
                  : category.id;

              tabs.push({
                name: category.name,
                id: categoryId,
                code: category.code,
                originalId: category.id, // 保留原始ID，用于调试
              });

              console.log(
                `添加分类标签: ${category.name}, ID: ${categoryId}, 原始ID: ${
                  category.id
                }, 类型: ${typeof categoryId}`
              );
            }
          });
        }

        console.log("最终分类选项卡:", JSON.stringify(tabs));
        return tabs;
      } catch (error) {
        console.error("生成分类选项卡失败:", error);
        return [{ name: "全部", id: 0 }];
      }
    },

    // 当前选中的分类ID
    selectedCategoryId() {
      try {
        // 确保 categoryIndex 是有效数字
        const index = parseInt(this.categoryIndex, 10);

        // 如果是 0 或无效数字，返回 null（全部分类）
        if (isNaN(index) || index === 0) {
          return null;
        }

        // 获取当前选中的分类
        const selectedTab = this.categoryTabs[index];
        if (!selectedTab) {
          console.warn("找不到选中的分类标签:", index);
          return null;
        }

        // 返回对应分类的 ID
        const categoryId = selectedTab.id;
        console.log(
          "当前选中分类ID:",
          categoryId,
          "索引:",
          index,
          "分类名称:",
          selectedTab.name
        );
        return categoryId;
      } catch (error) {
        console.error("获取选中分类ID失败:", error);
        return null;
      }
    },

    // 过滤后的商品列表
    filteredProducts() {
      try {
        if (!this.productList || this.productList.length === 0) {
          console.log("商品列表为空");
          return [];
        }

        // 直接返回API查询结果，不再在前端进行过滤
        // 因为我们已经在 onSearch 和 onTabClick 中通过API获取了过滤后的商品列表
        console.log(
          `直接使用API返回的商品列表，总数: ${this.productList.length}`
        );

        return this.productList;
      } catch (error) {
        console.error("获取商品列表失败:", error);
        return [];
      }
    },

    // 计算当前门店的商品总数
    totalProducts() {
      try {
        if (!this.selectedStore || !this.currentStoreInventory) {
          return 0;
        }

        // 返回有库存记录的商品数量
        return this.currentStoreInventory.length;
      } catch (error) {
        console.error("计算商品总数失败:", error);
        return 0;
      }
    },

    // 计算当前门店的库存总量
    totalInventory() {
      try {
        if (!this.selectedStore || !this.currentStoreInventory) {
          return 0;
        }

        // 计算所有商品库存数量的总和
        return this.currentStoreInventory.reduce((total, item) => {
          const quantity = item.quantity !== undefined ? item.quantity : 0;
          return (
            total +
            (typeof quantity === "string" ? parseInt(quantity) : quantity)
          );
        }, 0);
      } catch (error) {
        console.error("计算库存总量失败:", error);
        return 0;
      }
    },
  },
  methods: {
    ...mapActions("store", ["getStoreList"]),
    ...mapActions("battery", ["getCategoryList"]),
    ...mapActions("storeInventory", [
      "getStoreInventory",
      "updateStoreInventory",
      "setCurrentStore",
    ]),

    // 刷新当前门店库存，添加一个参数，允许强制刷新
    refreshCurrentStoreInventory(force = false) {
      // 如果是从门店详情页跳转过来的，且不是强制刷新，不执行刷新
      if (this._fromStoreDetail && !force) {
        console.log("从门店详情页跳转过来，不自动刷新库存数据");
        return Promise.resolve();
      }

      return this.$store.dispatch(
        "storeInventory/refreshCurrentStoreInventory"
      );
    },

    // 初始化数据
    async initData() {
      try {
        // 获取门店列表
        await this.getStoreList();

        // 获取商品分类列表
        console.log("开始获取商品分类列表");
        const categoryResult = await this.$store.dispatch(
          "product/getCategoryList",
          { keyword: "empty" }
        );
        console.log("获取商品分类列表结果:", categoryResult);

        // 确保分类ID是数字类型
        if (this.categoryList && this.categoryList.length > 0) {
          console.log("原始分类列表:", JSON.stringify(this.categoryList));

          // 修改 Vuex 中的分类列表，确保 ID 是数字类型
          const normalizedCategories = this.categoryList.map((category) => {
            if (category.id && typeof category.id === "string") {
              return {
                ...category,
                id: parseInt(category.id),
              };
            }
            return category;
          });

          // 使用 commit 更新 Vuex 中的分类列表
          this.$store.commit("product/SET_CATEGORY_LIST", normalizedCategories);

          console.log(
            "规范化后的分类列表:",
            JSON.stringify(normalizedCategories)
          );
        }

        // 获取所有商品列表（不带分类过滤）
        await this.loadProducts();

        // 如果有初始门店ID，自动选择对应门店
        if (
          this.initialStoreId &&
          this.storeList &&
          this.storeList.length > 0
        ) {
          console.log("尝试自动选择门店，ID:", this.initialStoreId);

          // 查找对应的门店
          const targetStore = this.storeList.find(
            (store) =>
              store.id === this.initialStoreId ||
              store.id === parseInt(this.initialStoreId)
          );

          if (targetStore) {
            console.log("找到匹配的门店:", targetStore.name);

            // 设置标记，表示已经尝试过选择门店
            this._hasTriedSelectStore = true;

            // 根据来源选择不同的方法
            if (this._fromStoreDetail) {
              // 如果是从门店详情页跳转过来的，使用不初始化库存的方法
              console.log("从门店详情页跳转，使用不初始化库存的方法");
              // 使用不会触发 API 调用的方法
              await this.selectStoreWithoutInit(targetStore);

              // 不自动刷新库存数据
              console.log("已选择门店，不自动刷新库存数据");
            } else {
              // 否则使用普通的选择门店方法
              console.log("正常选择门店，可能会初始化库存");
              await this.selectStore(targetStore);

              // 不自动刷新库存数据
              console.log("已选择门店，不自动刷新库存数据");
            }

            // 显示提示
            this.showToast(`已自动选择门店: ${targetStore.name}`, "success");
          } else {
            console.warn("未找到匹配的门店:", this.initialStoreId);
            this.showToast("未找到指定的门店", "warning");
          }
        }
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.showToast("加载数据失败，请重试", "error");
      }
    },

    // 加载商品列表
    async loadProducts(categoryId = null) {
      try {
        // 导入产品API
        const ProductAPI = (await import("@/api/product")).default;

        // 构建请求参数
        const params = {
          pageSize: 1000, // 获取足够多的商品
          // 不传递 keyword 参数，让后端返回所有商品
        };

        // 如果指定了分类ID，添加到请求参数
        if (categoryId && categoryId !== 0) {
          params.categoryId = categoryId;
          console.log(`按分类ID加载商品: ${categoryId}`);
        } else {
          console.log("加载所有商品");

          // 如果是加载所有商品，同时更新 allProducts
          try {
            // 获取所有商品（不带分类过滤）
            const allProductsResponse = await ProductAPI.getProducts({
              pageSize: 1000,
              // 不传递 keyword 参数，让后端返回所有商品
            });

            console.log("获取所有商品列表响应:", allProductsResponse);

            // 处理响应数据
            if (
              allProductsResponse &&
              allProductsResponse.code === 0 &&
              allProductsResponse.data &&
              allProductsResponse.data.batteries
            ) {
              this.allProducts = allProductsResponse.data.batteries;
            } else if (
              allProductsResponse &&
              allProductsResponse.data &&
              allProductsResponse.data.items
            ) {
              this.allProducts = allProductsResponse.data.items;
            } else if (allProductsResponse && allProductsResponse.data) {
              this.allProducts = allProductsResponse.data;
            } else if (allProductsResponse && allProductsResponse.items) {
              this.allProducts = allProductsResponse.items;
            } else if (Array.isArray(allProductsResponse)) {
              this.allProducts = allProductsResponse;
            }

            console.log(`获取到所有商品数量: ${this.allProducts.length}`);

            // 计算并缓存各分类的商品数量
            this.updateCategoryProductCounts();
          } catch (error) {
            console.error("获取所有商品失败:", error);
          }
        }

        console.log("请求参数:", params);

        // 获取当前分类的商品
        const response = await ProductAPI.getProducts(params);

        console.log("获取商品列表响应:", response);

        let products = [];

        // 处理响应数据
        if (
          response &&
          response.code === 0 &&
          response.data &&
          response.data.batteries
        ) {
          products = response.data.batteries;
        } else if (response && response.data && response.data.items) {
          products = response.data.items;
        } else if (response && response.data) {
          products = response.data;
        } else if (response && response.items) {
          products = response.items;
        } else if (Array.isArray(response)) {
          products = response;
        }

        console.log("处理后的商品列表:", products);

        // 确保所有商品的分类ID是数字类型
        products = products.map((product) => {
          // 确保分类ID是数字类型
          if (product.categoryId && typeof product.categoryId === "string") {
            product.categoryId = parseInt(product.categoryId);
          }

          // 如果商品有 categoryCode 但没有 categoryId，尝试从分类列表中查找对应的 ID
          if (
            !product.categoryId &&
            product.categoryCode &&
            this.categoryList &&
            this.categoryList.length > 0
          ) {
            const category = this.categoryList.find(
              (c) => c.code === product.categoryCode
            );
            if (category) {
              product.categoryId =
                typeof category.id === "string"
                  ? parseInt(category.id)
                  : category.id;
              console.log(
                `根据 categoryCode ${product.categoryCode} 设置 categoryId: ${product.categoryId}`
              );
            }
          }

          return product;
        });

        // // 打印每个商品的分类ID
        // products.forEach(product => {
        //   console.log(`商品 ${product.id} (${product.name || product.code || '未命名'}) 的分类ID: ${product.categoryId}, 类型: ${typeof product.categoryId}`);
        // });

        // 为每个商品添加新数量属性
        this.productList = products.map((product) => ({
          ...product,
          newQuantity: 0,
        }));

        console.log("最终商品列表:", this.productList);
      } catch (error) {
        console.error("加载商品列表失败:", error);
        this.showToast("加载商品列表失败", "error");
      }
    },

    // 选择门店
    async selectStore(store) {
      try {
        this.selectedStore = store;
        console.log("选择门店:", store.name, store.id);

        // 重置所有商品的输入框值为0
        this.resetAllInputValues();

        // 重置警告标记
        this._warnedNoInventory = false;
        this._warnedProducts = {};

        // 设置当前门店并获取库存，直接传递 ID 而不是对象
        await this.setCurrentStore(store.id);

        // 不自动刷新库存数据
        console.log("已选择门店，不自动刷新库存数据");

        console.log("门店商品库存:", this.currentStoreInventory);

        // 如果门店没有库存数据，只记录日志，不显示提示
        if (
          !this.currentStoreInventory ||
          this.currentStoreInventory.length === 0
        ) {
          console.log(`门店 ${store.id} 没有库存数据`);
        }
      } catch (error) {
        console.error("选择门店失败:", error);
        this.showToast(`加载门店库存失败: ${error.message}`, "error");
      }
    },

    // 选择门店但不初始化库存（用于从门店详情页跳转过来的情况）
    async selectStoreWithoutInit(store) {
      try {
        // 如果已经选择了相同的门店，不重复操作
        if (this.selectedStore && this.selectedStore.id === store.id) {
          console.log(
            "已经选择了相同的门店，不重复操作:",
            store.name,
            store.id
          );
          return;
        }

        this.selectedStore = store;
        console.log("选择门店（不初始化）:", store.name, store.id);

        // 重置所有商品的输入框值为0
        this.resetAllInputValues();

        // 重置警告标记
        this._warnedNoInventory = false;
        this._warnedProducts = {};

        // 设置当前门店但跳过获取库存数据，避免重复请求
        await this.$store.dispatch(
          "storeInventory/setCurrentStore",
          store.id,
          true
        );

        // 不自动刷新库存数据
        console.log("已选择门店，不自动刷新库存数据");

        console.log("门店商品库存:", this.currentStoreInventory);

        // 如果门店没有库存数据，只记录日志，不显示提示
        if (
          !this.currentStoreInventory ||
          this.currentStoreInventory.length === 0
        ) {
          console.log(`门店 ${store.id} 没有库存数据`);
        }
      } catch (error) {
        console.error("选择门店失败:", error);
        this.showToast(`加载门店库存失败: ${error.message}`, "error");
      }
    },

    // 返回上一页，并设置刷新标记
    goBack() {
      console.log("返回上一页");

      // 检查是否是从门店详情页跳转过来的
      const fromStoreDetail = uni.getStorageSync("fromStoreDetail");
      if (fromStoreDetail) {
        console.log("从门店详情页跳转过来，返回时设置刷新标记");

        // 清除标记
        uni.removeStorageSync("fromStoreDetail");

        // 设置门店详情页需要刷新的标记
        uni.setStorageSync("storeDetailNeedRefresh", "true");
      }

      // 返回上一页
      uni.navigateBack();
    },

    // 重置所有商品的输入框值为0
    resetAllInputValues() {
      console.log("重置所有商品的输入框值为0");
      if (this.productList && this.productList.length > 0) {
        this.productList.forEach((product) => {
          if (product.newQuantity !== 0) {
            console.log(
              `重置商品 ${product.id} (${
                product.name || "未命名商品"
              }) 的输入框值为0，原值: ${product.newQuantity}`
            );
            product.newQuantity = 0;
          }
        });
      }
    },

    // 刷新库存 - 简化版，只刷新库存数据
    async refreshInventory(force = false) {
      if (!this.selectedStore) {
        this.showToast("请先选择门店", "warning");
        return;
      }

      // 如果已经在刷新中，不重复请求
      if (this.refreshing) {
        this.showToast("正在刷新数据，请稍后", "info");
        return;
      }

      console.log("刷新库存数据");

      // 设置刷新状态
      this.refreshing = true;

      try {
        // 显示加载提示
        this.loadingText = "刷新中...";
        this.loading = true;

        console.log(`刷新门店 ${this.selectedStore.id} 库存数据`);

        // 导入简化版库存API
        const InventoryAPI = (await import("@/api/inventorySimple")).default;

        // 调用API获取库存数据
        console.log(`正在获取门店 ${this.selectedStore.id} 的库存数据...`);
        const response = await InventoryAPI.getStoreInventory(
          this.selectedStore.id,
          true
        );

        // 处理响应数据
        if (response && response.data) {
          // 确保数据是数组
          let inventoryData = [];

          if (Array.isArray(response.data)) {
            inventoryData = response.data;
          } else if (
            response.data &&
            typeof response.data === "object" &&
            Array.isArray(response.data.items)
          ) {
            // 有些API可能会返回包装对象，其中items字段包含实际数据
            inventoryData = response.data.items;
          } else if (typeof response.data === "object") {
            // 尝试将其他格式转换为数组
            try {
              // 如果是对象，尝试提取值
              const values = Object.values(response.data);
              if (values.length > 0 && Array.isArray(values[0])) {
                inventoryData = values[0];
              }
            } catch (conversionError) {
              console.error("转换响应数据失败:", conversionError);
            }
          }

          console.log(`处理后的库存数据共 ${inventoryData.length} 条记录`);

          // 更新库存数据
          this.$store.commit("storeInventory/SET_STORE_INVENTORY", {
            storeId: this.selectedStore.id,
            inventory: inventoryData,
          });

          // 重置所有商品的输入框值为0
          this.resetAllInputValues();

          // 显示成功提示
          if (!this._fromStoreDetail || force) {
            this.showToast("库存数据已刷新", "success");
          }

          // 强制更新视图
          this.$forceUpdate();
        } else {
          console.warn("获取库存数据成功，但数据为空");
        }
      } catch (error) {
        console.error("刷新库存失败:", error);
        this.showToast("刷新库存失败", "error");
      } finally {
        // 重置加载状态
        this.loading = false;

        // 重置刷新状态
        this.refreshing = false;
      }
    },

    // 增加库存
    async addInventory(product) {
      if (!this.selectedStore) {
        this.showToast("请先选择门店", "error");
        return;
      }

      if (
        !this.isValidQuantity(product.newQuantity) ||
        product.newQuantity === 0
      ) {
        this.showToast("请输入有效的数量", "error");
        return;
      }

      // 如果已经在加载中，不重复请求
      if (this.loading) {
        this.showToast("正在处理请求，请稍后再试", "warning");
        return;
      }

      try {
        // 获取当前库存数量
        const currentQuantity = this.getProductInventory(product.id, false); // 不自动创建初始记录

        // 确保数量是有效的数字
        const inputQuantity = parseInt(product.newQuantity) || 0;
        if (inputQuantity <= 0) {
          this.showToast("请输入大于0的数量", "warning");
          return;
        }

        // 计算最终数量
        const finalQuantity = currentQuantity + inputQuantity;

        // 保存最终数量，用于后续操作
        product._finalQuantity = finalQuantity;
        product._operationType = "add";

        // 检查商品是否已有库存记录
        const isNewInventory =
          currentQuantity === 0 && !this.hasInventoryRecord(product.id);
        console.log(`商品 ${product.id} 是否有库存记录: ${!isNewInventory}`);
        console.log(product);

        // 显示确认对话框
        uni.showModal({
          title: isNewInventory ? "确认新增库存" : "确认增加库存",
          content: isNewInventory
            ? `确定要为商品 "${
                product.code || "未命名商品"
              }" 新增库存 ${finalQuantity} 个吗？`
            : `确定要为商品 "${
                product.code || "未命名商品"
              }" 增加 ${inputQuantity} 个库存吗？(${currentQuantity} → ${finalQuantity})`,
          confirmText: "确定",
          cancelText: "取消",
          success: async (res) => {
            if (res.confirm) {
              await this.doUpdateInventory(product);
            } else {
              // 用户取消，重置输入框
              product.newQuantity = 0;
            }
          },
        });
      } catch (error) {
        console.error("增加库存操作失败:", error);
        this.showToast("操作失败: " + (error.message || "未知错误"), "error");
      }
    },

    // 减少库存
    async subtractInventory(product) {

      if (!this.selectedStore) {
        this.showToast("请先选择门店", "error");
        return;
      }

      if (
        !this.isValidQuantity(product.newQuantity) ||
        product.newQuantity === 0
      ) {

        this.showToast("请输入有效的数量", "error");
        return;
      }

      // 如果已经在加载中，不重复请求
      if (this.loading) {
        this.showToast("正在处理请求，请稍后再试", "warning");
        return;
      }

      try {
        // 获取当前库存数量
        const currentQuantity = this.getProductInventory(product.id, false); // 不自动创建初始记录

        // 确保数量是有效的数字
        const inputQuantity = parseInt(product.newQuantity) || 0;
        if (inputQuantity <= 0) {
          this.showToast("请输入大于0的数量", "warning");
          return;
        }

        // 检查是否有足够的库存可减
        if (currentQuantity < inputQuantity) {
          this.showToast(
            `库存不足，当前库存: ${currentQuantity}，无法减少: ${inputQuantity}`,
            "warning"
          );
          return;
        }

        // 计算最终数量
        const finalQuantity = Math.max(0, currentQuantity - inputQuantity);

        // 如果最终数量没有变化，提示用户并返回
        if (finalQuantity === currentQuantity) {
          this.showToast("库存数量未发生变化", "info");
          product.newQuantity = 0; // 重置输入框
          return;
        }

        // 保存最终数量，用于后续操作
        product._finalQuantity = finalQuantity;
        product._operationType = "subtract";

        // 显示确认对话框
        uni.showModal({
          title: "确认减少库存",
          content: `确定要从商品 "${
            product.name || "未命名商品"
          }" 减少 ${inputQuantity} 个库存吗？(${currentQuantity} → ${finalQuantity})`,
          confirmText: "确定",
          cancelText: "取消",
          success: async (res) => {
            if (res.confirm) {
              await this.doUpdateInventory(product);
            } else {
              // 用户取消，重置输入框
              product.newQuantity = 0;
            }
          },
        });
      } catch (error) {
        console.error("减少库存操作失败:", error);
        this.showToast("操作失败: " + (error.message || "未知错误"), "error");
      }
    },

    // 设置库存
    async setInventory(product) {

      if (!this.selectedStore) {
        this.showToast("请先选择门店", "error");
        return;
      }

      if (!this.isValidQuantity(product.newQuantity)) {
        this.showToast("请输入有效的数量", "error");
        return;
      }

      // 如果已经在加载中，不重复请求
      if (this.loading) {
        this.showToast("正在处理请求，请稍后再试", "warning");
        return;
      }

      try {
        // 获取当前库存数量
        const currentQuantity = this.getProductInventory(product.id, false); // 不自动创建初始记录

        // 确保数量是有效的数字
        const finalQuantity = parseInt(product.newQuantity) || 0;
        if (finalQuantity < 0) {
          this.showToast("请输入有效的数量", "warning");
          return;
        }

        // 如果最终数量没有变化，提示用户并返回
        if (finalQuantity === currentQuantity) {
          this.showToast("库存数量未发生变化", "info");
          product.newQuantity = 0; // 重置输入框
          return;
        }

        // 保存最终数量，用于后续操作
        product._finalQuantity = finalQuantity;
        product._operationType = "set";

        // 检查商品是否已有库存记录
        const isNewInventory =
          currentQuantity === 0 && !this.hasInventoryRecord(product.id);

        // 显示确认对话框
        uni.showModal({
          title: isNewInventory ? "确认新增库存" : "确认设置库存",
          content: isNewInventory
            ? `确定要为商品 "${
                product.name || "未命名商品"
              }" 新增库存 ${finalQuantity} 个吗？`
            : `确定要将商品 "${
                product.name || "未命名商品"
              }" 的库存设置为 ${finalQuantity} 个吗？(当前: ${currentQuantity})`,
          confirmText: "确定",
          cancelText: "取消",
          success: async (res) => {
            if (res.confirm) {
              await this.doUpdateInventory(product);
            } else {
              // 用户取消，重置输入框
              product.newQuantity = 0;
            }
          },
        });
      } catch (error) {
        console.error("设置库存操作失败:", error);
        this.showToast("操作失败: " + (error.message || "未知错误"), "error");
      }
    },

    // 执行库存更新
    async doUpdateInventory(product) {
      // 保存最终数量，以便在失败时恢复
      const finalQuantity = product._finalQuantity;
      const originalInputQuantity = product.newQuantity;
      const operationType = product._operationType;

      // 重置新数量和临时变量，避免重复提交
      product.newQuantity = 0;
      delete product._finalQuantity;
      delete product._operationType;

      // 显示加载提示
      this.loadingText = "更新库存中...";
      this.loading = true;

      try {
        // 检查商品是否已有库存记录
        const currentQuantity = this.getProductInventory(product.id, false); // 不自动创建初始记录
        const isNewInventory =
          currentQuantity === 0 && !this.hasInventoryRecord(product.id);

        // 确保最终数量是有效的数字
        const safeQuantity = parseInt(finalQuantity);

        if (isNaN(safeQuantity) || safeQuantity < 0) {
          throw new Error(`无效的库存数量: ${finalQuantity}`);
        }

        // 记录详细日志
        if (isNewInventory) {
          console.log(
            `开始新增商品 ${product.id} (${
              product.name || "未命名商品"
            }) 库存，新增数量: ${safeQuantity}`
          );
        } else {
          console.log(
            `开始更新商品 ${product.id} (${
              product.name || "未命名商品"
            }) 库存，操作: ${operationType}, 原数量: ${currentQuantity}, 变化量: ${originalInputQuantity}, 最终数量: ${safeQuantity}`
          );
        }

        // 导入简化版库存API
        const InventoryAPI = (await import("@/api/inventorySimple")).default;

        // 调用API更新库存，使用最终计算的数量
        console.log(
          `发送库存更新请求: 门店ID=${this.selectedStore.id}, 商品ID=${
            product.id
          } (${product.name || "未命名商品"}), 数量=${safeQuantity}`
        );
        const response = await InventoryAPI.updateInventory(
          this.selectedStore.id,
          product.id,
          safeQuantity
        );

        console.log("更新库存响应:", response);

        // 重置加载状态
        this.loading = false;

        // 等待一小段时间，确保数据库更新完成
        await new Promise((resolve) => setTimeout(resolve, 300));

        // 手动更新本地库存数据
        try {
          // 导入简化版库存API
          const InventoryAPI = (await import("@/api/inventorySimple")).default;

          // 获取最新的库存数据
          const response = await InventoryAPI.getStoreInventory(this.selectedStore.id, true);

          if (response && response.data) {
            // 确保数据是数组
            let inventoryData = [];

            if (Array.isArray(response.data)) {
              inventoryData = response.data;
            } else if (response.data && typeof response.data === "object" && Array.isArray(response.data.items)) {
              inventoryData = response.data.items;
            } else if (typeof response.data === "object") {
              try {
                const values = Object.values(response.data);
                if (values.length > 0 && Array.isArray(values[0])) {
                  inventoryData = values[0];
                }
              } catch (error) {
                console.error("转换响应数据失败:", error);
              }
            }

            // 更新库存数据
            this.$store.commit("storeInventory/SET_STORE_INVENTORY", {
              storeId: this.selectedStore.id,
              inventory: inventoryData,
            });
          }
        } catch (error) {
          console.error("获取最新库存数据失败:", error);
        }

        // 显示成功提示
        let successMsg = "";
        if (isNewInventory) {
          successMsg = `成功新增商品库存: ${finalQuantity}`;
        } else {
          if (operationType === "set") {
            successMsg = `成功设置商品库存: ${finalQuantity}`;
          } else if (operationType === "add") {
            successMsg = `成功增加商品库存: +${originalInputQuantity}`;
          } else if (operationType === "subtract") {
            successMsg = `成功减少商品库存: -${originalInputQuantity}`;
          }
        }
        this.showToast(successMsg, "success");

        // 设置门店详情页需要刷新的标记（如果是从门店详情页跳转过来的）
        const fromStoreDetail = uni.getStorageSync("fromStoreDetail");
        if (fromStoreDetail) {
          console.log("从门店详情页跳转过来，设置刷新标记");
          uni.setStorageSync("storeDetailNeedRefresh", "true");
        }

        // 播放成功音效
        this.playSound("success");

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        // 重置加载状态
        this.loading = false;

        console.error("更新商品库存失败:", error);

        // 显示错误提示
        this.showToast(`更新商品库存失败: ${error.message || error}`, "error");

        // 播放错误音效
        this.playSound("error");
      }
    },

    // 播放音效
    playSound(type) {
      try {
        const innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.autoplay = true;

        if (type === "success") {
          innerAudioContext.src = "/static/sounds/success.mp3";
        } else if (type === "error") {
          innerAudioContext.src = "/static/sounds/error.mp3";
        }

        // 移除音频播放失败的错误处理
      } catch (error) {
        // 忽略音频播放失败的错误
      }
    },

    // 设置加载状态重置定时器
    setupLoadingResetTimer() {
      // 清除可能存在的旧定时器
      if (this.loadingResetTimer) {
        clearInterval(this.loadingResetTimer);
      }

      // 每10秒检查一次加载状态，如果加载状态持续超过20秒，强制重置
      this.loadingResetTimer = setInterval(() => {
        if (this.loading) {
          console.log("检测到加载状态持续时间较长，更新提示文本");

          // 更新加载提示文本
          const texts = [
            "加载中，请稍候...",
            "正在处理数据，可能需要一点时间...",
            "数据量较大，请耐心等待...",
            "仍在加载，请稍候...",
            "加载时间较长，您可以刷新页面重试",
          ];

          // 随机选择一个提示文本
          this.loadingText = texts[Math.floor(Math.random() * texts.length)];

          // 检查加载状态是否持续超过30秒
          const loadingDuration = this._loadingStartTime
            ? Date.now() - this._loadingStartTime
            : 0;
          if (loadingDuration > 30000) {
            // 30秒
            console.warn("加载状态持续超过30秒，强制重置");
            this.resetLoadingState();

            // 显示提示
            this.showToast("加载超时，已自动重置", "warning");
          }
        } else {
          // 重置加载开始时间
          this._loadingStartTime = null;

          // 重置加载提示文本
          this.loadingText = "加载中...";
        }
      }, 10000); // 每10秒检查一次
    },

    // 重置加载状态
    resetLoadingState() {
      console.log("手动重置加载状态");

      // 重置 Vuex 中的加载状态
      this.$store.commit("storeInventory/SET_LOADING", false);

      // 重置加载提示文本
      this.loadingText = "加载中...";

      // 重置加载开始时间
      this._loadingStartTime = null;

      // 重置页面上的其他加载状态
      this.refreshing = false;
      this.batchUpdating = false;

      // 确保加载状态被重置
      this.loading = false;
    },

    // 检查商品是否有库存记录（即使数量为0）
    hasInventoryRecord(productId) {
      try {
        // 参数验证
        if (!productId) {
          return false;
        }

        // 确保 productId 是数字
        const numericProductId =
          typeof productId === "string" ? parseInt(productId) : productId;

        if (isNaN(numericProductId)) {
          return false;
        }

        if (
          !this.selectedStore ||
          !this.currentStoreInventory ||
          this.currentStoreInventory.length === 0
        ) {
          return false;
        }

        // 查找商品库存记录 - 使用数字比较
        const inventoryItem = this.currentStoreInventory.find((item) => {
          const itemProductId =
            typeof item.productId === "string"
              ? parseInt(item.productId)
              : item.productId;
          return itemProductId === numericProductId;
        });

        return inventoryItem !== undefined;
      } catch (error) {
        console.error("检查商品库存记录失败:", error);
        return false;
      }
    },

    // 获取商品库存数量
    // createInitialRecord: 参数保留但不再使用，为了保持兼容性
    getProductInventory(productId, _createInitialRecord = true) {
      // 添加下划线前缀表示不使用
      try {
        // 参数验证
        if (!productId) {
          console.warn("获取库存数量失败: 缺少商品ID");
          return 0;
        }

        // 确保 productId 是数字
        const numericProductId =
          typeof productId === "string" ? parseInt(productId) : productId;

        if (isNaN(numericProductId)) {
          console.warn(`获取库存数量失败: 无效的商品ID "${productId}"`);
          return 0;
        }

        if (!this.selectedStore) {
          console.warn("获取库存数量失败: 未选择门店");
          return 0;
        }

        // 如果门店没有库存数据，尝试初始化空库存
        if (
          !this.currentStoreInventory ||
          this.currentStoreInventory.length === 0
        ) {
          // 只在第一次警告，避免重复日志
          if (!this._warnedNoInventory) {
            console.warn(
              `门店 ${this.selectedStore.id} 没有库存数据，将使用默认值 0`
            );
            this._warnedNoInventory = true;

            // 不自动刷新，只显示警告
            console.log("库存数据为空，需要手动刷新");
          }
          return 0;
        }

        // 打印当前库存数据，用于调试
        console.log(
          `当前库存数据: ${this.currentStoreInventory.length} 条记录`
        );

        // 查找商品库存 - 使用数字比较
        const inventoryItem = this.currentStoreInventory.find((item) => {
          // 确保比较的是数字类型
          const itemProductId =
            typeof item.productId === "string"
              ? parseInt(item.productId)
              : item.productId;
          const match = itemProductId === numericProductId;
          if (match) {
            console.log(`找到商品 ${numericProductId} 的库存记录:`, item);
          }
          return match;
        });

        if (inventoryItem) {
          // 确保返回数字类型
          let quantity = 0;

          if (
            inventoryItem.quantity !== undefined &&
            inventoryItem.quantity !== null
          ) {
            quantity =
              typeof inventoryItem.quantity === "string"
                ? parseInt(inventoryItem.quantity)
                : inventoryItem.quantity;

            console.log(`商品 ${numericProductId} 的库存数量: ${quantity}`);
          } else {
            console.warn(`商品 ${numericProductId} 的库存数量未定义或为null`);
          }

          // 确保是有效的数字
          const numericQuantity = isNaN(quantity) ? 0 : quantity;

          // 不再设置输入框的初始值，让它始终保持为0
          // 移除设置初始数量的代码，确保刷新后输入框从0开始

          return numericQuantity;
        } else {
          // 只在第一次警告，避免重复日志
          if (!this._warnedProducts) {
            this._warnedProducts = {};
          }

          if (!this._warnedProducts[numericProductId]) {
            console.warn(
              `门店 ${this.selectedStore.id} 中找不到商品 ${numericProductId} 的库存，将使用默认值 0`
            );
            this._warnedProducts[numericProductId] = true;

            // 不再创建初始库存记录
            console.log(`商品 ${numericProductId} 没有库存记录，使用默认值 0`);
          }

          return 0;
        }
      } catch (error) {
        console.error("获取商品库存数量失败:", error);
        return 0;
      }
    },

    // 获取分类名称
    getCategoryName(categoryId) {
      try {
        if (!categoryId || !this.categoryList || !this.categoryList.length) {
          return "未分类";
        }

        // 确保 categoryId 是数字类型
        const numericCategoryId =
          typeof categoryId === "string" ? parseInt(categoryId) : categoryId;

        if (isNaN(numericCategoryId)) {
          console.warn(`无效的分类ID: ${categoryId}`);
          return "未分类";
        }

        const category = this.categoryList.find((item) => {
          const itemId =
            typeof item.id === "string" ? parseInt(item.id) : item.id;
          return itemId === numericCategoryId;
        });

        if (category && category.name) {
          return category.name;
        } else {
          console.warn(`找不到分类: ${categoryId}`);
          return "未分类";
        }
      } catch (error) {
        console.error("获取分类名称失败:", error, "分类ID:", categoryId);
        return "未分类";
      }
    },

    // 更新分类商品数量缓存
    updateCategoryProductCounts() {
      try {
        console.log("更新分类商品数量缓存");

        if (!this.allProducts || this.allProducts.length === 0) {
          console.log("没有商品数据，无法更新分类商品数量");
          return;
        }

        // 重置缓存
        this.categoryProductCounts = {};

        // 遍历所有商品，统计各分类的商品数量
        this.allProducts.forEach((product) => {
          try {
            if (product.categoryId) {
              // 确保分类ID是数字类型
              const categoryId =
                typeof product.categoryId === "string"
                  ? parseInt(product.categoryId)
                  : product.categoryId;

              if (!isNaN(categoryId)) {
                // 增加该分类的商品计数
                this.categoryProductCounts[categoryId] =
                  (this.categoryProductCounts[categoryId] || 0) + 1;
              }
            }
          } catch (error) {
            console.error("统计商品分类时出错:", error, product);
          }
        });

        console.log("分类商品数量统计结果:", this.categoryProductCounts);
      } catch (error) {
        console.error("更新分类商品数量缓存失败:", error);
      }
    },

    // 获取分类下的商品数量
    getCategoryProductCount(categoryId) {
      try {
        if (!categoryId) {
          return 0;
        }

        // 确保 categoryId 是数字类型
        const numericCategoryId =
          typeof categoryId === "string" ? parseInt(categoryId) : categoryId;

        // 优先使用缓存的分类商品数量
        if (
          this.categoryProductCounts &&
          this.categoryProductCounts[numericCategoryId] !== undefined
        ) {
          return this.categoryProductCounts[numericCategoryId];
        }

        // 如果缓存中没有，则从 allProducts 中计算
        if (this.allProducts && this.allProducts.length > 0) {
          // 计算该分类下的商品数量
          const count = this.allProducts.filter((product) => {
            const productCategoryId =
              typeof product.categoryId === "string"
                ? parseInt(product.categoryId)
                : product.categoryId;
            return productCategoryId === numericCategoryId;
          }).length;

          // 更新缓存
          this.categoryProductCounts[numericCategoryId] = count;

          return count;
        }

        // 如果 allProducts 为空，则从 productList 中计算（兼容旧逻辑）
        if (this.productList && this.productList.length > 0) {
          // 计算该分类下的商品数量
          const count = this.productList.filter((product) => {
            const productCategoryId =
              typeof product.categoryId === "string"
                ? parseInt(product.categoryId)
                : product.categoryId;
            return productCategoryId === numericCategoryId;
          }).length;

          return count;
        }

        return 0;
      } catch (error) {
        console.error("获取分类商品数量失败:", error, "分类ID:", categoryId);
        return 0;
      }
    },

    // 获取库存状态样式类
    getInventoryStatusClass(productId) {
      const quantity = this.getProductInventory(productId, true);
      if (quantity === 0) {
        return "inventory-zero";
      } else if (quantity > 0 && quantity < 10) {
        return "inventory-low";
      } else {
        return "inventory-normal";
      }
    },

    // 格式化金额
    formatAmount(amount) {
      if (amount === undefined || amount === null) {
        return "0.00";
      }
      return parseFloat(amount).toFixed(2);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr || dateStr === "未知") {
        return "未知";
      }

      try {
        // 处理特殊情况：如果是带有时区信息的日期字符串
        console.log("原始日期字符串:", dateStr);

        // 如果是带有时区信息的日期字符串，先提取日期部分
        if (typeof dateStr === "string" && dateStr.includes("T")) {
          dateStr = dateStr.split("T")[0];
          console.log("提取日期部分后:", dateStr);
        }

        // 尝试将日期字符串转换为 Date 对象
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          console.log("日期无效:", dateStr);
          return dateStr; // 如果日期无效，返回原始字符串
        }

        // 格式化为 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error("格式化日期失败:", error, dateStr);
        return dateStr;
      }
    },

    // 下拉刷新
    onRefresh() {
      console.log("触发下拉刷新");
      this.refreshing = true;
      this.loadData(true);
    },

    // 加载更多
    loadMore() {
      // 防止重复触发
      if (this.loading || this.noMore) {
        console.log("正在加载或没有更多数据，不触发加载更多");
        return;
      }

      // 使用节流防抖，避免频繁触发
      if (this.loadMoreTimer) {
        clearTimeout(this.loadMoreTimer);
      }

      this.loadMoreTimer = setTimeout(() => {
        console.log("触发加载更多，当前页码:", this.page);
        this.loadData();
      }, 300); // 300毫秒的防抖时间
    },

    // 加载数据
    async loadData(refresh = false) {
      console.log("调用 loadData，刷新:", refresh, "当前页码:", this.page);

      if (refresh) {
        console.log("刷新数据，重置页码为1");
        this.page = 1;
        this.noMore = false;
      }

      // 防止重复加载
      if (this.loading) {
        console.log("正在加载中，不重复加载");
        return;
      }

      // 如果没有更多数据且不是刷新，则不加载
      if (this.noMore && !refresh) {
        console.log("没有更多数据且不是刷新，不加载");
        return;
      }

      // 如果没有选择门店，不加载数据
      if (!this.selectedStore) {
        console.log("未选择门店，不加载数据");
        return;
      }

      this.loading = true;
      console.log("开始加载数据，页码:", this.page);

      try {
        // 刷新库存数据
        await this.refreshInventory(true);

        // 如果是刷新，重置页码
        if (refresh) {
          this.page = 1;
        } else {
          // 如果不是刷新，增加页码
          this.page++;
        }

        this.loading = false;

        if (this.refreshing) {
          this.refreshing = false;
        }

        return true;
      } catch (error) {
        console.error("加载数据失败:", error);
        this.loading = false;

        if (this.refreshing) {
          this.refreshing = false;
        }

        this.showToast("加载失败，请重试", "error");
        return false;
      }
    },

    // 验证数量是否有效
    isValidQuantity(quantity) {
      return quantity !== null && quantity !== undefined && quantity >= 0;
    },

    // 分类切换
    onCategoryChange(index) {
      console.log("收到分类切换事件，参数:", index);

      // 处理不同类型的索引参数
      let actualIndex;

      if (typeof index === "object" && index !== null) {
        // 如果是对象，尝试获取 index 属性
        if (index.index !== undefined) {
          actualIndex = index.index;
          console.log("从对象中提取索引:", actualIndex);
        } else {
          console.error("无法从对象中提取索引:", index);
          actualIndex = 0;
        }
      } else {
        // 如果是基本类型，尝试转换为数字
        actualIndex = parseInt(index, 10);
      }

      // 检查是否为有效数字
      if (isNaN(actualIndex)) {
        console.error("无效的分类索引:", index);
        this.categoryIndex = 0; // 默认为第一个分类
      } else {
        this.categoryIndex = actualIndex;
      }

      console.log("切换分类，处理后索引:", this.categoryIndex);

      // 使用 nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        // 重置搜索关键词
        this.keyword = "";
      });
    },

    // 点击分类标签
    async onTabClick(index) {
      console.log("点击分类标签，索引:", index);

      // 获取当前选中的分类标签
      const selectedTab = this.categoryTabs[index];
      console.log("选中的分类标签:", selectedTab);

      this.categoryIndex = index;

      // 打印选中的分类ID
      const categoryId = selectedTab ? selectedTab.id : null;
      console.log(
        `选中分类: ${
          selectedTab ? selectedTab.name : "全部"
        }, ID: ${categoryId}`
      );

      // 显示加载提示
      this.loading = true;
      this.loadingText = "加载商品数据...";

      try {
        // 根据选中的分类ID重新加载商品
        await this.loadProducts(categoryId);

        // 打印所有商品的分类ID，帮助调试
        console.log("所有商品的分类ID:");
        this.productList.forEach((product) => {
          const productCategoryId =
            typeof product.categoryId === "string"
              ? parseInt(product.categoryId)
              : product.categoryId;
          console.log(
            `- 商品 ${product.id} (${
              product.name || product.code || "未命名"
            }): 分类ID=${productCategoryId}, 原始类型=${typeof product.categoryId}`
          );
        });

        // 使用 nextTick 确保 DOM 已更新
        this.$nextTick(() => {
          // 重置搜索关键词
          this.keyword = "";

          // 重置所有商品的输入框值为0
          this.resetAllInputValues();

          // 打印过滤后的商品数量
          console.log(`过滤后的商品数量: ${this.filteredProducts.length}`);

          // // 打印过滤后的商品
          // console.log('过滤后的商品:');
          // this.filteredProducts.forEach(product => {
          //   console.log(`- ${product.id} (${product.name || product.code || '未命名'}): 分类ID=${product.categoryId}`);
          // });
        });
      } catch (error) {
        console.error("加载分类商品失败:", error);
        this.showToast("加载分类商品失败", "error");
      } finally {
        // 隐藏加载提示
        this.loading = false;
      }
    },

    // 搜索
    async onSearch() {
      console.log("搜索关键词:", this.keyword);

      if (!this.selectedStore) {
        this.showToast("请先选择门店", "warning");
        return;
      }

      try {
        this.loading = true;
        this.loadingText = "搜索中...";

        // 导入产品API
        const ProductAPI = (await import("@/api/product")).default;

        // 构建请求参数
        const params = {
          pageSize: 1000,
        };

        // 只有当关键词不为空时才添加到请求参数
        if (this.keyword && this.keyword.trim() !== "") {
          params.keyword = this.keyword.trim();
        }

        // 如果当前有选中的分类，添加到请求参数
        if (this.categoryIndex > 0 && this.categoryTabs[this.categoryIndex]) {
          params.categoryId = this.categoryTabs[this.categoryIndex].id;
        }

        console.log("搜索参数:", params);

        // 调用API获取商品列表
        const response = await ProductAPI.getProducts(params);
        console.log("搜索结果:", response);

        // 处理响应数据
        if (
          response &&
          response.code === 0 &&
          response.data &&
          response.data.batteries
        ) {
          // 更新商品列表
          this.productList = response.data.batteries.map((product) => ({
            ...product,
            newQuantity: 0,
          }));

          console.log("搜索结果商品数量:", this.productList.length);

          // 更新分类商品数量统计
          this.updateCategoryProductCounts();
        } else {
          console.warn("搜索返回数据格式不正确:", response);
          this.showToast("搜索失败，请重试", "error");
        }
      } catch (error) {
        console.error("搜索失败:", error);
        this.showToast("搜索失败: " + (error.message || "未知错误"), "error");
      } finally {
        this.loading = false;
      }
    },

    // 显示消息提示
    showToast(message, type = "default") {
      this.$toast.show(message, type);
    },
  },

  watch: {
    // 监听加载状态变化
    loading(newVal, oldVal) {
      if (newVal && !oldVal) {
        // 加载状态从 false 变为 true，记录开始时间
        this._loadingStartTime = Date.now();
        console.log("加载开始，记录时间:", this._loadingStartTime);
      } else if (!newVal && oldVal) {
        // 加载状态从 true 变为 false，重置开始时间
        console.log("加载结束，重置时间");
        this._loadingStartTime = null;
      }
    },
  },

  // 页面加载时接收参数
  onLoad(options) {
    console.log("库存管理页面加载，参数:", options);

    // 检查用户是否已登录
    const token = uni.getStorageSync("token");
    if (!token) {
      console.log("用户未登录，不加载库存管理页面");
      this.showToast("请先登录", "warning");

      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: "/pages/login/login",
        });
      }, 1000);

      return;
    }

    // 检查是否是从门店详情页跳转过来的
    const fromStoreDetail = uni.getStorageSync("fromStoreDetail");
    if (fromStoreDetail) {
      this._fromStoreDetail = true;
      // 清除标记
      uni.removeStorageSync("fromStoreDetail");
    }

    // 保存门店ID参数
    if (options && options.storeId) {
      this.initialStoreId = options.storeId;
      console.log("接收到门店ID参数:", this.initialStoreId);

      // 如果没有从存储中检测到标记，也设置为从门店详情页跳转
      if (!this._fromStoreDetail) {
        this._fromStoreDetail = true;
      }
    }

    // 初始化数据
    this.initData();

    // 确保 categoryIndex 是数字类型
    this.categoryIndex = 0;

    // 设置加载状态重置定时器，防止加载状态卡住
    this.setupLoadingResetTimer();

    // 如果有初始门店ID，延迟一段时间后检查是否成功选择了门店
    if (this.initialStoreId) {
      // 设置一个标记，表示已经在 initData 中尝试过选择门店
      this._hasTriedSelectStore = false;

      setTimeout(() => {
        // 如果没有成功选择门店，且之前没有尝试过，尝试手动选择
        if (
          (!this.selectedStore ||
            this.selectedStore.id !== parseInt(this.initialStoreId)) &&
          !this._hasTriedSelectStore
        ) {
          console.log(
            "自动选择门店失败，尝试手动选择门店:",
            this.initialStoreId
          );
          this._hasTriedSelectStore = true;

          // 查找门店
          const targetStore = this.storeList.find(
            (store) =>
              store.id === this.initialStoreId ||
              store.id === parseInt(this.initialStoreId)
          );

          if (targetStore) {
            // 根据来源选择不同的方法
            if (this._fromStoreDetail) {
              // 如果是从门店详情页跳转过来的，使用不初始化库存的方法
              console.log("从门店详情页跳转，使用不初始化库存的方法");
              // 使用不会触发 API 调用的方法
              this.selectStoreWithoutInit(targetStore);

              // 不自动刷新库存数据
              console.log("已选择门店，不自动刷新库存数据");
            } else {
              // 否则使用普通的选择门店方法
              console.log("正常选择门店，可能会初始化库存");
              this.selectStore(targetStore);
            }
          } else {
            console.warn("找不到指定的门店:", this.initialStoreId);
            this.showToast("找不到指定的门店", "warning");
          }
        }
      }, 1500); // 延迟1.5秒，确保 initData 完成
    }
  },

  // 页面隐藏时设置刷新标记
  onHide() {
    console.log("库存管理页面隐藏");

    // 检查是否是从门店详情页跳转过来的
    const fromStoreDetail = uni.getStorageSync("fromStoreDetail");
    if (fromStoreDetail || this._fromStoreDetail) {
      console.log("从门店详情页跳转过来，设置刷新标记");

      // 设置门店详情页需要刷新的标记
      uni.setStorageSync("storeDetailNeedRefresh", "true");
    }
  },

  // 页面卸载时清理资源
  onUnload() {
    console.log("库存管理页面卸载");

    // 检查是否是从门店详情页跳转过来的
    const fromStoreDetail = uni.getStorageSync("fromStoreDetail");
    if (fromStoreDetail || this._fromStoreDetail) {
      console.log("从门店详情页跳转过来，设置刷新标记");

      // 设置门店详情页需要刷新的标记
      uni.setStorageSync("storeDetailNeedRefresh", "true");
    }

    // 清除定时器
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
      this.autoRefreshTimer = null;
    }

    if (this.loadingResetTimer) {
      clearInterval(this.loadingResetTimer);
      this.loadingResetTimer = null;
    }

    // 强制重置加载状态
    this.resetLoadingState();
  },

  mounted() {
    // 延迟标记 tabs 组件准备好，避免初始化问题
    setTimeout(() => {
      try {
        // 确保分类数据已加载
        if (this.categoryList && this.categoryList.length > 0) {
          this.tabsReady = true;
          console.log("Tabs 组件已准备好，分类数量:", this.categoryList.length);
        } else {
          console.warn("分类数据尚未加载完成，延迟初始化 Tabs");
          // 再次延迟尝试
          setTimeout(() => {
            this.tabsReady = true;
            console.log("Tabs 组件已准备好（第二次尝试）");
          }, 1000);
        }
      } catch (error) {
        console.error("初始化 Tabs 组件失败:", error);
        // 无论如何都设置为 true，使用备用 UI
        this.tabsReady = true;
      }
    }, 800);

    // 不设置自动刷新定时器
    console.log("不设置自动刷新定时器，需要手动刷新库存数据");

    // 监听页面显示事件，但不自动刷新数据
    uni.$on("refreshInventory", () => {
      console.log("收到刷新库存事件，但不自动刷新，需要手动刷新库存数据");
    });
  },

  beforeDestroy() {
    // 清除自动刷新定时器
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
    }

    // 移除事件监听
    uni.$off("refreshInventory");
  },
};
</script>

<style lang="scss" scoped>
.inventory-container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;

  /* 通用卡片样式 */
  .card {
    background-color: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 20rpx;
    overflow: hidden;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    align-items: center;
    height: 90rpx;
    margin-bottom: 20rpx;

    .header-back {
      width: 70rpx;
      height: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      border-radius: 50%;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }

    .header-title {
      flex: 1;
      text-align: center;
      font-size: 34rpx;
      font-weight: bold;
      color: #333333;
    }
  }

  /* 返回按钮 */
  .back-nav {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 16rpx 24rpx;
    border-radius: 40rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    width: fit-content;

    text {
      margin-left: 10rpx;
      font-size: 26rpx;
      color: #333333;
    }
  }

  /* 门店选择卡片 */
  .store-card {
    padding: 20rpx;

    .card-header {
      display: flex;
      align-items: baseline;
      margin-bottom: 16rpx;

      .card-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333333;
      }

      .card-subtitle {
        font-size: 24rpx;
        color: #999999;
        margin-left: 16rpx;
      }
    }

    .store-scroll {
      width: 100%;
      white-space: nowrap;

      &::-webkit-scrollbar {
        display: none;
      }

      .store-list {
        display: inline-flex;
        padding: 10rpx 0;

        .store-item {
          display: flex;
          align-items: center;
          padding: 16rpx 24rpx;
          margin-right: 20rpx;
          background-color: #f0f2f5;
          border-radius: 40rpx;
          transition: all 0.3s ease;

          &.active {
            background-color: #3c9cff;
            box-shadow: 0 4rpx 12rpx rgba(60, 156, 255, 0.3);

            text {
              color: #ffffff;
            }
          }

          text {
            margin-left: 10rpx;
            font-size: 26rpx;
            color: #333333;
          }
        }
      }
    }
  }

  /* 门店信息卡片 */
  .info-card {
    padding: 20rpx;

    .store-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .store-info {
        .store-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          display: block;
          margin-bottom: 6rpx;
        }

        .store-subtitle {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }

    .stats-panel {
      display: flex;
      align-items: center;
      justify-content: space-around;
      background-color: #f8faff;
      border-radius: 12rpx;
      padding: 20rpx;

      .stat-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        .stat-value {
          font-size: 36rpx;
          font-weight: bold;
          color: #3c9cff;
          margin-bottom: 8rpx;
        }

        .stat-label {
          font-size: 24rpx;
          color: #666666;
        }
      }

      .stat-divider {
        width: 2rpx;
        height: 50rpx;
        background-color: #e0e0e0;
      }
    }
  }

  /* 搜索和分类卡片 */
  .filter-card {
    padding: 20rpx;

    .search-box {
      margin-bottom: 20rpx;
    }

    .category-scroll {
      width: 100%;
      white-space: nowrap;

      &::-webkit-scrollbar {
        display: none;
      }

      .category-list {
        display: inline-flex;
        padding: 10rpx 0;

        .category-item {
          padding: 14rpx 24rpx;
          margin-right: 16rpx;
          background-color: #f0f2f5;
          border-radius: 40rpx;
          font-size: 26rpx;
          color: #666666;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;

          &.active {
            background-color: #3c9cff;
            color: #ffffff;
            box-shadow: 0 4rpx 12rpx rgba(60, 156, 255, 0.3);

            .category-badge {
              background-color: #ffffff;
              color: #3c9cff;
            }
          }

          .category-badge {
            font-size: 22rpx;
            background-color: #3c9cff;
            color: #ffffff;
            border-radius: 20rpx;
            padding: 2rpx 10rpx;
            margin-left: 10rpx;
            min-width: 32rpx;
            text-align: center;
            display: inline-block;
          }
        }
      }
    }
  }

  /* 商品列表 */
  .product-scroll {
    background-color: #f5f7fa;
    padding: 0 4rpx;

    .empty-tip {
      padding: 80rpx 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .product-card {
      padding: 24rpx;
      margin-bottom: 20rpx;

      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16rpx;
        border-bottom: 1px solid #f0f0f0;

        .product-title {
          flex: 1;

          .product-name {
            font-size: 30rpx;
            font-weight: bold;
            color: #333333;
            display: block;
            margin-bottom: 6rpx;
          }

          .product-code {
            font-size: 24rpx;
            color: #999999;
          }
        }

        .stock-tag {
          padding: 6rpx 16rpx;
          border-radius: 30rpx;
          font-size: 24rpx;
          font-weight: bold;

          &.inventory-zero {
            background-color: #fff0f0;
            color: #ff4d4f;
          }

          &.inventory-low {
            background-color: #fff7e6;
            color: #fa8c16;
          }

          &.inventory-normal {
            background-color: #f6ffed;
            color: #52c41a;
          }
        }
      }

      .product-info {
        padding: 16rpx 0;

        .info-row {
          display: flex;
          margin-bottom: 12rpx;

          .info-item {
            flex: 1;

            .info-label {
              font-size: 24rpx;
              color: #999999;
              display: block;
              margin-bottom: 6rpx;
            }

            .info-value {
              font-size: 26rpx;
              color: #333333;
              font-weight: 500;

              &.price {
                color: #ff4d4f;
                font-weight: bold;
              }
            }
          }
        }
      }

      .inventory-panel {
        padding-top: 16rpx;
        border-top: 1px solid #f0f0f0;

        .inventory-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16rpx;

          .inventory-title {
            font-size: 26rpx;
            font-weight: bold;
            color: #333333;
          }

          .inventory-count {
            font-size: 24rpx;

            &.inventory-zero {
              color: #ff4d4f;
            }

            &.inventory-low {
              color: #fa8c16;
            }

            &.inventory-normal {
              color: #52c41a;
            }
          }
        }

        .inventory-controls {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #f8faff;
          border-radius: 12rpx;
          padding: 16rpx;

          .quantity-group {
            display: flex;
            align-items: center;

            .control-btn {
              margin: 0 10rpx;
            }

            .number-input {
              margin: 0 10rpx;
            }
          }

          .set-btn {
            margin-left: 10rpx;
          }
        }
      }
    }

    .loading-more,
    .no-more {
      text-align: center;
      padding: 20rpx 0;

      text {
        font-size: 24rpx;
        color: #999999;
        margin-left: 10rpx;
      }
    }
  }

  /* 空状态 */
  .empty-state {
    padding: 80rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
