using System.ComponentModel.DataAnnotations;

namespace BatteryApi.DTOs.Product;

/// <summary>
/// 创建商品规格请求
/// </summary>
public class CreateProductSpecRequest
{
    /// <summary>
    /// 规格名称
    /// </summary>
    [Required(ErrorMessage = "规格名称不能为空")]
    [StringLength(100, ErrorMessage = "规格名称长度不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 电压 (V)
    /// </summary>
    [Range(0, 1000, ErrorMessage = "电压必须在0到1000V之间")]
    public decimal Voltage { get; set; } = 0;

    /// <summary>
    /// 容量 (Ah)
    /// </summary>
    [Range(0, 10000, ErrorMessage = "容量必须在0到10000Ah之间")]
    public decimal Capacity { get; set; } = 0;

    /// <summary>
    /// 重量 (kg)
    /// </summary>
    [Range(0, 1000, ErrorMessage = "重量必须在0到1000kg之间")]
    public decimal Weight { get; set; } = 0;

    /// <summary>
    /// 尺寸规格
    /// </summary>
    [StringLength(200, ErrorMessage = "尺寸规格长度不能超过200个字符")]
    public string? Dimensions { get; set; }

    /// <summary>
    /// 价格 (元)
    /// </summary>
    [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 描述信息
    /// </summary>
    [StringLength(1000, ErrorMessage = "描述信息长度不能超过1000个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 图片URL
    /// </summary>
    [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// 所属分类ID
    /// </summary>
    [Required(ErrorMessage = "所属分类ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "所属分类ID必须大于0")]
    public int CategoryId { get; set; }
}

/// <summary>
/// 更新商品规格请求
/// </summary>
public class UpdateProductSpecRequest
{
    /// <summary>
    /// 规格名称
    /// </summary>
    [Required(ErrorMessage = "规格名称不能为空")]
    [StringLength(100, ErrorMessage = "规格名称长度不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 电压 (V)
    /// </summary>
    [Range(0, 1000, ErrorMessage = "电压必须在0到1000V之间")]
    public decimal Voltage { get; set; } = 0;

    /// <summary>
    /// 容量 (Ah)
    /// </summary>
    [Range(0, 10000, ErrorMessage = "容量必须在0到10000Ah之间")]
    public decimal Capacity { get; set; } = 0;

    /// <summary>
    /// 重量 (kg)
    /// </summary>
    [Range(0, 1000, ErrorMessage = "重量必须在0到1000kg之间")]
    public decimal Weight { get; set; } = 0;

    /// <summary>
    /// 尺寸规格
    /// </summary>
    [StringLength(200, ErrorMessage = "尺寸规格长度不能超过200个字符")]
    public string? Dimensions { get; set; }

    /// <summary>
    /// 价格 (元)
    /// </summary>
    [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 描述信息
    /// </summary>
    [StringLength(1000, ErrorMessage = "描述信息长度不能超过1000个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 图片URL
    /// </summary>
    [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
    public string? ImageUrl { get; set; }
}
