using BatteryApi.DTOs;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using BatteryApi.Services.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Hosting;
using SqlSugar;
using BCrypt.Net;

namespace BatteryApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")]  // 只允许管理员访问
    public class DatabaseController : ControllerBase
    {
        private readonly ISqlSugarClient _dbClient;
        private readonly ILogger<DatabaseController> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWebHostEnvironment _env;

        public DatabaseController(ISqlSugarClient dbClient, ILogger<DatabaseController> logger, IServiceProvider serviceProvider, IWebHostEnvironment env)
        {
            _dbClient = dbClient;
            _logger = logger;
            _serviceProvider = serviceProvider;
            _env = env;
        }

        [HttpPost("initialize")]
        public async Task<IActionResult> Initialize([FromBody] InitializeDatabaseDto initDto)
        {
            try
            {
                // 只在开发环境中启用
                if (!_env.IsDevelopment())
                {
                    _logger.LogWarning("Database initialization attempted in non-development environment");
                    return StatusCode(403, new { message = "此接口仅在开发环境中可用" });
                }

                _logger.LogInformation("Starting database initialization...");

                // Create database if it doesn't exist
                _dbClient.DbMaintenance.CreateDatabase();

                // Check existing tables
                var tables = _dbClient.DbMaintenance.GetTableInfoList();
                var tableNames = tables.Select(t => t.Name.ToLower()).ToList();

                // 不删除现有表，只检查表是否存在
                _logger.LogInformation("Checking existing database tables...");
                foreach (var tableName in tableNames)
                {
                    _logger.LogInformation($"Found existing table: {tableName}");
                }

                _logger.LogInformation("Database tables checked successfully.");

                // 删除并重新创建 Orders 和 OrderOperations 表
                if (_dbClient.DbMaintenance.IsAnyTable("Orders"))
                {
                    _logger.LogInformation("Dropping Orders table...");
                    _dbClient.DbMaintenance.DropTable("Orders");
                }

                if (_dbClient.DbMaintenance.IsAnyTable("OrderOperations"))
                {
                    _logger.LogInformation("Dropping OrderOperations table...");
                    _dbClient.DbMaintenance.DropTable("OrderOperations");
                }

                // Create tables if they don't exist
                _dbClient.CodeFirst.SetStringDefaultLength(200).InitTables(
                    typeof(User),
                    typeof(UserVerification),
                    typeof(Product),
                    // 移除了 BatteryHistory 和 BatteryUsage 表的创建
                    typeof(BatteryCategory),
                    typeof(Store),
                    typeof(StoreInventory)
                );

                // 单独创建 Order 和 OrderOperation 表
                _logger.LogInformation("Creating Orders and OrderOperations tables...");
                _dbClient.CodeFirst.SetStringDefaultLength(200).InitTables(
                    typeof(Order),
                    typeof(OrderOperation)
                );

                // 单独创建SmsVerificationCode表，避免影响现有表
                if (!_dbClient.DbMaintenance.IsAnyTable(nameof(SmsVerificationCode)))
                {
                    _dbClient.CodeFirst.SetStringDefaultLength(200).InitTables(typeof(SmsVerificationCode));
                    _logger.LogInformation("SmsVerificationCode table created successfully.");
                }

                _logger.LogInformation("New tables created successfully.");

                // 创建或更新管理员用户
                var adminUser = await _dbClient.Queryable<User>().FirstAsync(u => u.Role == "Admin");

                if (adminUser == null)
                {
                    // 创建新管理员用户
                    _logger.LogInformation("Creating new admin user...");
                    adminUser = new User
                    {
                        Username = initDto.AdminUsername,
                        PhoneNumber = initDto.AdminPhone,
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword(initDto.AdminPassword),
                        Role = "Admin",
                        CreatedAt = DateTime.UtcNow,
                        Status = "Active",
                        IsDeleted = false,
                        IsVerified = true,
                        StatusReason = "Initial admin account",
                        StatusChangedAt = DateTime.UtcNow,
                        StatusChangedBy = "System",
                        LastLoginAt = null,
                        RefreshToken = null,
                        RefreshTokenExpiryTime = null,
                        IsOnline = false,
                        LastLoginIp = "System",
                        LastOnlineTime = null
                    };

                    // 首先插入用户并获取自增ID
                    await _dbClient.Insertable(adminUser).ExecuteCommandIdentityIntoEntityAsync();
                    _logger.LogInformation($"Admin user created successfully with ID: {adminUser.Id}");

                    // 使用获取到的用户ID创建验证记录
                    var adminVerification = new UserVerification
                    {
                        UserId = adminUser.Id,  // 使用正确的用户ID
                        RealName = "Administrator",
                        IdCardNumber = "000000000000000000",
                        Status = "Verified",
                        CreatedAt = DateTime.UtcNow,
                        VerifiedAt = DateTime.UtcNow,
                        VerifiedBy = "System",
                        FaceMatchResult = true,
                        IdCardFrontUrl = "admin_front.jpg",
                        IdCardBackUrl = "admin_back.jpg",
                        FaceImageUrl = "admin_face.jpg"
                    };

                    await _dbClient.Insertable(adminVerification).ExecuteCommandAsync();
                    _logger.LogInformation($"Admin verification record created successfully for user ID: {adminUser.Id}");
                }
                else
                {
                    // 更新现有管理员用户的密码
                    _logger.LogInformation($"Updating existing admin user with ID: {adminUser.Id}");
                    adminUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword(initDto.AdminPassword);
                    adminUser.Username = initDto.AdminUsername;
                    adminUser.PhoneNumber = initDto.AdminPhone;
                    adminUser.StatusChangedAt = DateTime.UtcNow;
                    adminUser.StatusChangedBy = "System";

                    await _dbClient.Updateable(adminUser).ExecuteCommandAsync();
                    _logger.LogInformation($"Admin user updated successfully with ID: {adminUser.Id}");
                }

                // 初始化电池分类和模拟数据
                if (initDto.CreateSampleData)
                {
                    _logger.LogInformation("Sample data creation is currently disabled. Please use the battery management interface to add categories and products.");
                }
                else
                {
                    _logger.LogInformation("Sample data creation skipped as per request.");
                }

                return Ok(new { message = "Database initialized successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database initialization error");
                return StatusCode(500, new { message = "Database initialization error", error = ex.Message });
            }
        }

        [HttpPost("reset-admin")]
        [AllowAnonymous] // 允许匿名访问，以便在管理员密码丢失时使用
        public async Task<IActionResult> ResetAdminPassword([FromBody] ResetAdminDto resetDto)
        {
            try
            {
                // 只在开发环境中启用
                if (!_env.IsDevelopment())
                {
                    _logger.LogWarning("Admin password reset attempted in non-development environment");
                    return StatusCode(403, new { message = "此接口仅在开发环境中可用" });
                }

                _logger.LogInformation("Starting admin password reset...");

                // 检查是否存在管理员用户
                var adminUser = await _dbClient.Queryable<User>().FirstAsync(u => u.Role == "Admin");

                if (adminUser == null)
                {
                    // 创建新管理员用户
                    _logger.LogInformation("No admin user found. Creating new admin user...");
                    adminUser = new User
                    {
                        Username = resetDto.AdminUsername,
                        PhoneNumber = "***********",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword(resetDto.NewPassword),
                        Role = "Admin",
                        CreatedAt = DateTime.UtcNow,
                        Status = "Active",
                        IsDeleted = false,
                        IsVerified = true,
                        StatusReason = "Reset admin account",
                        StatusChangedAt = DateTime.UtcNow,
                        StatusChangedBy = "System",
                        LastLoginAt = null,
                        RefreshToken = null,
                        RefreshTokenExpiryTime = null,
                        IsOnline = false,
                        LastLoginIp = "System",
                        LastOnlineTime = null
                    };

                    // 插入用户并获取自增ID
                    await _dbClient.Insertable(adminUser).ExecuteCommandIdentityIntoEntityAsync();
                    _logger.LogInformation($"Admin user created successfully with ID: {adminUser.Id}");

                    // 创建验证记录
                    var adminVerification = new UserVerification
                    {
                        UserId = adminUser.Id,
                        RealName = "Administrator",
                        IdCardNumber = "000000000000000000",
                        Status = "Verified",
                        CreatedAt = DateTime.UtcNow,
                        VerifiedAt = DateTime.UtcNow,
                        VerifiedBy = "System",
                        FaceMatchResult = true,
                        IdCardFrontUrl = "admin_front.jpg",
                        IdCardBackUrl = "admin_back.jpg",
                        FaceImageUrl = "admin_face.jpg"
                    };

                    await _dbClient.Insertable(adminVerification).ExecuteCommandAsync();
                    _logger.LogInformation($"Admin verification record created successfully for user ID: {adminUser.Id}");
                }
                else
                {
                    // 更新现有管理员用户的密码
                    _logger.LogInformation($"Updating existing admin user with ID: {adminUser.Id}");
                    adminUser.PasswordHash = BCrypt.Net.BCrypt.HashPassword(resetDto.NewPassword);
                    adminUser.Username = resetDto.AdminUsername;
                    adminUser.StatusChangedAt = DateTime.UtcNow;
                    adminUser.StatusChangedBy = "System";

                    await _dbClient.Updateable(adminUser).ExecuteCommandAsync();
                    _logger.LogInformation($"Admin user password reset successfully for ID: {adminUser.Id}");
                }

                return Ok(new { message = "Admin password reset successfully", username = adminUser.Username });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting admin password");
                return StatusCode(500, new { message = "Error resetting admin password", error = ex.Message });
            }
        }

        /// <summary>
        /// 执行数据库更新（使用SqlSugar）
        /// </summary>
        [HttpPost("update-schema")]
        [AllowAnonymous] // 临时允许匿名访问以修复数据库结构
        public async Task<IActionResult> UpdateDatabaseSchema()
        {
            try
            {
                // 只在开发环境中启用
                if (!_env.IsDevelopment())
                {
                    _logger.LogWarning("Database schema update attempted in non-development environment");
                    return StatusCode(403, new { message = "此接口仅在开发环境中可用" });
                }

                _logger.LogInformation("开始执行数据库更新（使用SqlSugar）...");

                using (var scope = _serviceProvider.CreateScope())
                {
                    var migrationService = scope.ServiceProvider.GetRequiredService<DatabaseMigrationService>();

                    // 检查数据库连接
                    var connectionOk = await migrationService.CheckDatabaseConnectionAsync();
                    if (!connectionOk)
                    {
                        return StatusCode(500, new { message = "数据库连接失败" });
                    }

                    // 执行所有迁移
                    await migrationService.ExecuteAllMigrationsAsync();

                    _logger.LogInformation("数据库更新完成");
                    return Ok(new { message = "数据库更新成功" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行数据库更新时发生错误");
                return StatusCode(500, new { message = "执行数据库更新失败: " + ex.Message });
            }
        }

        [HttpPost("create-test-orders")]
        public async Task<IActionResult> CreateTestOrders()
        {
            try
            {
                // 只在开发环境中启用
                if (!_env.IsDevelopment())
                {
                    _logger.LogWarning("Test orders creation attempted in non-development environment");
                    return StatusCode(403, new { message = "此接口仅在开发环境中可用" });
                }

                _logger.LogInformation("Starting test orders creation...");

                using (var scope = _serviceProvider.CreateScope())
                {
                    var orderService = scope.ServiceProvider.GetRequiredService<IOrderService>();

                    // 获取所有订单
                    var orders = await orderService.GetAllOrdersAsync(null);
                    _logger.LogInformation($"Current orders count: {orders.Count}");

                    // 如果已经有订单，询问是否要继续
                    if (orders.Count > 0)
                    {
                        _logger.LogInformation("Orders already exist. Creating additional test orders...");
                    }

                    // 创建测试订单
                    // 这里我们调用之前在OrderService中实现的CreateTestOrdersAsync方法
                    // 但由于该方法是私有的，我们需要在OrderService中添加一个公共方法来调用它
                    await orderService.CreateTestOrdersAsync();

                    // 获取更新后的订单数量
                    orders = await orderService.GetAllOrdersAsync(null);
                    _logger.LogInformation($"Updated orders count: {orders.Count}");

                    return Ok(new { message = "Test orders created successfully", count = orders.Count });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating test orders");
                return StatusCode(500, new { message = "Error creating test orders", error = ex.Message });
            }
        }
    }
}
