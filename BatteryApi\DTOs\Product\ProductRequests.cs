using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace BatteryApi.DTOs.Product;

/// <summary>
/// 商品图片请求
/// </summary>
public class ProductImageRequest
{
    /// <summary>
    /// 图片ID（更新时使用）
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// 临时文件路径
    /// </summary>
    [JsonPropertyName("tempPath")]
    public string TempPath { get; set; } = string.Empty;

    /// <summary>
    /// 图片URL
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// 相对路径
    /// </summary>
    [JsonPropertyName("relativePath")]
    public string RelativePath { get; set; } = string.Empty;

    /// <summary>
    /// 图片名称
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    [JsonPropertyName("size")]
    public long Size { get; set; }

    /// <summary>
    /// 上传状态
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = "pending";

    /// <summary>
    /// 是否为主图
    /// </summary>
    [JsonPropertyName("isMain")]
    public bool IsMain { get; set; }

    /// <summary>
    /// 是否为新图片
    /// </summary>
    [JsonPropertyName("isNew")]
    public bool IsNew { get; set; } = true;

    /// <summary>
    /// 图片类型
    /// </summary>
    [JsonPropertyName("imageType")]
    public string ImageType { get; set; } = "main";
}

/// <summary>
/// 商品服务请求
/// </summary>
public class ProductServiceRequest
{
    /// <summary>
    /// 服务ID（更新时使用）
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// 服务名称
    /// </summary>
    [JsonPropertyName("name")]
    [Required(ErrorMessage = "服务名称不能为空")]
    [StringLength(100, ErrorMessage = "服务名称长度不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 服务描述
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(500, ErrorMessage = "服务描述长度不能超过500个字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [JsonPropertyName("isEnabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("sort")]
    public int Sort { get; set; } = 0;
}

/// <summary>
/// 商品安装费用请求
/// </summary>
public class ProductInstallationFeeRequest
{
    /// <summary>
    /// 费用ID（更新时使用）
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// 费用名称
    /// </summary>
    [JsonPropertyName("name")]
    [Required(ErrorMessage = "费用名称不能为空")]
    [StringLength(100, ErrorMessage = "费用名称长度不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 费用金额
    /// </summary>
    [JsonPropertyName("price")]
    [Range(0, 1000000, ErrorMessage = "费用金额必须在0到1000000元之间")]
    public decimal Price { get; set; }

    /// <summary>
    /// 费用描述
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(500, ErrorMessage = "费用描述长度不能超过500个字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [JsonPropertyName("isEnabled")]
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("sort")]
    public int Sort { get; set; } = 0;
}

/// <summary>
/// 创建商品请求
/// </summary>
public class CreateProductRequest
{
    /// <summary>
    /// 商品名称
    /// </summary>
    [JsonPropertyName("name")]
    [Required(ErrorMessage = "商品名称不能为空")]
    [StringLength(200, ErrorMessage = "商品名称长度不能超过200个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规格/型号
    /// </summary>
    [JsonPropertyName("spec")]
    [Required(ErrorMessage = "规格不能为空")]
    [StringLength(100, ErrorMessage = "规格长度不能超过100个字符")]
    public string Spec { get; set; } = string.Empty;

    /// <summary>
    /// 商品状态 (Available, Rented, Sold, Maintenance)
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = "Available";

    /// <summary>
    /// 销售价格 (元)
    /// </summary>
    [JsonPropertyName("price")]
    [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 租赁价格 (元/天)
    /// </summary>
    [JsonPropertyName("rentPrice")]
    [Range(0, 10000, ErrorMessage = "租赁价格必须在0到10000元之间")]
    public decimal RentPrice { get; set; } = 0;

    /// <summary>
    /// 生产日期
    /// </summary>
    [JsonPropertyName("manufactureDate")]
    public DateTime? ManufactureDate { get; set; }

    /// <summary>
    /// 使用寿命（月）
    /// </summary>
    [JsonPropertyName("lifespan")]
    [Range(1, 1200, ErrorMessage = "使用寿命必须在1到1200个月之间")]
    public int Lifespan { get; set; } = 36;

    /// <summary>
    /// 商品描述
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 分类ID
    /// </summary>
    [JsonPropertyName("categoryId")]
    [Required(ErrorMessage = "分类ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "分类ID必须大于0")]
    public int CategoryId { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    [JsonPropertyName("categoryCode")]
    public string CategoryCode { get; set; } = string.Empty;

    /// <summary>
    /// 分类名称
    /// </summary>
    [JsonPropertyName("categoryName")]
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 电压
    /// </summary>
    [JsonPropertyName("voltage")]
    [StringLength(20, ErrorMessage = "电压长度不能超过20个字符")]
    public string Voltage { get; set; } = string.Empty;

    /// <summary>
    /// 容量
    /// </summary>
    [JsonPropertyName("capacity")]
    [StringLength(20, ErrorMessage = "容量长度不能超过20个字符")]
    public string Capacity { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    [JsonPropertyName("cycleCount")]
    [StringLength(20, ErrorMessage = "循环次数长度不能超过20个字符")]
    public string CycleCount { get; set; } = string.Empty;

    /// <summary>
    /// 充电时间
    /// </summary>
    [JsonPropertyName("chargeTime")]
    [StringLength(20, ErrorMessage = "充电时间长度不能超过20个字符")]
    public string ChargeTime { get; set; } = string.Empty;

    /// <summary>
    /// 关联的电池ID（用于BMS信息）
    /// </summary>
    [JsonPropertyName("batteryId")]
    public int? BatteryId { get; set; }

    /// <summary>
    /// 图片列表（包含主图、详情图、规格图等）- JSON方式
    /// </summary>
    [JsonPropertyName("mainImages")]
    public List<ProductImageRequest>? MainImages { get; set; }

    /// <summary>
    /// 图片文件列表 - FormData方式（mainImagesFiles 字段直接接收 IFormFile）
    /// </summary>
    public List<IFormFile>? MainImagesFiles { get; set; }

    /// <summary>
    /// 服务列表
    /// </summary>
    [JsonPropertyName("services")]
    public List<ProductServiceRequest> Services { get; set; } = new List<ProductServiceRequest>();

    /// <summary>
    /// 安装费用列表
    /// </summary>
    [JsonPropertyName("installationFees")]
    public List<ProductInstallationFeeRequest> InstallationFees { get; set; } = new List<ProductInstallationFeeRequest>();

    /// <summary>
    /// 已删除的图片ID列表
    /// </summary>
    [JsonPropertyName("removedImages")]
    public List<long> RemovedImages { get; set; } = new List<long>();
}

/// <summary>
/// 更新商品请求
/// </summary>
public class UpdateProductRequest
{
    /// <summary>
    /// 商品名称
    /// </summary>
    [JsonPropertyName("name")]
    [Required(ErrorMessage = "商品名称不能为空")]
    [StringLength(200, ErrorMessage = "商品名称长度不能超过200个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规格/型号
    /// </summary>
    [JsonPropertyName("spec")]
    [Required(ErrorMessage = "规格不能为空")]
    [StringLength(100, ErrorMessage = "规格长度不能超过100个字符")]
    public string Spec { get; set; } = string.Empty;

    /// <summary>
    /// 商品状态 (Available, Rented, Sold, Maintenance)
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = "Available";

    /// <summary>
    /// 销售价格 (元)
    /// </summary>
    [JsonPropertyName("price")]
    [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
    public decimal Price { get; set; } = 0;

    /// <summary>
    /// 租赁价格 (元/天)
    /// </summary>
    [JsonPropertyName("rentPrice")]
    [Range(0, 10000, ErrorMessage = "租赁价格必须在0到10000元之间")]
    public decimal RentPrice { get; set; } = 0;

    /// <summary>
    /// 生产日期
    /// </summary>
    [JsonPropertyName("manufactureDate")]
    public DateTime? ManufactureDate { get; set; }

    /// <summary>
    /// 使用寿命（月）
    /// </summary>
    [JsonPropertyName("lifespan")]
    [Range(1, 1200, ErrorMessage = "使用寿命必须在1到1200个月之间")]
    public int Lifespan { get; set; } = 36;

    /// <summary>
    /// 商品描述
    /// </summary>
    [JsonPropertyName("description")]
    [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 分类ID
    /// </summary>
    [JsonPropertyName("categoryId")]
    [Required(ErrorMessage = "分类ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "分类ID必须大于0")]
    public int CategoryId { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    [JsonPropertyName("categoryCode")]
    public string CategoryCode { get; set; } = string.Empty;

    /// <summary>
    /// 分类名称
    /// </summary>
    [JsonPropertyName("categoryName")]
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 电压
    /// </summary>
    [JsonPropertyName("voltage")]
    [StringLength(20, ErrorMessage = "电压长度不能超过20个字符")]
    public string Voltage { get; set; } = string.Empty;

    /// <summary>
    /// 容量
    /// </summary>
    [JsonPropertyName("capacity")]
    [StringLength(20, ErrorMessage = "容量长度不能超过20个字符")]
    public string Capacity { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    [JsonPropertyName("cycleCount")]
    [StringLength(20, ErrorMessage = "循环次数长度不能超过20个字符")]
    public string CycleCount { get; set; } = string.Empty;

    /// <summary>
    /// 充电时间
    /// </summary>
    [JsonPropertyName("chargeTime")]
    [StringLength(20, ErrorMessage = "充电时间长度不能超过20个字符")]
    public string ChargeTime { get; set; } = string.Empty;

    /// <summary>
    /// 关联的电池ID（用于BMS信息）
    /// </summary>
    [JsonPropertyName("batteryId")]
    public int? BatteryId { get; set; }

    /// <summary>
    /// 图片列表（包含主图、详情图、规格图等）- JSON方式
    /// </summary>
    [JsonPropertyName("mainImages")]
    public List<ProductImageRequest>? MainImages { get; set; }

    /// <summary>
    /// 图片文件列表 - FormData方式（mainImagesFiles 字段直接接收 IFormFile）
    /// </summary>
    public List<IFormFile>? MainImagesFiles { get; set; }

    /// <summary>
    /// 服务列表
    /// </summary>
    [JsonPropertyName("services")]
    public List<ProductServiceRequest> Services { get; set; } = new List<ProductServiceRequest>();

    /// <summary>
    /// 安装费用列表
    /// </summary>
    [JsonPropertyName("installationFees")]
    public List<ProductInstallationFeeRequest> InstallationFees { get; set; } = new List<ProductInstallationFeeRequest>();

    /// <summary>
    /// 已删除的图片ID列表
    /// </summary>
    [JsonPropertyName("removedImages")]
    public List<long> RemovedImages { get; set; } = new List<long>();
}
