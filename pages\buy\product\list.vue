<template>
  <view class="index-container">
    <!-- Banner区域 -->
    <view class="banner-section">
      <swiper class="banner-swiper" circular autoplay interval="3000" duration="500">
        <swiper-item v-for="(item, index) in bannerList" :key="index">
          <image class="banner-image" :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
    </view>

    <!-- 公告区域 -->
    <view class="notice-section">
      <view class="notice-icon">
        <text class="iconfont icon-notice"></text>
      </view>
      <swiper class="notice-swiper" vertical autoplay circular interval="3000" duration="500">
        <swiper-item v-for="(item, index) in noticeList" :key="index">
          <view class="notice-item">{{ item.content }}</view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷入口区域 -->
    <view class="quick-links">
      <view class="quick-link-item" @tap="navigateTo('/pages/store/nearby')">
        <view class="quick-link-icon">
          <text class="iconfont icon-location">⛪</text>
        </view>
        <text class="quick-link-text">附近门店</text>
      </view>
      <view class="quick-link-item" @tap="navigateTo('/pages/buy/battery/category')">
        <view class="quick-link-icon">
          <text class="iconfont icon-category">💪</text>
        </view>
        <text class="quick-link-text">电池分类</text>
      </view>
      <view class="quick-link-item" @tap="navigateTo('/pages/buy/order/list')">
        <view class="quick-link-icon">
          <text class="iconfont icon-order">📜</text>
        </view>
        <text class="quick-link-text">我的订单</text>
      </view>
      <view class="quick-link-item" @tap="navigateTo('/pages/buy/about/about')">
        <view class="quick-link-icon">
          <text class="iconfont icon-about">❓</text>
        </view>
        <text class="quick-link-text">关于我们</text>
      </view>
    </view>

    <!-- 主体内容区域 -->
    <view class="content-container">
      <!-- 分类选项卡导航（垂直方向） -->
      <view class="tab-category-vertical">
        <view
          class="tab-item-vertical"
          v-for="(item, index) in categories"
          :key="index"
          :class="{active: currentCategoryId === item.id}"
          @tap="switchCategory(item.id)"
        >
          <text class="tab-name">{{ item.name }}</text>
        </view>
      </view>

      <!-- 商品列表区域 -->
      <view class="battery-list-container">
        <view v-if="categoryLoading" class="loading-skeleton">
          <view class="skeleton-item" v-for="i in 3" :key="i"></view>
        </view>
        <view v-else-if="!filteredBatteries.length" class="empty-state">
          <text class="empty-text">当前分类暂无商品</text>
        </view>
        <view
          class="battery-item"
          v-for="battery in filteredBatteries"
          :key="battery.id"
          v-else
        >
          <view class="battery-image-container">
            <view class="battery-status" :class="getBatteryStatusClass(battery.status)">
              {{ getBatteryStatusText(battery.status) }}
            </view>
            <image class="battery-image" :src="getBatteryImage(battery)" mode="aspectFit"></image>
          </view>
          <view class="battery-info">
            <view>
              <view class="battery-spec">{{ battery.model || battery.spec }}</view>
              <view class="battery-desc">{{ battery.description || '优质电池，质量保证' }}</view>
            </view>
            <view class="battery-bottom">
              <view class="battery-price">
                <text class="rent-price">租金: {{ $utils.formatAmount(battery.rentPrice) }}/天</text>
                <text class="buy-price">售价: {{ $utils.formatAmount(battery.price) }}</text>
              </view>
              <view class="battery-action" @click="navigateTo(`/pages/buy/battery/detail?id=${battery.id}`)">选购</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex";

export default {
  data() {
    return {
      bannerList: [
        { image: "/static/banner/banner1.svg" },
        { image: "/static/banner/banner2.svg" },
        { image: "/static/banner/banner3.svg" },
      ],
      noticeList: [
        { content: "电池租售管理系统正式上线，欢迎使用！" },
        { content: "门店库存实时更新，请随时关注！" },
      ],
      categories: [
        { id: 'recommend', name: '推荐', code: 'recommend' },
      ],
      currentCategoryId: 'recommend', // 默认选中推荐分类
      location: {
        latitude: 0,
        longitude: 0,
      },
      loading: false,
      pageNumber: 1,
      pageSize: 10,
      totalItems: 0,
      totalPages: 0,
    };
  },
  computed: {
    ...mapState("battery", [
      "batteryList",
      "categoryList",
      "categoryBatteries",
      "categoryLoading",
      "categoryBatteryTotal",
      "categoryBatteryPage",
      "categoryBatteryTotalPages"
    ]),
    ...mapState("store", ["storeList"]),
    ...mapState("user", ["userRole"]),
    ...mapGetters("battery", ["availableBatteries", "getCategoryByCode"]),

    // 根据当前选中的分类筛选电池列表
    filteredBatteries() {
      console.log('当前类别电池列表:', this.categoryBatteries);
      console.log('当前电池列表:', this.batteryList);

      // 如果当前类别是推荐，则显示所有电池
      if (this.currentCategoryId === 'recommend') {
        return Array.isArray(this.batteryList) ? this.batteryList : [];
      }

      // 否则显示当前类别的电池
      return Array.isArray(this.categoryBatteries) ? this.categoryBatteries : [];
    }
  },
  mounted() {
    this.initPage();
  },
  onPullDownRefresh() {
    this.loadData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    ...mapActions("battery", [
      "getBatteryList",
      "getBatteryCategories",
      "getBatteriesByCategory"
    ]),
    ...mapActions("store", ["getStoreList"]),

    // 初始化页面
    async initPage() {
      this.getLocation();
      await this.loadData();
    },

    // 切换分类
    async switchCategory(categoryId) {
      if (this.currentCategoryId === categoryId) return;

      this.currentCategoryId = categoryId;
      console.log('切换到分类:', categoryId);

      // 加载该分类下的电池列表
      await this.loadCategoryBatteries();
    },

    // 获取电池状态样式类
    getBatteryStatusClass(status) {
      const statusMap = {
        1: 'status-available',  // 可用
        2: 'status-rented',     // 已租
        3: 'status-sold',       // 已售
        4: 'status-maintenance', // 维修中
        'Available': 'status-available',
        'InUse': 'status-rented',
        'Maintenance': 'status-maintenance',
        'Retired': 'status-sold'
      };
      return statusMap[status] || 'status-available';
    },

    // 获取电池状态文本
    getBatteryStatusText(status) {
      const statusMap = {
        1: '可用',
        2: '已租',
        3: '已售',
        4: '维修中',
        'Available': '可用',
        'InUse': '已租',
        'Maintenance': '维修中',
        'Retired': '已售'
      };
      return statusMap[status] || '可用';
    },

    // 根据电池类型获取对应的图片
    getBatteryImage(battery) {
      // 从序列号或型号中提取类型
      const serialNumber = battery.serialNumber?.toLowerCase() || '';
      const model = battery.model?.toLowerCase() || '';

      // 根据序列号前缀或型号关键词判断类型
      if (serialNumber.startsWith('lithium-') || model.includes('锂电池') || model.includes('18650') || model.includes('21700')) {
        return '/static/battery-lithium.svg';
      } else if (serialNumber.startsWith('ev-') || model.includes('电动车')) {
        return '/static/battery-ev.svg';
      } else if (serialNumber.startsWith('tricycle-') || model.includes('三轮车')) {
        return '/static/battery-tricycle.svg';
      } else if (serialNumber.startsWith('charger-') || model.includes('充电器')) {
        return '/static/charger.svg';
      } else if (serialNumber.startsWith('bms-') || model.includes('保护板')) {
        return '/static/protection-board.svg';
      } else if (serialNumber.startsWith('cell-') || model.includes('电芯')) {
        return '/static/battery-cell.svg';
      } else if (serialNumber.startsWith('box-') || model.includes('电池盒')) {
        return '/static/battery-box.svg';
      } else if (serialNumber.startsWith('acc-') || model.includes('辅材')) {
        return '/static/accessories.svg';
      }

      // 默认图片
      return '/static/battery-default.svg';
    },

    // 加载数据
    async loadData() {
      try {
        // 加载电池类别
        await this.getBatteryCategories();

        console.log('类别列表加载完成', this.categoryList);

        // 更新类别列表
        if (this.categoryList && this.categoryList.length > 0) {
          // 添加推荐类别
          const hasRecommend = this.categoryList.some(c => c.code === 'recommend');
          const categories = [...this.categoryList];

          if (!hasRecommend) {
            categories.unshift({ id: 'recommend', name: '推荐', code: 'recommend' });
          }

          this.categories = categories;
          console.log('更新后的类别列表', this.categories);
        } else {
          console.warn('类别列表为空');
        }

        // 加载当前类别的电池列表
        await this.loadCategoryBatteries();

        // 加载门店列表
        await this.getStoreList();
      } catch (error) {
        console.error("加载数据失败", error);
        uni.showToast({
          title: "加载数据失败，请重试",
          icon: "none",
        });
      }
    },

    // 加载分类电池列表
    async loadCategoryBatteries() {
      try {
        const params = {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize
        };

        if (this.currentCategoryId === 'recommend') {
          // 推荐类别，直接获取所有电池
          await this.getBatteryList(params);
        } else {
          // 获取指定类别的电池
          const category = this.categories.find(c => c.id === this.currentCategoryId);
          const categoryCode = category ? category.code : '';
          console.log('加载类别电池列表，类别编码:', categoryCode, '参数:', params);
          // 将参数作为数组传递
          await this.getBatteriesByCategory([categoryCode, params]);
        }
      } catch (error) {
        console.error("加载分类电池列表失败", error);
        uni.showToast({
          title: "加载电池列表失败，请重试",
          icon: "none",
        });
      }
    },

    // 获取当前位置
    getLocation() {
      uni.getLocation({
        type: "gcj02",
        success: (res) => {
          this.location.latitude = res.latitude;
          this.location.longitude = res.longitude;
        },
        fail: () => {
          uni.showToast({
            title: "获取位置信息失败，将展示所有门店",
            icon: "none",
          });
        },
      });
    },

    // 计算两点之间的距离（km）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371;
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(this.deg2rad(lat1)) *
          Math.cos(this.deg2rad(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI / 180);
    },

    // 格式化距离显示
    formatDistance(distance) {
      if (distance === null) return "距离未知";
      if (distance < 1) return `${(distance * 1000).toFixed(0)}m`;
      return `${distance.toFixed(1)}km`;
    },

    // 页面导航
    navigateTo(url) {
      if (url.includes("/pages/buy/order/list")) {
        // 如果当前已经在首页，直接触发切换事件
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];

        if (currentPage && currentPage.route && currentPage.route.includes('/pages/buy/index')) {
          console.log('当前已在首页，直接触发切换事件');
          // 直接触发切换事件
          uni.$emit('switchToOrderTab', { tabIndex: 0 }); // 默认切换到第一个标签页
        } else {
          // 存储要切换的标签页索引
          uni.setStorageSync('orderTabIndex', 0); // 默认切换到第一个标签页

          // 切换到首页，然后通过首页的onShow方法切换到订单标签页
          uni.switchTab({
            url: '/pages/buy/index',
            success: function() {
              console.log('切换到首页成功');
            },
            fail: function(err) {
              console.error('切换失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none',
                duration: 1500
              });
            }
          });
        }
      } else {
        uni.navigateTo({ url });
      }
    },

    // 打开地图导航
    openLocation(store) {
      uni.openLocation({
        latitude: store.latitude,
        longitude: store.longitude,
        name: store.name,
        address: store.address,
        scale: 18,
      });
    },

    // 查看更多电池
    viewMoreBatteries() {
      uni.navigateTo({
        url: "/pages/buy/battery/batterylist",
        fail: () => {

        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.uni-scroll-view {
  width: auto!important;
}
.index-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f8f8;
  overflow: hidden;
}

.banner-section {
  width: 100%;
  height: 350rpx;

  .banner-swiper {
    width: 100%;
    height: 100%;
  }

  .banner-image {
    width: 100%;
    height: 100%;
  }
}

.notice-section {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 2rpx;

  .notice-icon {
    margin-right: 20rpx;

    .iconfont {
      font-size: 40rpx;
      color: #ff6b00;
    }
  }

  .notice-swiper {
    flex: 1;
    height: 60rpx;
  }

  .notice-item {
    font-size: 28rpx;
    color: #666;
    line-height: 60rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.quick-links {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .quick-link-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;
  }

  .quick-link-icon {
    width: 80rpx;
    height: 80rpx;
    background-color: #f5f7fa;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10rpx;

    .iconfont {
      font-size: 40rpx;
      color: #2979ff;
    }
  }

  .quick-link-text {
    font-size: 24rpx;
    color: #333;
  }
}

.content-container {
  display: flex;
  position: relative;
  overflow: auto;
  flex: 1;
}

.tab-category-vertical {
  background-color: #fff;
  width: 180rpx;
  box-shadow: 2rpx 0 10rpx rgba(0, 0, 0, 0.05);
  height: 100%;

  .tab-item-vertical {
    padding: 30rpx 0;
    font-size: 28rpx;
    color: #666;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    border-bottom: 1rpx solid #f5f5f5;

    &.active {
      color: #ff6b00;
      font-weight: bold;
      background-color: #fff9f5;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 40rpx;
        background-color: #ff6b00;
        border-radius: 0 3rpx 3rpx 0;
      }
    }
  }
}

.battery-list-container {
  flex: 1;
  background-color: #fff;
  height: auto;
  overflow-y: auto;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  margin-bottom: 2rpx;

  .title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 30rpx;
      background-color: #2979ff;
      border-radius: 3rpx;
    }
  }

  .more {
    font-size: 26rpx;
    color: #999;
    display: flex;
    align-items: center;

    .iconfont {
      font-size: 24rpx;
      margin-left: 5rpx;
    }
  }
}

.battery-list-container .battery-item {
  width: 100%;
  // padding: 0 30rpx 20rpx 0;
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.battery-image-container {
  width: 180rpx;
  height: 180rpx;
  margin-right: 20rpx;
  position: relative;
}

.battery-status {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  z-index: 1;

  &.status-available {
    background-color: #2979ff;
  }
  &.status-rented {
    background-color: #ff9500;
  }
  &.status-sold {
    background-color: #8e8e93;
  }
  &.status-maintenance {
    background-color: #ff3b30;
  }
}

.battery-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.battery-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  width: calc(100% - 200rpx);
  padding-right: 20rpx;
}

.battery-spec {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.battery-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2; /* Standard property for compatibility */
  overflow: hidden;
}

.battery-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.battery-price {
  display: flex;
  flex-direction: column;

  .rent-price,
  .buy-price {
    font-size: 24rpx;
    color: #666;
  }

  .rent-price {
    color: #2979ff;
    margin-bottom: 5rpx;
  }
}

.battery-action {
  background-color: #ff6b00;
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  min-width: 80rpx;
  text-align: center;
  flex-shrink: 0;
  z-index: 2;
}

.nearby-store-section {
  background-color: #fff;
  padding: 20rpx 30rpx;

  .store-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .store-info {
    flex: 1;
  }

  .store-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .store-address {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 450rpx;
  }

  .store-distance {
    font-size: 24rpx;
    color: #999;
  }

  .store-action {
    margin-left: 20rpx;
  }

  .nav-btn {
    width: 120rpx;
    height: 60rpx;
    line-height: 60rpx;
    font-size: 26rpx;
    color: #2979ff;
    background-color: rgba(41, 121, 255, 0.1);
    border: 1rpx solid #2979ff;
    border-radius: 30rpx;
    padding: 0;
  }
}

// 加载骨架屏样式
.loading-skeleton {
  padding: 20rpx;

  .skeleton-item {
    height: 160rpx;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    margin-bottom: 20rpx;
    border-radius: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 空状态样式
.empty-state {
  width: 100%;
  padding: 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
</style>
