using BatteryApi.DTOs.Battery;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using SqlSugar;

namespace BatteryApi.Services.Implementations;

public class BatteryCategoryService : IBatteryCategoryService
{
    private readonly ISqlSugarClient _db;

    public BatteryCategoryService(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<List<BatteryCategoryDto>> GetAllCategoriesAsync()
    {
        var categories = await _db.Queryable<BatteryCategory>()
            .OrderBy(c => c.DisplayOrder)
            .ToListAsync();

        return categories.Select(MapToDto).ToList();
    }

    public async Task<BatteryCategoryDto> GetCategoryByIdAsync(int id)
    {
        var category = await _db.Queryable<BatteryCategory>()
            .FirstAsync(c => c.Id == id);

        if (category == null)
        {
            throw new Exception($"Category with ID {id} not found");
        }

        return MapToDto(category);
    }

    public async Task<BatteryCategoryDto> GetCategoryByCodeAsync(string code)
    {
        var category = await _db.Queryable<BatteryCategory>()
            .FirstAsync(c => c.Code == code);

        if (category == null)
        {
            throw new Exception($"Category with code {code} not found");
        }

        return MapToDto(category);
    }

    public async Task<BatteryCategoryDto> CreateCategoryAsync(CreateBatteryCategoryRequest request)
    {
        var existing = await _db.Queryable<BatteryCategory>()
            .AnyAsync(c => c.Code == request.Code);

        if (existing)
        {
            throw new Exception($"Category with code {request.Code} already exists");
        }

        var category = new BatteryCategory
        {
            Code = request.Code,
            Name = request.Name,
            Description = request.Description,
            DisplayOrder = request.DisplayOrder,
            IsActive = request.IsActive
        };

        await _db.Insertable(category).ExecuteCommandAsync();
        return MapToDto(category);
    }

    public async Task<BatteryCategoryDto> UpdateCategoryAsync(int id, UpdateBatteryCategoryRequest request)
    {
        var category = await _db.Queryable<BatteryCategory>()
            .FirstAsync(c => c.Id == id);

        if (category == null)
        {
            throw new Exception($"Category with ID {id} not found");
        }

        category.Name = request.Name;
        category.Description = request.Description;
        category.DisplayOrder = request.DisplayOrder;
        category.IsActive = request.IsActive;
        category.UpdatedAt = DateTime.UtcNow;

        await _db.Updateable(category).ExecuteCommandAsync();
        return MapToDto(category);
    }

    public async Task<bool> DeleteCategoryAsync(int id)
    {
        var category = await _db.Queryable<BatteryCategory>()
            .FirstAsync(c => c.Id == id);

        if (category == null)
        {
            throw new Exception($"Category with ID {id} not found");
        }

        // Check if there are any products in this category
        var hasProducts = await _db.Queryable<Product>()
            .AnyAsync(p => p.CategoryId == id);

        if (hasProducts)
        {
            throw new Exception("Cannot delete category that has products assigned to it");
        }

        return await _db.Deleteable<BatteryCategory>()
            .Where(c => c.Id == id)
            .ExecuteCommandHasChangeAsync();
    }

    // 移除了 GetBatteriesByCategoryAsync 方法，改为在 BatteryService 中通过查询参数筛选

    private BatteryCategoryDto MapToDto(BatteryCategory category)
    {
        return new BatteryCategoryDto
        {
            Id = category.Id,
            Code = category.Code,
            Name = category.Name,
            Description = category.Description,
            DisplayOrder = category.DisplayOrder,
            IsActive = category.IsActive,
            CreatedAt = category.CreatedAt,
            UpdatedAt = category.UpdatedAt
        };
    }
}
