using BatteryApi.DTOs.Store;
using BatteryApi.Models;
using BatteryApi.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;

namespace BatteryApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InventoryController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<InventoryController> _logger;
        private readonly IStoreService _storeService;

        public InventoryController(ISqlSugarClient db, ILogger<InventoryController> logger, IStoreService storeService)
        {
            _db = db;
            _logger = logger;
            _storeService = storeService;
        }

        /// <summary>
        /// 获取所有库存列表
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetInventories([FromQuery] InventoryQueryParameters parameters)
        {
            try
            {
                _logger.LogInformation("获取库存列表，参数: {@Parameters}", parameters);

                // 查询库存数据
                var query = _db.Queryable<StoreInventory>()
                    .Includes(i => i.Store)
                    .Includes(i => i.Product);

                // 应用筛选条件
                if (parameters.StoreId.HasValue)
                {
                    query = query.Where(i => i.StoreId == parameters.StoreId.Value);
                }

                // 处理关键词搜索，确保参数不为 null
                if (!string.IsNullOrEmpty(parameters.Keyword))
                {
                    query = query.Where(i =>
                        i.Store.Name.Contains(parameters.Keyword) ||
                        i.Product.Name.Contains(parameters.Keyword) ||
                        (i.Product.Manufacturer != null && i.Product.Manufacturer.Contains(parameters.Keyword))
                    );
                }

                // 处理规格 ID 筛选，确保参数不为 null 且有值
                if (parameters.SpecIds.Any())
                {
                    query = query.Where(i => parameters.SpecIds.Contains(i.Product.Id));
                }

                if (parameters.LowStock)
                {
                    query = query.Where(i => i.AvailableQuantity <= 3 && i.AvailableQuantity > 0);
                }

                // 计算总记录数
                var total = await query.CountAsync();

                // 应用分页
                var page = parameters.Page <= 0 ? 1 : parameters.Page;
                var pageSize = parameters.PageSize <= 0 ? 10 : parameters.PageSize;

                var inventories = await query
                    .OrderBy(i => i.StoreId)
                    .OrderBy(i => i.ProductId) // SqlSugar 使用多个 OrderBy 来实现多字段排序
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // 转换为DTO
                var items = inventories.Select(i => new
                {
                    id = i.Id,
                    storeId = i.StoreId,
                    storeName = i.Store?.Name,
                    address = i.Store?.Address,
                    batteryId = i.ProductId,
                    spec = $"{i.Product?.Voltage}V{i.Product?.Capacity}Ah", // 格式化为前端期望的规格格式
                    quantity = i.Quantity,
                    available = i.AvailableQuantity,
                    rented = i.Quantity - i.AvailableQuantity, // 计算租出数量
                    maintenance = 0, // 默认维护中数量为0
                    updateTime = i.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                }).ToList();

                // 返回分页结果
                return Ok(new
                {
                    items,
                    totalItems = total,
                    page,
                    pageSize
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存列表时发生错误");
                return StatusCode(500, new { message = "获取库存列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 门店进货
        /// </summary>
        [HttpPost("stock-in")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> StockIn([FromBody] StockInRequest request)
        {
            try
            {
                _logger.LogInformation("门店进货，参数: {@Request}", request);

                if (request.StoreId <= 0)
                {
                    return BadRequest(new { message = "门店ID无效" });
                }

                if (request.Items == null || !request.Items.Any())
                {
                    return BadRequest(new { message = "进货项目不能为空" });
                }

                // 检查门店是否存在
                var store = await _db.Queryable<Store>().FirstAsync(s => s.Id == request.StoreId);
                if (store == null)
                {
                    return NotFound(new { message = $"门店ID {request.StoreId} 不存在" });
                }

                // 处理每个进货项
                var results = new List<object>();
                foreach (var item in request.Items)
                {
                    // 从规格字符串中解析电压和容量
                    var batteryId = await GetProductIdFromSpec(item.Spec);
                    if (batteryId <= 0)
                    {
                        results.Add(new { spec = item.Spec, success = false, message = "找不到匹配的电池规格" });
                        continue;
                    }

                    // 更新库存
                    var inventoryDto = new StoreInventoryUpdateDto
                    {
                        ProductId = request.ProductId,
                        Quantity = item.Quantity,
                        AvailableQuantity = item.Quantity // 新进货的都是可用的
                    };

                    try
                    {
                        var inventory = await _storeService.UpdateStoreInventoryAsync(request.StoreId, inventoryDto);
                        results.Add(new
                        {
                            spec = item.Spec,
                            success = true,
                            quantity = inventory.Quantity,
                            availableQuantity = inventory.AvailableQuantity
                        });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"更新门店ID {request.StoreId} 的库存时发生错误");
                        results.Add(new { spec = item.Spec, success = false, message = ex.Message });
                    }
                }

                return Ok(new { success = true, message = "进货成功", results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "门店进货时发生错误");
                return StatusCode(500, new { message = "门店进货失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 库存调拨
        /// </summary>
        [HttpPost("transfer")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> TransferInventory([FromBody] TransferRequest request)
        {
            try
            {
                _logger.LogInformation("库存调拨，参数: {@Request}", request);

                if (request.FromStoreId <= 0 || request.ToStoreId <= 0)
                {
                    return BadRequest(new { message = "门店ID无效" });
                }

                if (request.FromStoreId == request.ToStoreId)
                {
                    return BadRequest(new { message = "源门店和目标门店不能相同" });
                }

                if (request.Quantity <= 0)
                {
                    return BadRequest(new { message = "调拨数量必须大于0" });
                }

                // 从规格字符串中解析电压和容量
                var batteryId = await GetProductIdFromSpec(request.BatterySpec);
                if (batteryId <= 0)
                {
                    return BadRequest(new { message = "找不到匹配的电池规格" });
                }

                // 检查源门店库存是否足够
                var sourceInventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.FromStoreId && i.ProductId == batteryId);

                if (sourceInventory == null || sourceInventory.AvailableQuantity < request.Quantity)
                {
                    return BadRequest(new { message = "源门店库存不足" });
                }

                // 1. 减少源门店库存
                sourceInventory.AvailableQuantity -= request.Quantity;
                sourceInventory.Quantity -= request.Quantity;
                sourceInventory.UpdatedAt = DateTime.Now;
                await _db.Updateable(sourceInventory).ExecuteCommandAsync();

                // 2. 增加目标门店库存
                var targetInventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.ToStoreId && i.ProductId == batteryId);

                if (targetInventory == null)
                {
                    // 如果目标门店没有该电池库存，创建新记录
                    targetInventory = new StoreInventory
                    {
                        StoreId = request.ToStoreId,
                        ProductId = batteryId,
                        Quantity = request.Quantity,
                        AvailableQuantity = request.Quantity,
                        UpdatedAt = DateTime.Now
                    };
                    await _db.Insertable(targetInventory).ExecuteCommandAsync();
                }
                else
                {
                    // 如果目标门店已有该电池库存，更新记录
                    targetInventory.Quantity += request.Quantity;
                    targetInventory.AvailableQuantity += request.Quantity;
                    targetInventory.UpdatedAt = DateTime.Now;
                    await _db.Updateable(targetInventory).ExecuteCommandAsync();
                }

                // 3. 记录调拨操作
                var transfer = new InventoryTransfer
                {
                    FromStoreId = request.FromStoreId,
                    ToStoreId = request.ToStoreId,
                    ProductId = batteryId,
                    Quantity = request.Quantity,
                    Remark = request.Remark,
                    CreatedAt = DateTime.Now
                };
                await _db.Insertable(transfer).ExecuteCommandAsync();

                return Ok(new
                {
                    success = true,
                    message = "调拨成功",
                    data = new
                    {
                        id = transfer.Id,
                        fromStoreId = transfer.FromStoreId,
                        toStoreId = transfer.ToStoreId,
                        batterySpec = request.BatterySpec,
                        quantity = transfer.Quantity,
                        remark = transfer.Remark,
                        transferTime = transfer.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "库存调拨时发生错误");
                return StatusCode(500, new { message = "库存调拨失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 清理库存
        /// </summary>
        [HttpPost("clear")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ClearInventory([FromBody] ClearRequest request)
        {
            try
            {
                _logger.LogInformation("清理库存，参数: {@Request}", request);

                if (request.StoreId <= 0)
                {
                    return BadRequest(new { message = "门店ID无效" });
                }

                if (request.Quantity <= 0)
                {
                    return BadRequest(new { message = "清理数量必须大于0" });
                }

                if (string.IsNullOrEmpty(request.Reason))
                {
                    return BadRequest(new { message = "清理原因不能为空" });
                }

                // 从规格字符串中解析电压和容量
                var batteryId = await GetProductIdFromSpec(request.BatterySpec);
                if (batteryId <= 0)
                {
                    return BadRequest(new { message = "找不到匹配的电池规格" });
                }

                // 检查门店库存是否足够
                var inventory = await _db.Queryable<StoreInventory>()
                    .FirstAsync(i => i.StoreId == request.StoreId && i.ProductId == batteryId);

                if (inventory == null || inventory.AvailableQuantity < request.Quantity)
                {
                    return BadRequest(new { message = "门店库存不足" });
                }

                // 减少库存
                inventory.AvailableQuantity -= request.Quantity;
                inventory.Quantity -= request.Quantity;
                inventory.UpdatedAt = DateTime.Now;
                await _db.Updateable(inventory).ExecuteCommandAsync();

                // 记录清理操作
                var clearRecord = new InventoryClear
                {
                    StoreId = request.StoreId,
                    ProductId = batteryId,
                    Quantity = request.Quantity,
                    Reason = request.Reason,
                    Remark = request.Remark,
                    CreatedAt = DateTime.Now
                };
                await _db.Insertable(clearRecord).ExecuteCommandAsync();

                return Ok(new
                {
                    success = true,
                    message = "清理成功",
                    data = new
                    {
                        id = clearRecord.Id,
                        storeId = clearRecord.StoreId,
                        batterySpec = request.BatterySpec,
                        quantity = clearRecord.Quantity,
                        reason = clearRecord.Reason,
                        remark = clearRecord.Remark,
                        clearTime = clearRecord.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理库存时发生错误");
                return StatusCode(500, new { message = "清理库存失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 从规格字符串中获取商品ID
        /// </summary>
        private async Task<int> GetProductIdFromSpec(string spec)
        {
            try
            {
                // 解析规格字符串，例如 "48V20Ah"
                var voltageStr = spec.Split('V')[0];
                var capacityStr = spec.Split('V')[1].Replace("Ah", "");

                if (double.TryParse(voltageStr, out double voltage) && double.TryParse(capacityStr, out double capacity))
                {
                    // 查找匹配的商品
                    var product = await _db.Queryable<Product>()
                        .FirstAsync(p => p.Voltage == (decimal)voltage && p.Capacity == (decimal)capacity);

                    return product?.Id ?? 0;
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }

    /// <summary>
    /// 库存查询参数
    /// </summary>
    public class InventoryQueryParameters
    {
        public int? StoreId { get; set; }

        // 设置为可选参数
        public string? Keyword { get; set; } = string.Empty;

        // 使用自定义模型绑定器处理逗号分隔的整数列表
        public List<int> SpecIds { get; set; } = new List<int>();

        public bool LowStock { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 进货请求
    /// </summary>
    public class StockInRequest
    {
        public int StoreId { get; set; }
        public List<StockInItem> Items { get; set; }
    }

    /// <summary>
    /// 进货项
    /// </summary>
    public class StockInItem
    {
        public string Spec { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public string Remark { get; set; }
        public int ProductId { get; set; }
    }

    /// <summary>
    /// 调拨请求
    /// </summary>
    public class TransferRequest
    {
        public int FromStoreId { get; set; }
        public int ToStoreId { get; set; }
        public string BatterySpec { get; set; }
        public int Quantity { get; set; }
        public string Remark { get; set; }
    }

    /// <summary>
    /// 清理库存请求
    /// </summary>
    public class ClearRequest
    {
        public int StoreId { get; set; }
        public string BatterySpec { get; set; }
        public int Quantity { get; set; }
        public string Reason { get; set; }
        public string Remark { get; set; }
    }
}
