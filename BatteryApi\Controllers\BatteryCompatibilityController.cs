using BatteryApi.DTOs.Battery;
using BatteryApi.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BatteryApi.Controllers;

/// <summary>
/// 兼容性控制器，用于处理前端的 /api/battery/* 路径请求
/// </summary>
[ApiController]
[Route("api/battery")]
[Authorize]
public class BatteryCompatibilityController : ControllerBase
{
    private readonly IBatteryCategoryService _categoryService;
    private readonly ILogger<BatteryCompatibilityController> _logger;

    public BatteryCompatibilityController(
        IBatteryCategoryService categoryService,
        ILogger<BatteryCompatibilityController> logger)
    {
        _categoryService = categoryService;
        _logger = logger;
    }

    /// <summary>
    /// 获取电池分类列表 - 兼容前端 /api/battery/categories 路径
    /// </summary>
    /// <returns>电池分类列表</returns>
    [HttpGet("categories")]
    [AllowAnonymous]
    public async Task<ActionResult<List<BatteryCategoryDto>>> GetBatteryCategories()
    {
        try
        {
            var categories = await _categoryService.GetAllCategoriesAsync();
            return Ok(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting battery categories");
            return StatusCode(500, new { message = "获取电池分类失败", error = ex.Message });
        }
    }
}
