using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace BatteryApi.DTOs.Product;

/// <summary>
/// 商品DTO
/// </summary>
public class ProductDto
{
    /// <summary>
    /// 商品ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 商品编号
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 商品规格/型号
    /// </summary>
    public string Spec { get; set; } = string.Empty;

    /// <summary>
    /// 商品状态
    /// </summary>
    public string Status { get; set; } = "Available";

    /// <summary>
    /// 销售价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 租赁价格
    /// </summary>
    public decimal RentPrice { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime ManufactureDate { get; set; }

    /// <summary>
    /// 使用寿命（月）
    /// </summary>
    public int Lifespan { get; set; }

    /// <summary>
    /// 商品描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 备注
    /// </summary>
    public string Notes { get; set; } = string.Empty;

    /// <summary>
    /// 分类ID
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// 分类代码
    /// </summary>
    public string CategoryCode { get; set; } = string.Empty;

    /// <summary>
    /// 分类名称
    /// </summary>
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 电压
    /// </summary>
    public string Voltage { get; set; } = string.Empty;

    /// <summary>
    /// 容量
    /// </summary>
    public string Capacity { get; set; } = string.Empty;

    /// <summary>
    /// 循环次数
    /// </summary>
    public string CycleCount { get; set; } = string.Empty;

    /// <summary>
    /// 充电时间
    /// </summary>
    public string ChargeTime { get; set; } = string.Empty;

    /// <summary>
    /// 关联的电池ID（用于BMS信息）
    /// </summary>
    public int? BatteryId { get; set; }

    /// <summary>
    /// 电池序列号（用于BMS查询）
    /// </summary>
    public string? BatterySerialNumber { get; set; }

    /// <summary>
    /// 图片列表
    /// </summary>
    public List<ProductImageDto> Images { get; set; } = new List<ProductImageDto>();

    /// <summary>
    /// 服务列表
    /// </summary>
    public List<ProductServiceDto> Services { get; set; } = new List<ProductServiceDto>();

    /// <summary>
    /// 安装费用列表
    /// </summary>
    public List<ProductInstallationFeeDto> InstallationFees { get; set; } = new List<ProductInstallationFeeDto>();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 商品图片DTO
/// </summary>
public class ProductImageDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string Url { get; set; } = string.Empty;
    public string RelativePath { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public long Size { get; set; }
    public string ImageType { get; set; } = "main";
    public bool IsMain { get; set; }
    public int Sort { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 商品服务DTO
/// </summary>
public class ProductServiceDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Sort { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 商品安装费用DTO
/// </summary>
public class ProductInstallationFeeDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int Sort { get; set; }
    public DateTime CreatedAt { get; set; }
}
