using System.Collections.Generic;
using System.Threading.Tasks;
using BatteryApi.DTOs;
using BatteryApi.DTOs.Battery;
using BatteryApi.DTOs.Common;

namespace BatteryApi.Services.Interfaces;

public interface IBatteryService
{
    Task<PagedResponse<BatteryDto>> GetBatteriesAsync(BatteryQueryParameters parameters);
    Task<BatteryDto> GetBatteryByIdAsync(int id);
    Task<BatteryDto> CreateBatteryAsync(CreateBatteryRequest request);
    Task<BatteryDto> UpdateBatteryAsync(int id, UpdateBatteryRequest request);
    Task<BatteryDto> CreateBatteryWithFilesAsync(CreateBatteryWithFilesRequest request);
    Task<BatteryDto> UpdateBatteryWithFilesAsync(int id, UpdateBatteryWithFilesRequest request);
    Task<bool> DeleteBatteryAsync(int id);
    Task<Dictionary<string, int>> GetBatteryStatusSummaryAsync();
    Task<double> GetAverageBatteryHealthAsync();
    Task<List<BatterySpecDto>> GetBatterySpecsAsync();
    Task<List<BatteryServiceDto>> GetBatteryServicesAsync(int id);
    Task<BatteryServiceDto> CreateBatteryServiceAsync(int batteryId, CreateBatteryServiceRequest request);
    Task<BatteryServiceDto> UpdateBatteryServiceAsync(int id, UpdateBatteryServiceRequest request);
    Task DeleteBatteryServiceAsync(int id);
    Task<List<BatteryInstallationFeeDto>> GetBatteryInstallationFeesAsync(int id);
    Task<BatteryInstallationFeeDto> CreateBatteryInstallationFeeAsync(int batteryId, CreateBatteryInstallationFeeRequest request);
    Task<BatteryInstallationFeeDto> UpdateBatteryInstallationFeeAsync(int id, UpdateBatteryInstallationFeeRequest request);
    Task DeleteBatteryInstallationFeeAsync(int id);
    Task<List<BatteryCategoryDto>> GetBatteryCategoriesAsync();
    Task<bool> CheckDuplicateAsync(int categoryId, string spec);
}