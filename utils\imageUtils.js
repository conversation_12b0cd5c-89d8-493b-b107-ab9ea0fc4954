/**
 * 图片URL处理工具
 */

/**
 * 获取图片显示URL
 * @param {string} imagePath - 图片路径
 * @param {string} baseUrl - 基础URL（可选）
 * @returns {string} 处理后的图片URL
 */
export function getImageDisplayUrl(imagePath, baseUrl = null) {
  if (!imagePath) {
    return '';
  }

  console.log('处理图片URL:', imagePath);

  // 如果是blob URL，直接返回（这是浏览器生成的临时URL）
  if (imagePath.startsWith('blob:')) {
    console.log('检测到blob URL，直接返回:', imagePath);
    return imagePath;
  }

  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    console.log('检测到完整URL，直接返回:', imagePath);
    return imagePath;
  }

  // 如果是临时路径，直接返回
  if (imagePath.startsWith('wxfile://') || imagePath.startsWith('file://')) {
    console.log('检测到临时路径，直接返回:', imagePath);
    return imagePath;
  }

  // 如果是data URL，直接返回
  if (imagePath.startsWith('data:')) {
    console.log('检测到data URL，直接返回:', imagePath);
    return imagePath;
  }

  // 如果是相对路径，拼接基础URL
  const finalBaseUrl = baseUrl || (
    process.env.NODE_ENV === 'development'
      ? 'http://localhost:5242'
      : (typeof window !== 'undefined' ? window.location.origin : '')
  );

  // 确保路径以 / 开头
  const path = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  const finalUrl = `${finalBaseUrl}${path}`;

  console.log('拼接后的URL:', finalUrl);
  return finalUrl;
}

/**
 * 检查是否为有效的图片URL
 * @param {string} url - 图片URL
 * @returns {boolean} 是否为有效的图片URL
 */
export function isValidImageUrl(url) {
  if (!url || typeof url !== 'string') {
    return false;
  }

  // blob URL
  if (url.startsWith('blob:')) {
    return true;
  }

  // 完整URL
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return true;
  }

  // 临时路径
  if (url.startsWith('wxfile://') || url.startsWith('file://')) {
    return true;
  }

  // data URL
  if (url.startsWith('data:image/')) {
    return true;
  }

  // 相对路径（假设是有效的）
  if (url.startsWith('/') || url.includes('.')) {
    return true;
  }

  return false;
}

/**
 * 处理uni.chooseImage的响应
 * @param {Object} res - uni.chooseImage的响应对象
 * @returns {Array} 处理后的文件列表
 */
export function processChooseImageResponse(res) {
  console.log('处理uni.chooseImage响应:', res);

  let tempFiles = [];

  if (res.tempFiles && res.tempFiles.length > 0) {
    // 标准格式：包含tempFiles数组
    tempFiles = res.tempFiles.map((file, index) => ({
      path: file.path,
      size: file.size || 0,
      name: file.name || `image_${Date.now()}_${index}.jpg`,
      originalFile: file // 保存原始文件对象
    }));
  } else if (res.tempFilePaths && res.tempFilePaths.length > 0) {
    // 兼容格式：只有tempFilePaths数组
    tempFiles = res.tempFilePaths.map((path, index) => ({
      path: path,
      size: 0,
      name: `image_${Date.now()}_${index}.jpg`,
      originalFile: null
    }));
  }

  console.log('处理后的tempFiles:', tempFiles);
  return tempFiles;
}

/**
 * 创建图片对象
 * @param {Object} file - 文件对象
 * @param {number} index - 索引
 * @param {boolean} isMain - 是否为主图
 * @returns {Object} 图片对象
 */
export function createImageItem(file, index = 0, isMain = false) {
  return {
    id: Date.now() + index,
    tempPath: file.path,
    url: '',
    name: file.name || `image_${Date.now()}_${index}.jpg`,
    size: file.size || 0,
    status: 'pending',
    isMain: isMain,
    isNew: true,
    imageType: 'main',
    file: file.originalFile || file // 保存原始文件对象，用于直接传递到后端
  };
}

/**
 * 验证图片格式
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否为支持的图片格式
 */
export function isValidImageFormat(fileName) {
  if (!fileName) return false;
  
  const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  return validExtensions.includes(extension);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查图片是否需要压缩
 * @param {number} fileSize - 文件大小（字节）
 * @param {number} threshold - 压缩阈值（字节，默认1MB）
 * @returns {boolean} 是否需要压缩
 */
export function shouldCompressImage(fileSize, threshold = 1024 * 1024) {
  return fileSize > threshold;
}

/**
 * 生成唯一的图片文件名
 * @param {string} originalName - 原始文件名
 * @param {string} prefix - 前缀
 * @returns {string} 唯一的文件名
 */
export function generateUniqueFileName(originalName, prefix = 'img') {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  const extension = originalName ? originalName.substring(originalName.lastIndexOf('.')) : '.jpg';
  
  return `${prefix}_${timestamp}_${random}${extension}`;
}

/**
 * 调试图片URL问题
 * @param {string} imagePath - 图片路径
 * @param {string} context - 上下文信息
 */
export function debugImageUrl(imagePath, context = '') {
  console.group(`🖼️ 图片URL调试 ${context ? `- ${context}` : ''}`);
  console.log('原始路径:', imagePath);
  console.log('路径类型:', getImageUrlType(imagePath));
  console.log('是否有效:', isValidImageUrl(imagePath));
  console.log('处理后URL:', getImageDisplayUrl(imagePath));
  console.groupEnd();
}

/**
 * 获取图片URL类型
 * @param {string} imagePath - 图片路径
 * @returns {string} URL类型
 */
function getImageUrlType(imagePath) {
  if (!imagePath) return 'empty';
  if (imagePath.startsWith('blob:')) return 'blob';
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) return 'http';
  if (imagePath.startsWith('wxfile://')) return 'wxfile';
  if (imagePath.startsWith('file://')) return 'file';
  if (imagePath.startsWith('data:')) return 'data';
  if (imagePath.startsWith('/')) return 'absolute';
  return 'relative';
}

export default {
  getImageDisplayUrl,
  isValidImageUrl,
  processChooseImageResponse,
  createImageItem,
  isValidImageFormat,
  formatFileSize,
  shouldCompressImage,
  generateUniqueFileName,
  debugImageUrl
};
