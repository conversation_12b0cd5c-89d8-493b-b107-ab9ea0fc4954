/**
 * 商品API服务
 * 替代原有的电池API，统一管理所有商品（包括电池类商品）
 */
import request from '@/utils/request';

// API接口
const ProductAPI = {
  /**
   * 获取商品列表（简化版，用于BMS监控）
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getProductList(params = { page: 1, pageSize: 20 }) {
    console.log('获取商品列表（BMS监控），参数:', params)

    // 转换参数格式
    const apiParams = {
      pageNumber: params.page || 1,
      pageSize: params.pageSize || 20
    }

    return request.get('/api/product', apiParams).then(response => {
      console.log('商品列表响应:', response)

      // 处理响应数据
      let products = []

      if (response && response.data && response.data.items && Array.isArray(response.data.items)) {
        products = response.data.items.map(item => ({
          id: item.id,
          code: item.productCode || `PROD-${item.id}`,
          name: item.name,
          spec: item.spec,
          categoryName: item.categoryName,
          status: item.status,
          price: item.price,
          rentPrice: item.rentPrice,
          manufactureDate: item.manufactureDate,
          voltage: item.voltage,
          capacity: item.capacity,
          cycleCount: item.cycleCount,
          chargeTime: item.chargeTime
        }))
      } else if (Array.isArray(response)) {
        products = response.map(item => ({
          id: item.id,
          code: item.productCode || `PROD-${item.id}`,
          name: item.name,
          spec: item.spec,
          categoryName: item.categoryName,
          status: item.status,
          price: item.price,
          rentPrice: item.rentPrice,
          manufactureDate: item.manufactureDate,
          voltage: item.voltage,
          capacity: item.capacity,
          cycleCount: item.cycleCount,
          chargeTime: item.chargeTime
        }))
      }

      return {
        code: 0,
        message: 'success',
        data: products
      }
    }).catch(error => {
      console.error('获取商品列表失败:', error)
      return {
        code: -1,
        message: error.message || '获取商品列表失败',
        data: []
      }
    })
  },

  /**
   * 获取商品列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getProducts(params = { pageNumber: 1, pageSize: 15 }) {
    // 将 page 参数转换为 pageNumber，以匹配后端 API 的参数名称
    const apiParams = { ...params };
    if (apiParams.page) {
      apiParams.pageNumber = apiParams.page;
      delete apiParams.page;
    }

    // 处理关键词搜索参数
    if (apiParams.keyword !== undefined && apiParams.keyword !== null) {
      apiParams.SearchTerm = apiParams.keyword;
      apiParams.searchTerm = apiParams.keyword;
      apiParams.search = apiParams.keyword;
      console.log('设置搜索参数:', apiParams.keyword);
    }

    // 处理筛选参数
    if (apiParams.specIds && Array.isArray(apiParams.specIds) && apiParams.specIds.length > 0) {
      apiParams.specIds = apiParams.specIds.join(',');
    }

    if (apiParams.statusIds && Array.isArray(apiParams.statusIds) && apiParams.statusIds.length > 0) {
      apiParams.statusIds = apiParams.statusIds.join(',');
    }

    console.log('发送请求到 /api/product，参数:', apiParams);
    return request.get('/api/product', apiParams).then(response => {
      console.log('获取商品列表原始响应:', JSON.stringify(response));

      // 将API响应数据转换为前端需要的格式
      let products = [];
      let totalItems = 0;
      let pageNumber = apiParams.pageNumber || 1;
      let pageSize = apiParams.pageSize || 15;
      let totalPages = 0;
      let hasNextPage = false;
      let hasPreviousPage = false;

      try {
        // 处理响应数据
        if (response && response.data && response.data.items && Array.isArray(response.data.items)) {
          console.log('响应有 data.items 属性，长度:', response.data.items.length);

          products = response.data.items.map(item => {
            // 处理价格字段，确保它们是数字
            let price = 0;
            let rentPrice = 0;

            if (item.price !== undefined && item.price !== null) {
              price = parseFloat(item.price);
              if (isNaN(price)) price = 0;
            }

            if (item.rentPrice !== undefined && item.rentPrice !== null) {
              rentPrice = parseFloat(item.rentPrice);
              if (isNaN(rentPrice)) rentPrice = 0;
            }

            // 处理生产日期
            let manufactureDate = '未知';
            if (item.manufactureDate) {
              try {
                let dateStr = item.manufactureDate;
                if (typeof dateStr === 'string' && dateStr.includes('T')) {
                  dateStr = dateStr.split('T')[0];
                }
                const date = new Date(dateStr);
                if (!isNaN(date.getTime())) {
                  manufactureDate = dateStr;
                }
              } catch (e) {
                console.error('处理生产日期出错:', e);
              }
            }

            return {
              id: item.id,
              code: item.productCode || `PROD-${item.id}`,
              name: item.name || '',
              spec: item.spec || '未知规格',
              categoryCode: item.categoryCode || '',
              categoryId: item.categoryId,
              categoryName: item.categoryName || '',
              status: this._mapStatusFromApi(item.status),
              price: price,
              rentPrice: rentPrice,
              manufactureDate: manufactureDate,
              lifespan: item.lifespan || 36,
              description: item.description || '',
              notes: item.notes || '',
              voltage: item.voltage || '',
              capacity: item.capacity || '',
              cycleCount: item.cycleCount || '',
              chargeTime: item.chargeTime || ''
            };
          });

          totalItems = response.data.totalItems || response.data.items.length;
          pageNumber = response.data.pageNumber || apiParams.pageNumber || 1;
          pageSize = response.data.pageSize || apiParams.pageSize || 15;
          totalPages = response.data.totalPages || Math.ceil(totalItems / pageSize);
          hasNextPage = response.data.hasNextPage || pageNumber < totalPages;
          hasPreviousPage = response.data.hasPreviousPage || pageNumber > 1;
        }
      } catch (error) {
        console.error('处理商品列表数据时出错:', error);
      }

      console.log('处理后的商品列表:', products);

      return {
        code: 0,
        message: 'success',
        data: {
          products,
          totalItems,
          pageNumber,
          pageSize,
          totalPages,
          hasNextPage,
          hasPreviousPage
        }
      };
    }).catch(error => {
      console.error('获取商品列表失败:', error);
      return {
        code: -1,
        message: error.message || '获取商品列表失败',
        data: {
          products: [],
          totalItems: 0,
          pageNumber: apiParams.pageNumber || 1,
          pageSize: apiParams.pageSize || 15,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false
        }
      };
    });
  },

  /**
   * 获取商品详情
   * @param {Number} id 商品ID
   * @returns {Promise} Promise对象
   */
  getProductDetail(id) {
    if (!id || id <= 0) {
      console.error('无效的商品 ID:', id);
      return Promise.reject(new Error('无效的商品 ID'));
    }

    console.log(`发送请求到 /api/product/${id}`);

    return request.get(`/api/product/${id}`).then(response => {
      console.log('获取商品详情响应:', response);

      if (response && response.data) {
        const item = response.data;
        
        // 处理价格字段
        let price = 0;
        let rentPrice = 0;

        if (item.price !== undefined && item.price !== null) {
          price = parseFloat(item.price);
          if (isNaN(price)) price = 0;
        }

        if (item.rentPrice !== undefined && item.rentPrice !== null) {
          rentPrice = parseFloat(item.rentPrice);
          if (isNaN(rentPrice)) rentPrice = 0;
        }

        // 处理生产日期
        let manufactureDate = '';
        if (item.manufactureDate) {
          try {
            let dateStr = item.manufactureDate;
            if (typeof dateStr === 'string' && dateStr.includes('T')) {
              dateStr = dateStr.split('T')[0];
            }
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              manufactureDate = dateStr;
            }
          } catch (e) {
            console.error('处理生产日期出错:', e);
          }
        }

        return {
          code: 0,
          message: 'success',
          data: {
            id: item.id,
            code: item.productCode || `PROD-${item.id}`,
            name: item.name || '',
            spec: item.spec || '',
            categoryCode: item.categoryCode || '',
            categoryId: item.categoryId,
            categoryName: item.categoryName || '',
            status: this._mapStatusFromApi(item.status),
            price: price,
            rentPrice: rentPrice,
            manufactureDate: manufactureDate,
            lifespan: item.lifespan || 36,
            description: item.description || '',
            notes: item.notes || '',
            voltage: item.voltage || '',
            capacity: item.capacity || '',
            cycleCount: item.cycleCount || '',
            chargeTime: item.chargeTime || ''
          }
        };
      }

      return {
        code: -1,
        message: '商品不存在',
        data: null
      };
    }).catch(error => {
      console.error('获取商品详情失败:', error);
      return {
        code: -1,
        message: error.message || '获取商品详情失败',
        data: null
      };
    });
  },

  /**
   * 创建商品
   * @param {Object} productData 商品数据
   * @returns {Promise} Promise对象
   */
  createProduct(productData) {
    console.log('创建商品，数据:', productData);
    return request.post('/api/product', productData).then(response => {
      console.log('创建商品响应:', response);
      return {
        code: 0,
        message: '商品创建成功',
        data: response.data
      };
    }).catch(error => {
      console.error('创建商品失败:', error);
      return {
        code: -1,
        message: error.message || '创建商品失败',
        data: null
      };
    });
  },

  /**
   * 更新商品
   * @param {Number} id 商品ID
   * @param {Object} productData 商品数据
   * @returns {Promise} Promise对象
   */
  updateProduct(id, productData) {
    console.log(`更新商品 ${id}，数据:`, productData);
    return request.put(`/api/product/${id}`, productData).then(response => {
      console.log('更新商品响应:', response);
      return {
        code: 0,
        message: '商品更新成功',
        data: response.data
      };
    }).catch(error => {
      console.error('更新商品失败:', error);
      return {
        code: -1,
        message: error.message || '更新商品失败',
        data: null
      };
    });
  },

  /**
   * 删除商品
   * @param {Number} id 商品ID
   * @returns {Promise} Promise对象
   */
  deleteProduct(id) {
    console.log(`删除商品 ${id}`);
    return request.delete(`/api/product/${id}`).then(response => {
      console.log('删除商品响应:', response);
      return {
        code: 0,
        message: '商品删除成功',
        data: null
      };
    }).catch(error => {
      console.error('删除商品失败:', error);
      return {
        code: -1,
        message: error.message || '删除商品失败',
        data: null
      };
    });
  },

  /**
   * 获取商品统计信息
   * @returns {Promise} Promise对象
   */
  getProductStats() {
    return request.get('/api/product/stats').then(response => {
      if (response && response.data) {
        return {
          code: 0,
          message: 'success',
          data: {
            totalProducts: response.data.totalProducts || 0,
            availableProducts: response.data.availableProducts || 0,
            rentedProducts: response.data.rentedProducts || 0,
            soldProducts: response.data.soldProducts || 0,
            maintenanceProducts: response.data.maintenanceProducts || 0,
            categoryStats: response.data.categoryStats || [],
            monthlyRentals: response.data.monthlyRentals || []
          }
        };
      }

      return {
        code: 0,
        message: 'success',
        data: {
          totalProducts: 0,
          availableProducts: 0,
          rentedProducts: 0,
          soldProducts: 0,
          maintenanceProducts: 0,
          categoryStats: [],
          monthlyRentals: []
        }
      };
    }).catch(error => {
      console.error('获取商品统计失败:', error);
      return {
        code: -1,
        message: error.message || '获取商品统计失败',
        data: null
      };
    });
  },

  /**
   * 获取商品分类列表
   * @returns {Promise} Promise对象
   */
  getProductCategories() {
    console.log('获取商品分类列表');
    return request.get('/api/battery-categories').then(response => {
      console.log('获取商品分类列表响应:', response);

      if (response && response.data && Array.isArray(response.data)) {
        return {
          code: 0,
          message: 'success',
          data: response.data
        };
      }

      return {
        code: 0,
        message: 'success',
        data: []
      };
    }).catch(error => {
      console.error('获取商品分类列表失败:', error);
      return {
        code: -1,
        message: error.message || '获取商品分类列表失败',
        data: []
      };
    });
  },

  /**
   * 状态映射函数
   * @private
   */
  _mapStatusFromApi(status) {
    const statusMap = {
      'Available': '可用',
      'Rented': '已租出',
      'Sold': '已售出',
      'Maintenance': '维护中',
      'Damaged': '损坏',
      'Retired': '已退役'
    };
    return statusMap[status] || status || '未知';
  }
};

export default ProductAPI;
