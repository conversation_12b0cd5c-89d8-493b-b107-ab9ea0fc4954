<template>
  <view class="user-container">
    <!-- 总部管理员信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image
          :src="
            userInfo && userInfo.avatar
              ? userInfo.avatar
              : '/static/default-avatar.png'
          "
          mode="aspectFill"
        ></image>
      </view>
      <view class="user-info">
        <view class="user-name">{{
          (userInfo && userInfo.nickname) || "未登录"
        }}</view>
        <view class="admin-tag">总部管理员</view>
        <view class="user-phone" v-if="userInfo && userInfo.phone">{{
          formatPhone(userInfo.phone)
        }}</view>
        <view class="user-login" v-else @tap="navToLogin">点击登录</view>
      </view>
      <view class="user-setting" @tap="navToSetting" v-if="userInfo && isLogin">
        <text class="iconfont icon-setting"></text>
      </view>
    </view>



    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @tap="navToUsers">
          <view class="menu-icon user-icon">
            <image src="/static/icons/user-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">用户管理</text>
            <text class="menu-desc">管理系统用户</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToVerifications">
          <view class="menu-icon verification-icon">
            <image src="/static/icons/verification-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">实名认证管理</text>
            <text class="menu-desc">审核用户实名认证</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToStores">
          <view class="menu-icon store-icon">
            <image src="/static/icons/store-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">门店管理</text>
            <text class="menu-desc">管理系统门店</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToBatteries">
          <view class="menu-icon battery-icon">
            <image
              src="/static/icons/battery-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">商品管理</text>
            <text class="menu-desc">管理商品信息</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <!-- <view class="menu-item" @tap="navToOrders">
          <view class="menu-icon order-icon">
            <image src="/static/icons/order-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">订单管理</text>
            <text class="menu-desc">查看所有订单</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view> -->

        <!-- <view class="menu-item" @tap="navToInventoryManagement">
          <view class="menu-icon inventory-icon">
            <image
              src="/static/icons/inventory-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">商品库存管理</text>
            <text class="menu-desc">门店商品库存管理</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view> -->

        <view class="menu-item" @tap="navToCategoryManagement">
          <view class="menu-icon category-icon">
            <image
              src="/static/icons/category-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">商品分类管理</text>
            <text class="menu-desc">管理商品分类和规格</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>
        <view class="menu-item" @tap="navToAnalytics">
          <view class="menu-icon analytics-icon">
            <image
              src="/static/icons/analytics-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">数据分析</text>
            <text class="menu-desc">系统运营数据</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>
      </view>

      <view class="menu-group">
        <!-- <view class="menu-item" @tap="navToStoreInitialize">
          <view class="menu-icon settings-icon">
            <image src="/static/icons/store-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">门店数据初始化</text>
            <text class="menu-desc">生成模拟门店数据</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToOrderInitialize">
          <view class="menu-icon settings-icon">
            <image src="/static/icons/order-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">订单数据初始化</text>
            <text class="menu-desc">生成模拟订单数据</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view> -->

        <view class="menu-item" @tap="navToSettings">
          <view class="menu-icon settings-icon">
            <image
              src="/static/icons/settings-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">系统设置</text>
            <text class="menu-desc">配置系统参数</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToPassword">
          <view class="menu-icon password-icon">
            <image
              src="/static/icons/password-icon.svg"
              mode="aspectFit"
            ></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">修改密码</text>
            <text class="menu-desc">定期修改密码更安全</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>

        <view class="menu-item" @tap="navToAbout">
          <view class="menu-icon about-icon">
            <image src="/static/icons/about-icon.svg" mode="aspectFit"></image>
          </view>
          <view class="menu-info">
            <text class="menu-name">关于系统</text>
            <text class="menu-desc">系统版本信息</text>
          </view>
          <view class="menu-arrow">
            <text class="iconfont icon-right"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section" v-if="isLogin">
      <button class="logout-btn" @tap="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from "vuex";

export default {
  data() {
    return {
      stats: {
        orderCount: 0,
        batteryCount: 0,
        userCount: 0,
        storeCount: 0,
      },
      isNavigating: false,
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapGetters("user", ["isLogin"]),
  },
  onShow() {
    // 如果已登录，获取总部统计数据
    if (this.isLogin) {
      this.fetchAdminStats();
    }
  },
  methods: {
    ...mapActions("user", ["logout", "getAdminStats"]),

    // 格式化手机号
    formatPhone(phone) {
      if (!phone) return "";
      return phone.replace(/^(\d{3})\d{4}(\d{4})$/, "$1****$2");
    },

    // 获取总部统计数据
    async fetchAdminStats() {
      try {
        const stats = await this.getAdminStats();
        if (stats) {
          this.stats = stats;
        }
      } catch (error) {
        console.error("获取总部统计数据失败", error);
      }
    },

    // 导航到登录页
    navToLogin() {
      uni.navigateTo({
        url: "/pages/login/login",
      });
    },

    // 导航到设置页
    navToSetting() {
      this.navToSettings();
    },

    // 导航到用户管理
    navToUsers() {
      // Set a flag to prevent multiple clicks
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        // Delay to release main thread and avoid DOM queries during navigation
        uni.navigateTo({
          url: "/pages/admin/user/list",
          success: (res) => {
            console.log("Successfully navigated to user management page", res);
          },
          fail: (err) => {
            console.error("Navigation to user management page failed:", err);
            uni.showToast({
              title: "Navigation failed",
              icon: "none",
              duration: 2000,
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到实名认证管理
    navToVerifications() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/verification/list",
          success: () => {
            console.log("导航到实名认证管理页面成功");
          },
          fail: (err) => {
            console.error("导航到实名认证管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到门店管理
    navToStores() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/store/list",
          success: () => {
            console.log("导航到门店管理页面成功");
          },
          fail: (err) => {
            console.error("导航到门店管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到电池管理
    navToBatteries() {
      // 使用 navigateTo 而不是 switchTab，因为该页面不是 tabBar 页面
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/product/list",
          success: () => {
            console.log("导航到电池管理页面成功");
          },
          fail: (err) => {
            console.error("导航到电池管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到订单管理
    navToOrders() {
      // 使用 navigateTo 而不是 switchTab，因为该页面不是 tabBar 页面
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/order/list",
          success: () => {
            console.log("导航到订单管理页面成功");
          },
          fail: (err) => {
            console.error("导航到订单管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到数据分析
    navToAnalytics() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/analytics/analytics",
          success: () => {
            console.log("导航到数据分析页面成功");
          },
          fail: (err) => {
            console.error("导航到数据分析页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到商品进销存管理
    navToInventoryManagement() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/inventory-management/index",
          success: () => {
            console.log("导航到商品进销存管理页面成功");
          },
          fail: (err) => {
            console.error("导航到商品进销存管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到门店数据初始化页面
    navToStoreInitialize() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/store/initialize",
          success: () => {
            console.log("导航到门店数据初始化页面成功");
          },
          fail: (err) => {
            console.error("导航到门店数据初始化页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到订单数据初始化页面
    navToOrderInitialize() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/order/initialize",
          success: () => {
            console.log("导航到订单数据初始化页面成功");
          },
          fail: (err) => {
            console.error("导航到订单数据初始化页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 导航到系统设置
    navToSettings() {
      uni.navigateTo({
        url: "/pages/admin/settings/settings",
      });
    },

    // 导航到修改密码页
    navToPassword() {
      uni.navigateTo({
        url: "/pages/user/password",
      });
    },

    // 导航到关于系统
    navToAbout() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/about/about",
          success: () => {
            console.log("导航到关于系统页面成功");
          },
          fail: (err) => {
            console.error("导航到关于系统页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 跳转到商品分类管理
    navToCategoryManagement() {
      if (this.isNavigating) return;
      this.isNavigating = true;

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/admin/category/list",
          success: () => {
            console.log("导航到商品分类管理页面成功");
          },
          fail: (err) => {
            console.error("导航到商品分类管理页面失败", err);
            uni.showToast({
              title: "页面跳转失败",
              icon: "none",
            });
          },
          complete: () => {
            // Reset navigation flag
            this.isNavigating = false;
          },
        });
      }, 100); // Small delay for UI thread to stabilize
    },

    // 退出登录
    async handleLogout() {
      const result = await uni.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        confirmColor: "#2979ff",
      });
      let LastResult = result[1];

      if (LastResult.confirm) {
        try {
          await this.logout();

          // 立即跳转到登录页
          uni.reLaunch({
            url: "/pages/login/login",
            success: () => {
              uni.showToast({
                title: "已退出登录",
                icon: "success",
              });
            },
          });
        } catch (error) {
          uni.showToast({
            title: "退出失败，请重试",
            icon: "none",
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.user-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

.user-card {
  position: relative;
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #2979ff;

  .user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    overflow: hidden;
    margin-right: 30rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .user-info {
    flex: 1;
  }

  .user-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .admin-tag {
    font-size: 24rpx;
    color: #fff;
    margin-bottom: 6rpx;
    background-color: rgba(255, 255, 255, 0.2);
    display: inline-block;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
  }

  .user-phone {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }

  .user-login {
    font-size: 28rpx;
    color: #fff;
    padding: 10rpx 30rpx;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    display: inline-block;
  }

  .user-setting {
    position: absolute;
    right: 30rpx;
    top: 40rpx;

    .iconfont {
      font-size: 40rpx;
      color: #fff;
    }
  }
}

.user-stats {
  display: flex;
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;

  .stat-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    &:not(:last-child)::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1rpx;
      height: 50%;
      background-color: #f0f0f0;
    }
  }

  .stat-num {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .stat-label {
    font-size: 26rpx;
    color: #999;
  }
}

.menu-section {
  margin-bottom: 20rpx;
}

.menu-group {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f5f5f5;

    .menu-icon {
      transform: scale(0.92);

      .iconfont {
        transform: scale(1.1);
      }

      &::before {
        opacity: 0.2;
        transform: scale(1.2);
      }
    }
  }

  // 菜单项悬停效果
  &:hover {
    background-color: #fafafa;
  }

  .menu-icon {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10rpx;
    position: relative;
    transition: all 0.3s;

    image {
      width: 44rpx;
      height: 44rpx;
      transition: all 0.3s;
      z-index: 2;
    }

    &::before {
      content: "";
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      z-index: 1;
      opacity: 0.1;
      transition: all 0.3s;
    }

    &.user-icon::before {
      background-color: #2979ff;
    }

    &.store-icon::before {
      background-color: #ff9500;
    }

    &.battery-icon::before {
      background-color: #4cd964;
    }

    &.order-icon::before {
      background-color: #5856d6;
    }

    &.inventory-icon::before {
      background-color: #ff3b30;
    }

    &.analytics-icon::before {
      background-color: #007aff;
    }

    &.settings-icon::before {
      background-color: #34aadc;
    }

    &.password-icon::before {
      background-color: #8e8e93;
    }

    &.about-icon::before {
      background-color: #5856d6;
    }

    &.supplier-icon::before {
      background-color: #ff9500;
    }

    &.category-icon::before {
      background-color: #9c27b0;
    }
  }

  .menu-info {
    flex: 1;
    margin-left: 20rpx;
  }

  .menu-name {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 5rpx;
  }

  .menu-desc {
    font-size: 24rpx;
    color: #999;
  }

  .menu-arrow {
    .iconfont {
      font-size: 30rpx;
      color: #ccc;
    }
  }
}

.logout-section {
  padding: 0 30rpx;
  margin-top: 60rpx;
}

.logout-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #fff;
  color: #ff3b30;
  font-size: 32rpx;
  border-radius: 45rpx;
}
</style>
