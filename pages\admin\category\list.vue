<template>
  <view class="page-wrapper">
    <view class="category-container">

      <!-- 加载中提示 -->
      <view class="loading-container" v-if="loading">
        <u-loading-icon mode="circle" size="36" color="#2979ff"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 分类列表 -->
      <view class="category-list" v-else>
        <view class="category-item" v-for="(category, index) in categories" :key="category.code">
          <view class="category-header" @click="toggleCategory(index)">
            <view class="category-name">{{ category.name }}</view>
            <view class="category-code">({{ category.code }})</view>
            <view class="category-actions">
              <view class="action-btn edit-btn" @click.stop="editCategory(category)">
                <text>编辑</text>
              </view>
              <view class="action-btn delete-btn" @click.stop="deleteCategory(category)">
                <text>删除</text>
              </view>
              <view class="toggle-btn">
                <text class="iconfont" :class="category.expanded ? 'icon-up' : 'icon-down'"></text>
              </view>
            </view>
          </view>

          <!-- 规格列表 -->
          <view class="specs-list" v-if="category.expanded">
            <view class="specs-header">
              <text>规格列表</text>
              <view class="add-spec-btn" @click.stop="showAddSpecModal(category)">
                <text>+</text>
                <text>添加规格</text>
              </view>
            </view>

            <view class="specs-table">
              <view class="specs-table-header">
                <view class="specs-table-cell name-cell">规格名称</view>
                <view class="specs-table-cell price-cell">价格(元)</view>
                <view class="specs-table-cell action-cell">操作</view>
              </view>

              <view class="specs-table-row" v-for="spec in category.specs" :key="spec.id">
                <view class="specs-table-cell name-cell">{{ spec.name }}</view>
                <view class="specs-table-cell price-cell">{{ (spec.price || 0).toFixed(2) }}</view>
                <view class="specs-table-cell action-cell">
                  <view class="spec-action edit-btn" @click="editSpec(category, spec)">
                    <text>编辑</text>
                  </view>
                  <view class="spec-action delete-btn" @click="deleteSpec(category, spec)">
                    <text>删除</text>
                  </view>
                </view>
              </view>

              <view class="empty-specs" v-if="!category.specs || category.specs.length === 0">
                <text>暂无规格数据</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-categories" v-if="!categories || categories.length === 0">
          <text>暂无分类数据</text>
        </view>
      </view>

      <!-- 添加分类按钮 -->
      <view class="add-category-btn" @click="showAddCategoryModal">
        <text>+</text>
        <text>添加分类</text>
      </view>

      <!-- 分类编辑弹窗 -->
      <u-popup :show="showCategoryModal" @close="closeCategoryModal" mode="center" :round="12" :closeable="true" :z-index="19999" :mask-z-index="10000">
        <view class="popup-content">
          <view class="popup-title">
            {{ isEditingCategory ? '编辑分类' : '添加分类' }}
          </view>
          <view class="popup-body">
            <u-form :model="currentCategory" label-position="top" label-width="auto">
              <u-form-item label="分类名称" required>
                <u-input v-model="currentCategory.name" placeholder="请输入分类名称" />
              </u-form-item>
              <u-form-item label="分类代码" required>
                <u-input v-model="currentCategory.code" placeholder="请输入分类代码" :disabled="isEditingCategory" />
              </u-form-item>
            </u-form>
          </view>
          <view class="popup-footer">
            <button class="btn btn-cancel" @click="closeCategoryModal">取消</button>
            <button class="btn btn-primary" @click="saveCategory">确定</button>
          </view>
        </view>
      </u-popup>

      <!-- 规格编辑弹窗 -->
      <u-popup :show="showSpecModal" @close="closeSpecModal" mode="center" :round="12" :closeable="true" :z-index="19999" :mask-z-index="10000">
        <view class="popup-content spec-popup-content">
          <view class="popup-title">
            {{ isEditingSpec ? '编辑规格' : '添加规格' }}
          </view>
          <view class="popup-body">
            <u-form :model="currentSpec" label-position="top" label-width="auto">
              <u-form-item label="所属分类">
                <u-input v-model="currentSpecCategory.name" disabled />
              </u-form-item>
              <u-form-item label="规格名称" required>
                <u-input v-model="currentSpec.name" placeholder="请输入规格名称" />
              </u-form-item>
              <u-form-item label="价格 (元)">
                <u-input v-model="currentSpec.price" placeholder="请输入价格" type="number" />
              </u-form-item>
            </u-form>
          </view>
          <view class="popup-footer">
            <view class="btn btn-cancel" @click="closeSpecModal">取消</view>
            <button class="btn btn-primary" @click="saveSpec">确定</button>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import batteryAPI from '@/api/battery';
import uPopup from 'uview-ui/components/u-popup/u-popup';
import uLoadingIcon from 'uview-ui/components/u-loading-icon/u-loading-icon';
import uForm from 'uview-ui/components/u-form/u-form';
import uFormItem from 'uview-ui/components/u-form-item/u-form-item';
import uInput from 'uview-ui/components/u-input/u-input';
import uTextarea from 'uview-ui/components/u-textarea/u-textarea';
import uButton from 'uview-ui/components/u-button/u-button';

export default {
  components: {
    uPopup,
    uLoadingIcon,
    uForm,
    uFormItem,
    uInput,
    uTextarea,
    uButton
  },
  data() {
    return {
      categories: [],
      loading: false,
      showCategoryModal: false,
      showSpecModal: false,
      isEditingCategory: false,
      isEditingSpec: false,
      currentCategory: {
        code: '',
        name: '',
        description: '',
        specs: []
      },
      currentSpecCategory: {
        id: 0,
        code: '',
        name: ''
      },
      currentSpec: {
        id: 0,
        name: '',
        voltage: 0,
        capacity: 0,
        weight: 0,
        dimensions: '',
        price: 0,
        description: '',
        categoryId: 0
      },
      nextSpecId: 1 // 下一个规格ID
    }
  },

  onLoad() {
    this.loadCategories();
  },

  methods: {
    // 加载分类列表
    async loadCategories() {
      this.loading = true;

      try {
        const response = await batteryAPI.getBatteryCategories();
        console.log('获取分类列表响应:', response);

        if (response && response.code === 0 && Array.isArray(response.data)) {
          // 为每个分类添加 expanded 属性和空的 specs 数组
          this.categories = response.data.map(category => ({
            ...category,
            expanded: false,
            specs: []
          }));

          // 更新 nextSpecId
          this.nextSpecId = 1;
        } else {
          this.categories = [];
          uni.showToast({
            title: '获取分类列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取分类列表错误:', error);
        this.categories = [];
        uni.showToast({
          title: '获取分类列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载分类的规格列表
    async loadCategorySpecs(category, index) {
      if (!category.code) {
        console.warn('分类代码为空，无法加载规格列表');
        return;
      }

      console.log(`开始加载分类 ${category.code} (ID: ${category.id}) 的规格列表`);

      try {
        uni.showLoading({
          title: '加载规格中...',
          mask: true
        });

        const response = await batteryAPI.getSpecsByCategoryId(category.id);
        console.log(`获取分类 ${category.id} 的规格列表响应:`, response);
        console.log('响应类型:', typeof response);
        console.log('是否为数组:', Array.isArray(response));

        let specs = [];

        // 处理不同的响应格式
        if (Array.isArray(response)) {
          // 如果直接返回数组
          console.log('收到直接数组格式的响应');
          specs = response;
        } else if (response && response.code === 0 && Array.isArray(response.data)) {
          // 如果是包装格式
          console.log('收到包装格式的响应');
          specs = response.data;
        } else if (response && Array.isArray(response.data)) {
          // 如果没有code字段但有data数组
          console.log('收到无code字段的包装格式响应');
          specs = response.data;
        } else {
          console.warn(`分类 ${category.code} 的规格列表为空或响应格式不正确，响应:`, response);
          specs = [];
        }

        console.log(`分类 ${category.code} 的规格数量:`, specs.length);
        console.log('规格数据:', specs);

        // 更新分类的规格列表
        this.$set(this.categories[index], 'specs', specs);
        console.log(`已更新分类 ${category.code} 的规格列表`);

        // 更新 nextSpecId
        if (specs.length > 0) {
          const maxId = Math.max(...specs.map(spec => spec.id));
          this.nextSpecId = maxId + 1;
          console.log(`更新 nextSpecId 为 ${this.nextSpecId}`);
        }

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        console.error(`获取分类 ${category.code} 的规格列表错误:`, error);
        this.$set(this.categories[index], 'specs', []);

        uni.showToast({
          title: `加载规格失败: ${error.message || '未知错误'}`,
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 切换分类展开/折叠状态
    async toggleCategory(index) {
      console.log(`切换分类 ${this.categories[index].code} 的展开状态`);
      const expanded = !this.categories[index].expanded;
      this.$set(this.categories[index], 'expanded', expanded);

      // 如果展开，则加载规格列表（无论是否为空）
      if (expanded) {
        console.log(`分类 ${this.categories[index].code} 已展开，加载规格列表`);
        await this.loadCategorySpecs(this.categories[index], index);
      }
    },

    // 显示添加分类弹窗
    showAddCategoryModal() {
      console.log('点击添加分类按钮');

      try {
        this.isEditingCategory = false;
        this.currentCategory = {
          code: '',
          name: '',
          description: '',
          specs: []
        };

        console.log('设置 showCategoryModal = true');
        this.showCategoryModal = true;

        // 强制更新
        this.$forceUpdate();

        // 打印当前状态
        console.log('当前 showCategoryModal 状态:', this.showCategoryModal);
      } catch (error) {
        console.error('显示添加分类弹窗失败:', error);
        uni.showToast({
          title: '显示添加分类弹窗失败',
          icon: 'none'
        });
      }
    },

    // 显示编辑分类弹窗
    editCategory(category) {
      this.isEditingCategory = true;
      this.currentCategory = JSON.parse(JSON.stringify(category));
      this.showCategoryModal = true;
    },

    // 关闭分类弹窗
    closeCategoryModal() {
      this.showCategoryModal = false;
    },

    // 删除分类
    async deleteCategory(category) {
      if (!category || !category.id) {
        uni.showToast({
          title: '分类数据不完整，无法删除',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '确认删除',
        content: `确定要删除分类“${category.name}”吗？删除后将无法恢复。`,
        success: async res => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中...',
              mask: true
            });

            try {
              await batteryAPI.deleteBatteryCategory(category.id);

              // 从本地列表中删除
              const index = this.categories.findIndex(c => c.id === category.id);
              if (index !== -1) {
                this.categories.splice(index, 1);
              }

              uni.showToast({
                title: '分类删除成功',
                icon: 'success'
              });
            } catch (error) {
              console.error('删除分类错误:', error);
              uni.showToast({
                title: error.message || '删除分类失败',
                icon: 'none',
                duration: 3000
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
      });
    },

    // 保存分类
    async saveCategory() {
      if (!this.currentCategory.name) {
        uni.showToast({
          title: '请输入分类名称',
          icon: 'none'
        });
        return;
      }

      if (!this.currentCategory.code) {
        uni.showToast({
          title: '请输入分类代码',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '保存中...',
        mask: true
      });

      try {
        if (this.isEditingCategory) {
          // 编辑现有分类
          const response = await batteryAPI.updateBatteryCategory(this.currentCategory.id, {
            name: this.currentCategory.name
          });

          console.log('更新分类响应:', response);

          // 更新本地分类列表
          const index = this.categories.findIndex(c => c.id === this.currentCategory.id);
          if (index !== -1) {
            this.$set(this.categories, index, {
              ...this.categories[index],
              name: this.currentCategory.name
            });
          }

          uni.showToast({
            title: '分类更新成功',
            icon: 'success'
          });
        } else {
          // 添加新分类
          const response = await batteryAPI.createBatteryCategory({
            code: this.currentCategory.code,
            name: this.currentCategory.name,
            displayOrder: 0,
            isActive: true
          });

          console.log('创建分类响应:', response);

          // 添加到本地分类列表
          this.categories.push({
            ...response,
            expanded: false,
            specs: []
          });

          uni.showToast({
            title: '分类添加成功',
            icon: 'success'
          });
        }

        this.closeCategoryModal();
      } catch (error) {
        console.error('保存分类错误:', error);
        uni.showToast({
          title: error.message || '保存分类失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 显示添加规格弹窗
    showAddSpecModal(category) {
      console.log('点击添加规格按钮，分类:', category);

      try {
        // 确保分类数据完整
        if (!category || !category.id || !category.code) {
          uni.showToast({
            title: '分类数据不完整，无法添加规格',
            icon: 'none'
          });
          return;
        }

        // 重置编辑状态
        this.isEditingSpec = false;

        // 设置当前分类
        this.currentSpecCategory = {
          id: category.id,
          code: category.code,
          name: category.name || '未命名分类'
        };
        console.log('设置当前分类:', this.currentSpecCategory);

        // 初始化规格数据
        this.currentSpec = {
          id: 0,
          name: '',
          price: 0,
          categoryId: category.id
        };

        console.log('设置 showSpecModal = true');
        // 直接显示弹窗
        this.showSpecModal = true;

        // 强制更新
        this.$forceUpdate();

        // 打印当前状态
        console.log('当前 showSpecModal 状态:', this.showSpecModal);
      } catch (error) {
        console.error('显示添加规格弹窗失败:', error);
        uni.showToast({
          title: '显示添加规格弹窗失败',
          icon: 'none'
        });
      }
    },

    // 显示编辑规格弹窗
    editSpec(category, spec) {
      try {
        // 确保分类和规格数据完整
        if (!category || !category.id || !spec || !spec.id) {
          uni.showToast({
            title: '数据不完整，无法编辑规格',
            icon: 'none'
          });
          return;
        }

        // 设置编辑状态
        this.isEditingSpec = true;

        // 设置当前分类
        this.currentSpecCategory = {
          id: category.id,
          code: category.code,
          name: category.name || '未命名分类'
        };

        // 只保留需要的规格数据
        this.currentSpec = {
          id: spec.id,
          name: spec.name,
          price: spec.price || 0,
          categoryId: category.id
        };

        // 显示弹窗
        this.showSpecModal = true;
      } catch (error) {
        uni.showToast({
          title: '显示编辑规格弹窗失败',
          icon: 'none'
        });
      }
    },

    // 关闭规格弹窗
    closeSpecModal() {
      this.showSpecModal = false;
    },



    // 保存规格
    async saveSpec() {
      // 验证必填字段
      if (!this.currentSpec.name) {
        uni.showToast({
          title: '请输入规格名称',
          icon: 'none'
        });
        return;
      }

      // 验证分类ID
      if (!this.currentSpecCategory.id) {
        uni.showToast({
          title: '分类ID不能为空',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '保存中...',
        mask: true
      });

      try {
        // 准备请求数据
        const specData = {
          name: this.currentSpec.name,
          price: parseFloat(this.currentSpec.price) || 0,
          categoryId: this.currentSpecCategory.id
        };

        let response;

        if (this.isEditingSpec) {
          // 编辑现有规格
          response = await batteryAPI.updateBatterySpec(this.currentSpec.id, specData);

          // 更新本地规格列表
          const categoryIndex = this.categories.findIndex(c => c.id === this.currentSpecCategory.id);
          if (categoryIndex !== -1) {
            const specIndex = this.categories[categoryIndex].specs.findIndex(s => s.id === this.currentSpec.id);
            if (specIndex !== -1) {
              const updatedSpec = {
                ...this.categories[categoryIndex].specs[specIndex],
                ...specData,
                id: this.currentSpec.id
              };
              this.$set(this.categories[categoryIndex].specs, specIndex, updatedSpec);
            }
          }

          uni.showToast({
            title: '规格更新成功',
            icon: 'success'
          });
        } else {
          // 添加新规格
          response = await batteryAPI.createBatterySpec(specData);

          // 添加到本地规格列表
          const categoryIndex = this.categories.findIndex(c => c.id === this.currentSpecCategory.id);
          if (categoryIndex !== -1) {
            if (!this.categories[categoryIndex].specs) {
              this.$set(this.categories[categoryIndex], 'specs', []);
            }

            this.categories[categoryIndex].specs.push(response);

            // 更新 nextSpecId
            if (response.id) {
              this.nextSpecId = Math.max(this.nextSpecId, response.id + 1);
            }
          }

          uni.showToast({
            title: '规格添加成功',
            icon: 'success'
          });
        }

        // 关闭弹窗
        this.closeSpecModal();

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        // 显示错误信息
        let errorMessage = '保存规格失败';
        if (error.message) {
          errorMessage += ': ' + error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 删除规格
    async deleteSpec(category, spec) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除规格"${spec.name}"吗？`,
        success: async res => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中...',
              mask: true
            });

            try {
              await batteryAPI.deleteBatterySpec(spec.id);

              // 从本地规格列表中删除
              const categoryIndex = this.categories.findIndex(c => c.id === category.id);
              if (categoryIndex !== -1) {
                const specIndex = this.categories[categoryIndex].specs.findIndex(s => s.id === spec.id);
                if (specIndex !== -1) {
                  this.categories[categoryIndex].specs.splice(specIndex, 1);
                }
              }

              uni.showToast({
                title: '规格删除成功',
                icon: 'success'
              });
            } catch (error) {
              uni.showToast({
                title: error.message || '删除规格失败',
                icon: 'none'
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-wrapper {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

.popup-content {
  width: 600rpx;
  padding: 0;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.spec-popup-content {
  width: 650rpx;
  max-height: 90vh;
}

.popup-title {
  padding: 24rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.popup-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
  background-color: #fff;

  ::v-deep .u-form-item {
    margin-bottom: 20rpx;
  }
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
  background-color: #f8f8f8;

  .btn {
    padding: 0 30rpx;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    border-radius: 4rpx;
    margin-left: 20rpx;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-cancel {
    background-color: #f2f2f2;
    color: #666;
    border: 1rpx solid #ddd;
  }

  .btn-primary {
    background-color: #2979ff;
    color: #fff;
  }
}

.category-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
  padding-top: 20rpx;
  box-sizing: border-box;
}



.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 0;

  .loading-text {
    font-size: 28rpx;
    color: #666;
    margin-top: 24rpx;
    letter-spacing: 1px;
  }
}

.category-list {
  padding: 30rpx 20rpx;
}

.category-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.03);

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  }
}

.category-header {
  display: flex;
  align-items: center;
  padding: 36rpx 30rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
  transition: background-color 0.2s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .category-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.5px;
  }

  .category-code {
    font-size: 24rpx;
    color: #888;
    margin-left: 12rpx;
    background-color: #f0f2f5;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
  }

  .category-actions {
    margin-left: auto;
    display: flex;
    align-items: center;

    .action-btn {
      min-width: 70rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6rpx;
      transition: all 0.2s ease;
      margin-right: 10rpx;
      padding: 0 12rpx;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

      text {
        font-size: 22rpx;
        transition: all 0.2s ease;
        display: inline-block;
        text-align: center;
        font-weight: 500;
        letter-spacing: 1rpx;
      }

      &.edit-btn {
        background: rgba(41, 121, 255, 0.1);
        border: 1rpx solid rgba(41, 121, 255, 0.3);

        text {
          color: #2979ff;
        }

        &:active {
          background: rgba(41, 121, 255, 0.2);
        }
      }

      &.delete-btn {
        background: rgba(255, 59, 48, 0.1);
        border: 1rpx solid rgba(255, 59, 48, 0.3);

        text {
          color: #ff3b30;
        }

        &:active {
          background: rgba(255, 59, 48, 0.2);
        }
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .toggle-btn {
      width: 70rpx;
      height: 70rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }

      .iconfont {
        font-size: 24rpx;
        color: #555;
      }
    }

    .toggle-btn .iconfont {
      color: #666;
    }
  }
}

.specs-list {
  background-color: #f8fafc;
  padding: 24rpx 30rpx 36rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.specs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  text {
    font-size: 28rpx;
    color: #444;
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    padding-left: 16rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 24rpx;
      background-color: #2979ff;
      border-radius: 3rpx;
    }
  }

  .add-spec-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(41, 121, 255, 0.1);
    color: #2979ff;
    padding: 14rpx 28rpx;
    border-radius: 40rpx;
    font-size: 26rpx;
    box-shadow: 0 2rpx 8rpx rgba(41, 121, 255, 0.15);
    transition: all 0.2s ease;
    z-index: 10;
    cursor: pointer;
    position: relative;
    letter-spacing: 0.5px;
    border: 1rpx solid rgba(41, 121, 255, 0.3);

    text:first-child {
      font-size: 32rpx;
      margin-right: 8rpx;
      font-weight: bold;
      line-height: 1;
    }

    &:active {
      opacity: 0.9;
      transform: scale(0.98);
      box-shadow: 0 2rpx 6rpx rgba(41, 121, 255, 0.15);
      background: rgba(41, 121, 255, 0.2);
    }
  }
}

.specs-table {
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.specs-table-header {
  display: flex;
  background: linear-gradient(to right, #f5f7fa, #f0f2f5);
  font-weight: 600;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
}

.specs-table-row {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  min-height: 80rpx;
  align-items: center;
  padding: 10rpx 0;

  &:first-child {
    border-top: none;
  }

  &:hover {
    background-color: rgba(41, 121, 255, 0.05);
  }
}

.specs-table-cell {
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.id-cell {
    width: 100rpx;
    flex-shrink: 0;
    color: #666;
    justify-content: center;
    font-family: monospace;
    background-color: rgba(0, 0, 0, 0.02);
  }

  &.name-cell {
    flex: 1;
    font-weight: 500;
  }

  &.price-cell {
    width: 120rpx;
    color: #ff6b00;
    font-weight: 500;
    text-align: right;
    padding-right: 20rpx;
  }

  &.action-cell {
    width: 180rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10rpx;
    padding-right: 20rpx;
    padding-bottom: 0;
  }
}

.spec-action {
  min-width: 70rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  transition: all 0.2s ease;
  margin-left: 10rpx;
  position: relative;
  overflow: visible;
  padding: 0 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  text {
    font-size: 22rpx;
    transition: all 0.2s ease;
    display: inline-block;
    text-align: center;
    font-weight: 500;
    letter-spacing: 1rpx;
  }

  &.edit-btn {
    background: rgba(41, 121, 255, 0.1);
    border: 1rpx solid rgba(41, 121, 255, 0.3);

    text {
      color: #2979ff;
    }

    &:active {
      background: rgba(41, 121, 255, 0.2);
    }
  }

  &.delete-btn {
    background: rgba(255, 59, 48, 0.1);
    border: 1rpx solid rgba(255, 59, 48, 0.3);

    text {
      color: #ff3b30;
    }

    &:active {
      background: rgba(255, 59, 48, 0.2);
    }
  }

  &:active {
    transform: scale(0.95);

    .iconfont {
      transform: scale(0.9);
    }
  }
}

.empty-specs, .empty-categories {
  padding: 60rpx 0;
  text-align: center;
  color: #888;
  font-size: 28rpx;
  letter-spacing: 1px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &::before {
    content: '\e67c'; // 使用一个空状态图标的 Unicode
    font-family: 'iconfont';
    font-size: 80rpx;
    color: #ccc;
    margin-bottom: 20rpx;
    opacity: 0.6;
  }
}

.add-category-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(41, 121, 255, 0.1);
  color: #2979ff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 320rpx;
  height: 90rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.15);
  z-index: 100;
  cursor: pointer;
  letter-spacing: 1px;
  border: 1rpx solid rgba(41, 121, 255, 0.3);
  transition: all 0.3s ease;

  text:first-child {
    font-size: 40rpx;
    margin-right: 12rpx;
    font-weight: bold;
    transition: all 0.3s ease;
    line-height: 1;
  }

  text {
    font-size: 30rpx;
    font-weight: 500;
  }

  &:active {
    opacity: 0.95;
    transform: translateX(-50%) scale(0.97);
    box-shadow: 0 2rpx 8rpx rgba(41, 121, 255, 0.15);
    background: rgba(41, 121, 255, 0.2);

    text:first-child {
      transform: rotate(90deg);
    }
  }
}


</style>
