using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace BatteryApi.DTOs.Battery
{
    /// <summary>
    /// 创建电池分类请求
    /// </summary>
    public class CreateBatteryCategoryRequest
    {
        /// <summary>
        /// 分类代码
        /// </summary>
        [Required(ErrorMessage = "分类代码不能为空")]
        [StringLength(20, ErrorMessage = "分类代码长度不能超过20个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称
        /// </summary>
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(50, ErrorMessage = "分类名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Range(0, 9999, ErrorMessage = "显示顺序必须在0到9999之间")]
        public int DisplayOrder { get; set; } = 0;
    }

    /// <summary>
    /// 更新电池分类请求
    /// </summary>
    public class UpdateBatteryCategoryRequest
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(50, ErrorMessage = "分类名称长度不能超过50个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 显示顺序
        /// </summary>
        [Range(0, 9999, ErrorMessage = "显示顺序必须在0到9999之间")]
        public int DisplayOrder { get; set; } = 0;
    }

    /// <summary>
    /// 创建电池规格请求
    /// </summary>
    public class CreateBatterySpecRequest
    {
        /// <summary>
        /// 规格名称
        /// </summary>
        [Required(ErrorMessage = "规格名称不能为空")]
        [StringLength(100, ErrorMessage = "规格名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 电压 (V)
        /// </summary>
        [Range(0, 1000, ErrorMessage = "电压必须在0到1000V之间")]
        public decimal Voltage { get; set; } = 0;

        /// <summary>
        /// 容量 (Ah)
        /// </summary>
        [Range(0, 10000, ErrorMessage = "容量必须在0到10000Ah之间")]
        public decimal Capacity { get; set; } = 0;

        /// <summary>
        /// 重量 (kg)
        /// </summary>
        [Range(0, 1000, ErrorMessage = "重量必须在0到1000kg之间")]
        public decimal Weight { get; set; } = 0;

        /// <summary>
        /// 尺寸
        /// </summary>
        [StringLength(50, ErrorMessage = "尺寸长度不能超过50个字符")]
        public string Dimensions { get; set; } = string.Empty;

        /// <summary>
        /// 价格 (元)
        /// </summary>
        [Required(ErrorMessage = "价格不能为空")]
        [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
        public decimal Price { get; set; } = 0;

        /// <summary>
        /// 规格描述
        /// </summary>
        [StringLength(500, ErrorMessage = "规格描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 图片URL
        /// </summary>
        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// 分类ID
        /// </summary>
        [Required(ErrorMessage = "分类ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "分类ID必须大于0")]
        public int CategoryId { get; set; }
    }

    /// <summary>
    /// 更新电池规格请求
    /// </summary>
    public class UpdateBatterySpecRequest
    {
        /// <summary>
        /// 规格名称
        /// </summary>
        [Required(ErrorMessage = "规格名称不能为空")]
        [StringLength(100, ErrorMessage = "规格名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 电压 (V)
        /// </summary>
        [Range(0, 1000, ErrorMessage = "电压必须在0到1000V之间")]
        public decimal Voltage { get; set; } = 0;

        /// <summary>
        /// 容量 (Ah)
        /// </summary>
        [Range(0, 10000, ErrorMessage = "容量必须在0到10000Ah之间")]
        public decimal Capacity { get; set; } = 0;

        /// <summary>
        /// 重量 (kg)
        /// </summary>
        [Range(0, 1000, ErrorMessage = "重量必须在0到1000kg之间")]
        public decimal Weight { get; set; } = 0;

        /// <summary>
        /// 尺寸
        /// </summary>
        [StringLength(50, ErrorMessage = "尺寸长度不能超过50个字符")]
        public string Dimensions { get; set; } = string.Empty;

        /// <summary>
        /// 价格 (元)
        /// </summary>
        [Required(ErrorMessage = "价格不能为空")]
        [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
        public decimal Price { get; set; } = 0;

        /// <summary>
        /// 规格描述
        /// </summary>
        [StringLength(500, ErrorMessage = "规格描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 图片URL
        /// </summary>
        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string ImageUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 创建商品请求
    /// </summary>
    public class CreateProductRequest
    {
        /// <summary>
        /// 规格/型号
        /// </summary>
        [JsonPropertyName("spec")]
        [Required(ErrorMessage = "规格不能为空")]
        [StringLength(100, ErrorMessage = "规格长度不能超过100个字符")]
        public string Spec { get; set; } = string.Empty;

        /// <summary>
        /// 电池状态 (1=可用, 2=租出, 3=维修, 4=报废)
        /// </summary>
        [JsonPropertyName("status")] 
        public string Status { get; set; } 

        /// <summary>
        /// 销售价格 (元)
        /// </summary>
        [JsonPropertyName("price")]
        [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
        public decimal Price { get; set; } = 0;

        /// <summary>
        /// 租赁价格 (元/天)
        /// </summary>
        [JsonPropertyName("rentPrice")]
        [StringLength(20, ErrorMessage = "租赁价格长度不能超过20个字符")]
        public string RentPrice { get; set; } = string.Empty;

        /// <summary>
        /// 生产日期
        /// </summary>
        [JsonPropertyName("manufactureDate")]
        public DateTime? ManufactureDate { get; set; }

        /// <summary>
        /// 使用寿命 (天)
        /// </summary>
        [JsonPropertyName("lifespan")]
        [Range(0, 10000, ErrorMessage = "使用寿命必须在0到10000天之间")]
        public int Lifespan { get; set; } = 0;

        /// <summary>
        /// 产品描述
        /// </summary>
        [JsonPropertyName("description")]
        [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 分类ID
        /// </summary>
        [JsonPropertyName("categoryId")]
        [Required(ErrorMessage = "分类ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "分类ID必须大于0")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        [JsonPropertyName("categoryCode")]
        [StringLength(20, ErrorMessage = "分类代码长度不能超过20个字符")]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称
        /// </summary>
        [JsonPropertyName("categoryName")]
        [StringLength(50, ErrorMessage = "分类名称长度不能超过50个字符")]
        public string CategoryName { get; set; } = string.Empty;

 

        

        /// <summary>
        /// 图片列表（包含主图、详情图、规格图等）- JSON方式
        /// </summary>
        [JsonPropertyName("mainImages")]
        public List<BatteryImageRequest>? MainImages { get; set; }

        /// <summary>
        /// 图片文件列表 - FormData方式（mainImagesFiles 字段直接接收 IFormFile）
        /// </summary>
        public List<IFormFile>? MainImagesFiles { get; set; }

        /// <summary>
        /// 服务列表
        /// </summary>
        [JsonPropertyName("services")]
        public List<BatteryServiceRequest> Services { get; set; } = new List<BatteryServiceRequest>();

        /// <summary>
        /// 安装费用列表
        /// </summary>
        [JsonPropertyName("installationFees")]
        public List<BatteryInstallationFeeRequest> InstallationFees { get; set; } = new List<BatteryInstallationFeeRequest>();

        /// <summary>
        /// 已删除的图片ID列表
        /// </summary>
        [JsonPropertyName("removedImages")]
        public List<long> RemovedImages { get; set; } = new List<long>();
    }

    /// <summary>
    /// 创建电池请求（支持 IFormFile）
    /// </summary>
    public class CreateBatteryWithFilesRequest
    {
        /// <summary>
        /// 规格型号
        /// </summary>
        [Required(ErrorMessage = "规格型号不能为空")]
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string Spec { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        [Required(ErrorMessage = "状态不能为空")]
        [StringLength(50, ErrorMessage = "状态长度不能超过50个字符")]
        public string Status { get; set; } = "Available";

        /// <summary>
        /// 售价
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "售价必须大于等于0")]
        public decimal Price { get; set; }

        /// <summary>
        /// 日租金
        /// </summary>
        [StringLength(50, ErrorMessage = "日租金长度不能超过50个字符")]
        public string RentPrice { get; set; } = "0";

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime ManufactureDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 使用寿命（月）
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "使用寿命必须大于0")]
        public int Lifespan { get; set; } = 36;

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 类别ID
        /// </summary>
        [Required(ErrorMessage = "类别ID不能为空")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 电压
        /// </summary>
        [StringLength(50, ErrorMessage = "电压长度不能超过50个字符")]
        public string Voltage { get; set; } = "0";

        /// <summary>
        /// 容量
        /// </summary>
        [StringLength(50, ErrorMessage = "容量长度不能超过50个字符")]
        public string Capacity { get; set; } = "0";

        /// <summary>
        /// 图片文件列表
        /// </summary>
        public List<IFormFile>? ImageFiles { get; set; }

        /// <summary>
        /// 图片类型
        /// </summary>
        [StringLength(50, ErrorMessage = "图片类型长度不能超过50个字符")]
        public string ImageType { get; set; } = "main";
    }

    /// <summary>
    /// 更新电池请求（支持 IFormFile）
    /// </summary>
    public class UpdateBatteryWithFilesRequest
    {
        /// <summary>
        /// 规格型号
        /// </summary>
        [Required(ErrorMessage = "规格型号不能为空")]
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string Spec { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        [Required(ErrorMessage = "状态不能为空")]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 售价
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "售价必须大于等于0")]
        public decimal Price { get; set; }

        /// <summary>
        /// 日租金
        /// </summary>
        [StringLength(50, ErrorMessage = "日租金长度不能超过50个字符")]
        public string RentPrice { get; set; } = "0";

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ManufactureDate { get; set; }

        /// <summary>
        /// 使用寿命（月）
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "使用寿命必须大于0")]
        public int Lifespan { get; set; } = 36;

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 类别ID
        /// </summary>
        [Required(ErrorMessage = "类别ID不能为空")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 电压
        /// </summary>
        [StringLength(50, ErrorMessage = "电压长度不能超过50个字符")]
        public string Voltage { get; set; } = "0";

        /// <summary>
        /// 容量
        /// </summary>
        [StringLength(50, ErrorMessage = "容量长度不能超过50个字符")]
        public string Capacity { get; set; } = "0";

        /// <summary>
        /// 新增图片文件列表
        /// </summary>
        public List<IFormFile>? ImageFiles { get; set; }

        /// <summary>
        /// 图片类型
        /// </summary>
        [StringLength(50, ErrorMessage = "图片类型长度不能超过50个字符")]
        public string ImageType { get; set; } = "main";

        /// <summary>
        /// 要删除的图片ID列表
        /// </summary>
        public List<int>? RemoveImageIds { get; set; }
    }

    /// <summary>
    /// 电池图片请求
    /// </summary>
    public class BatteryImageRequest
    {
        /// <summary>
        /// 图片ID
        /// </summary>
        [JsonPropertyName("id")]
        public long Id { get; set; }

        /// <summary>
        /// 临时路径
        /// </summary>
        [JsonPropertyName("tempPath")]
        [StringLength(500, ErrorMessage = "临时路径长度不能超过500个字符")]
        public string TempPath { get; set; } = string.Empty;

        /// <summary>
        /// 图片URL
        /// </summary>
        [JsonPropertyName("url")]
        [StringLength(500, ErrorMessage = "图片URL长度不能超过500个字符")]
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// 相对路径
        /// </summary>
        [JsonPropertyName("relativePath")]
        [StringLength(500, ErrorMessage = "相对路径长度不能超过500个字符")]
        public string RelativePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件名
        /// </summary>
        [JsonPropertyName("name")]
        [StringLength(255, ErrorMessage = "文件名长度不能超过255个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小 (字节)
        /// </summary>
        [JsonPropertyName("size")]
        public long Size { get; set; }

        /// <summary>
        /// 上传状态
        /// </summary>
        [JsonPropertyName("status")]
        [StringLength(20, ErrorMessage = "状态长度不能超过20个字符")]
        public string Status { get; set; } = "pending";

        /// <summary>
        /// 是否为主图
        /// </summary>
        [JsonPropertyName("isMain")]
        public bool IsMain { get; set; } = false;

        /// <summary>
        /// 是否为新图片
        /// </summary>
        [JsonPropertyName("isNew")]
        public bool IsNew { get; set; } = true;

        /// <summary>
        /// 图片类型
        /// </summary>
        [JsonPropertyName("imageType")]
        [StringLength(20, ErrorMessage = "图片类型长度不能超过20个字符")]
        public string ImageType { get; set; } = "main";

    
    }

    /// <summary>
    /// 电池服务请求
    /// </summary>
    public class BatteryServiceRequest
    {
        /// <summary>
        /// 服务名称
        /// </summary>
        [JsonPropertyName("name")]
        [StringLength(100, ErrorMessage = "服务名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 服务描述
        /// </summary>
        [JsonPropertyName("description")]
        [StringLength(500, ErrorMessage = "服务描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 电池安装费用请求
    /// </summary>
    public class BatteryInstallationFeeRequest
    {
        /// <summary>
        /// 费用名称
        /// </summary>
        [JsonPropertyName("name")]
        [StringLength(100, ErrorMessage = "费用名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 费用价格
        /// </summary>
        [JsonPropertyName("price")]
        [StringLength(20, ErrorMessage = "费用价格长度不能超过20个字符")]
        public string Price { get; set; } = string.Empty;

        /// <summary>
        /// 费用描述
        /// </summary>
        [JsonPropertyName("description")]
        [StringLength(500, ErrorMessage = "费用描述长度不能超过500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新电池请求
    /// </summary>
    public class UpdateBatteryRequest
    {
        /// <summary>
        /// 规格/型号
        /// </summary>
        [JsonPropertyName("spec")]
        [Required(ErrorMessage = "规格不能为空")]
        [StringLength(100, ErrorMessage = "规格长度不能超过100个字符")]
        public string Spec { get; set; } = string.Empty;

        /// <summary>
        /// 电池状态 (1=可用, 2=租出, 3=维修, 4=报废)
        /// </summary>
        [JsonPropertyName("status")]
        [Range(1, 4, ErrorMessage = "状态必须在1到4之间")]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 销售价格 (元)
        /// </summary>
        [JsonPropertyName("price")]
        [Range(0, 1000000, ErrorMessage = "价格必须在0到1000000元之间")]
        public decimal Price { get; set; } = 0;

        /// <summary>
        /// 租赁价格 (元/天)
        /// </summary>
        [JsonPropertyName("rentPrice")]
        [StringLength(20, ErrorMessage = "租赁价格长度不能超过20个字符")]
        public string RentPrice { get; set; } = string.Empty;

        /// <summary>
        /// 生产日期
        /// </summary>
        [JsonPropertyName("manufactureDate")]
        public DateTime? ManufactureDate { get; set; }

        /// <summary>
        /// 使用寿命 (天)
        /// </summary>
        [JsonPropertyName("lifespan")]
        [Range(0, 10000, ErrorMessage = "使用寿命必须在0到10000天之间")]
        public int Lifespan { get; set; } = 0;

        /// <summary>
        /// 产品描述
        /// </summary>
        [JsonPropertyName("description")]
        [StringLength(1000, ErrorMessage = "描述长度不能超过1000个字符")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 分类ID
        /// </summary>
        [JsonPropertyName("categoryId")]
        [Required(ErrorMessage = "分类ID不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "分类ID必须大于0")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        [JsonPropertyName("categoryCode")]
        [StringLength(20, ErrorMessage = "分类代码长度不能超过20个字符")]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称
        /// </summary>
        [JsonPropertyName("categoryName")]
        [StringLength(50, ErrorMessage = "分类名称长度不能超过50个字符")]
        public string CategoryName { get; set; } = string.Empty;

        
 
        /// <summary>
        /// 主要图片列表 - JSON方式
        /// </summary>
        [JsonPropertyName("mainImages")]
        public List<BatteryImageRequest>? MainImages { get; set; }

        /// <summary>
        /// 图片文件列表 - FormData方式（mainImagesFiles 字段直接接收 IFormFile）
        /// </summary>
        public List<IFormFile>? MainImagesFiles { get; set; }

        /// <summary>
        /// 服务列表
        /// </summary>
        [JsonPropertyName("services")]
        public List<BatteryServiceRequest> Services { get; set; } = new List<BatteryServiceRequest>();

        /// <summary>
        /// 安装费用列表
        /// </summary>
        [JsonPropertyName("installationFees")]
        public List<BatteryInstallationFeeRequest> InstallationFees { get; set; } = new List<BatteryInstallationFeeRequest>();

        /// <summary>
        /// 已删除的图片ID列表
        /// </summary>
        [JsonPropertyName("removedImages")]
        public List<long> RemovedImages { get; set; } = new List<long>();
    }
}
