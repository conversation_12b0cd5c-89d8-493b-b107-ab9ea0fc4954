import Vue from 'vue'
import Vuex from 'vuex'

// 导入各模块
import user from './modules/user'
import battery from './modules/battery'
import product from './modules/product'
import order from './modules/order'
import store from './modules/store'
import chat from './modules/chat'
import inventory from './modules/inventory'
import storeInventory from './modules/storeInventory'
import settings from './modules/settings'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 全局加载状态
    loading: false,
    // 系统配置
    config: {
      // 应用名称
      appName: '电池租售管理系统',
      // 版本号
      version: '1.0.0',
      // API基础URL
      apiBaseUrl: '',
      // 上传文件URL
      uploadUrl: '',
      // 图片基础URL
      imageBaseUrl: ''
    }
  },
  mutations: {
    // 设置加载状态
    SET_LOADING(state, status) {
      state.loading = status
    },
    // 设置系统配置
    SET_CONFIG(state, config) {
      state.config = {
        ...state.config,
        ...config
      }
    }
  },
  actions: {
    // 初始化应用
    initApp({ commit, dispatch, rootGetters }) {
      console.log('开始初始化应用')

      // 检查用户是否已登录
      const isLoggedIn = rootGetters['user/isLoggedIn']
      if (!isLoggedIn) {
        console.log('用户未登录，跳过初始化')
        return Promise.resolve()
      }

      // 用户已登录，初始化应用数据
      console.log('用户已登录，开始加载应用数据')

      // 准备要执行的任务列表
      const tasks = [];

      // 只有在用户已登录时才获取用户信息
      if (isLoggedIn) {
        tasks.push(
          dispatch('user/getCurrentUser').catch(err => {
            console.error('获取用户信息失败:', err)
            // 如果是 401 错误，可能是登录已过期
            if (err && err.statusCode === 401) {
              throw err // 向上传递错误
            }
          })
        );

        // 获取门店列表 - 仅在用户已登录时调用
        tasks.push(
          dispatch('store/getStoreList').catch(err => {
            console.error('获取门店列表失败:', err)
          })
        );
      } else {
        console.log('用户未登录，跳过获取用户信息和门店列表');
      }

      // 获取商品规格列表 - 不需要登录也可以获取
      tasks.push(
        dispatch('product/getSpecList').catch(err => {
          console.error('获取商品规格列表失败:', err)
        })
      );

      // 使用 Promise.allSettled 执行所有任务
      return Promise.allSettled(tasks).then(results => {
        console.log('应用初始化完成，结果:', results)

        // 检查是否有 401 错误
        const hasAuthError = results.some(result =>
          result.status === 'rejected' &&
          result.reason &&
          result.reason.statusCode === 401
        )

        if (hasAuthError) {
          console.error('初始化过程中发现授权错误')
          throw new Error('登录已过期，请重新登录')
        }

        return results
      })
    }
  },
  modules: {
    user,
    battery,
    product,
    order,
    store,
    chat,
    inventory,
    storeInventory,
    settings
  },
  getters: {
    // 全局加载状态
    loading: state => state.loading,
    // 系统配置
    config: state => state.config
  }
})