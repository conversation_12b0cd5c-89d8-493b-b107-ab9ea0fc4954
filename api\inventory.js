/**
 * 库存管理API服务
 */
import request from '@/utils/request';

// API接口
const InventoryAPI = {
  /**
   * 获取库存列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getInventoryList(params = {}) {
    // 构建查询参数，确保提供所有必需的参数
    const queryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      keyword: params.keyword || '', // 提供默认值，避免后端验证错误
      lowStock: params.lowStock || false
    };

    // 添加可选参数
    if (params.storeId) {
      queryParams.storeId = params.storeId;
    }

    // 处理 specIds 参数 - 不再提供默认值，让后端使用自己的默认值
    // 只有当有实际值时才添加此参数
    if (params.specIds && params.specIds.length > 0) {
      queryParams.specIds = params.specIds.join(',');
    }

    console.log('发送到服务器的库存查询参数:', queryParams);

    // 使用新的 Inventory 接口获取库存列表
    return request.get('/api/inventory', queryParams).then(response => {
      console.log('获取库存列表响应:', response);

      // 处理响应数据
      if (response && response.items) {
        return {
          list: response.items,
          total: response.totalItems,
          page: response.page,
          pageSize: response.pageSize
        };
      } else if (Array.isArray(response)) {
        // 如果返回的是数组，转换为分页格式
        return {
          list: response,
          total: response.length,
          page: 1,
          pageSize: response.length
        };
      }

      return {
        list: [],
        total: 0
      };
    }).catch(error => {
      console.error('获取库存列表失败:', error);

      // 如果API调用失败，返回空结果
      return {
        list: [],
        total: 0,
        error: error.message || '获取库存列表失败'
      };
    });
  },

  /**
   * 获取门店库存
   * @param {Number} storeId 门店ID
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getStoreInventory(storeId, params = {}) {
    // 构建查询参数
    const queryParams = { ...params };

    return request.get(`/api/store/${storeId}/inventory`, queryParams).then(response => {
      console.log('获取门店库存响应:', response);

      return {
        list: response,
        total: response.length
      };
    }).catch(error => {
      console.error('获取门店库存失败:', error);

      // API不存在，返回错误信息
      if (error.statusCode === 404) {
        console.error('API不存在，请确保后端API已实现');
        return {
          list: [],
          total: 0,
          error: '门店库存API未实现，请联系管理员'
        };
      }

      // 返回空数据
      return {
        list: [],
        total: 0
      };
    });
  },

  /**
   * 获取门店商品库存
   * @param {Number} storeId 门店ID
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getStoreProductInventory(storeId, params = {}) {
    console.log('获取门店商品库存，门店ID:', storeId, '参数:', params);

    // 移除空参数和'empty'值
    const queryParams = {};
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== 'empty') {
        queryParams[key] = params[key];
      }
    });

    // 添加时间戳，避免缓存
    queryParams._t = Date.now();

    return request.get(`/api/store/${storeId}/product-inventory`, queryParams).then(response => {
      console.log('获取门店商品库存响应:', response);

      // 验证响应
      if (!response) {
        console.warn('门店商品库存响应为空');
        return [];
      }

      if (!Array.isArray(response)) {
        console.warn('门店商品库存响应不是数组:', response);
        return response.data || [];
      }

      return response;
    }).catch(error => {
      console.error('获取门店商品库存失败:', error);
      throw error; // 抛出错误，让调用者处理
    });
  },

  /**
   * 门店进货
   * @param {Object} data 进货数据
   * @returns {Promise} Promise对象
   */
  storeStockIn(data) {
    console.log('门店进货请求数据:', data);

    // 使用新的进货接口
    return request.post('/api/inventory/stock-in', {
      storeId: data.storeId,
      items: data.items.map(item => ({
        spec: item.spec,
        quantity: item.quantity,
        price: item.price,
        remark: item.remark
      }))
    }).then(response => {
      console.log('门店进货响应:', response);

      return {
        success: true,
        message: '进货成功',
        data: response
      };
    }).catch(error => {
      console.error('门店进货失败:', error);

      return {
        success: false,
        message: error.message || '进货失败，请重试',
        error: error
      };
    });
  },

  /**
   * 库存调拨
   * @param {Object} data 调拨数据
   * @returns {Promise} Promise对象
   */
  transferInventory(data) {
    console.log('库存调拨请求数据:', data);

    // 使用新的调拨接口
    return request.post('/api/inventory/transfer', {
      fromStoreId: data.fromStoreId,
      toStoreId: data.toStoreId,
      batterySpec: data.batterySpec,
      quantity: data.quantity,
      remark: data.remark || ''
    }).then(response => {
      console.log('库存调拨响应:', response);

      return {
        success: true,
        message: '调拨成功',
        data: response
      };
    }).catch(error => {
      console.error('库存调拨失败:', error);

      return {
        success: false,
        message: error.message || '调拨失败，请重试',
        error: error
      };
    });
  },

  /**
   * 清理库存
   * @param {Object} data 清理数据
   * @returns {Promise} Promise对象
   */
  clearInventory(data) {
    console.log('清理库存请求数据:', data);

    // 使用清理库存接口
    return request.post('/api/inventory/clear', {
      storeId: data.storeId,
      batterySpec: data.batterySpec,
      quantity: data.quantity,
      reason: data.reason || '',
      remark: data.remark || ''
    }).then(response => {
      console.log('清理库存响应:', response);

      return {
        success: true,
        message: '清理成功',
        data: response
      };
    }).catch(error => {
      console.error('清理库存失败:', error);

      return {
        success: false,
        message: error.message || '清理失败，请重试',
        error: error
      };
    });
  },

  /**
   * 获取库存历史记录
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getInventoryHistory(params = {}) {
    // 构建查询参数
    const queryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 10
    };

    // 添加可选参数
    if (params.storeId) queryParams.storeId = params.storeId;
    if (params.productId) queryParams.productId = params.productId;
    if (params.operationType) queryParams.operationType = params.operationType;
    if (params.startDate) queryParams.startDate = params.startDate;
    if (params.endDate) queryParams.endDate = params.endDate;

    console.log('获取库存历史记录参数:', queryParams);

    return request.get('/api/inventory/history', queryParams).then(response => {
      console.log('获取库存历史记录响应:', response);
      return response;
    }).catch(error => {
      console.error('获取库存历史记录失败:', error);

      // API不存在，返回错误信息
      if (error.statusCode === 404) {
        console.error('API不存在，请确保后端API已实现');
        return {
          items: [],
          totalItems: 0,
          error: '库存历史记录API未实现，请联系管理员'
        };
      }

      throw error;
    });
  },

  /**
   * 商品入库（新版）
   * @param {Object} data 入库数据
   * @returns {Promise} Promise对象
   */
  stockInProduct(data) {
    console.log('商品入库参数:', data);

    return request.post('/api/inventory/product/stock-in', data).then(response => {
      console.log('商品入库响应:', response);
      return response;
    }).catch(error => {
      console.error('商品入库失败:', error);

      // API不存在，返回错误信息
      if (error.statusCode === 404) {
        console.error('API不存在，请确保后端API已实现');
        return {
          success: false,
          message: '商品入库API未实现，请联系管理员',
          error: '商品入库API未实现'
        };
      }

      throw error;
    });
  },

  /**
   * 商品出库
   * @param {Object} data 出库数据
   * @returns {Promise} Promise对象
   */
  stockOutProduct(data) {
    console.log('商品出库参数:', data);

    return request.post('/api/inventory/product/stock-out', data).then(response => {
      console.log('商品出库响应:', response);
      return response;
    }).catch(error => {
      console.error('商品出库失败:', error);

      // API不存在，返回错误信息
      if (error.statusCode === 404) {
        console.error('API不存在，请确保后端API已实现');
        return {
          success: false,
          message: '商品出库API未实现，请联系管理员',
          error: '商品出库API未实现'
        };
      }

      throw error;
    });
  },

  /**
   * 商品调拨
   * @param {Object} data 调拨数据
   * @returns {Promise} Promise对象
   */
  transferProduct(data) {
    console.log('商品调拨参数:', data);

    return request.post('/api/inventory/product/transfer', data).then(response => {
      console.log('商品调拨响应:', response);
      return response;
    }).catch(error => {
      console.error('商品调拨失败:', error);

      // API不存在，返回错误信息
      if (error.statusCode === 404) {
        console.error('API不存在，请确保后端API已实现');
        return {
          success: false,
          message: '商品调拨API未实现，请联系管理员',
          error: '商品调拨API未实现'
        };
      }

      throw error;
    });
  }
};

export default InventoryAPI;
