// 电池模块的状态管理
import batteryAPI from '@/api/battery'

// 计算剩余寿命的辅助函数
function calculateRemainingLife(manufactureDate, lifespan) {
  // 默认寿命为36个月
  const defaultLifespan = 36;

  // 使用提供的寿命或默认值
  const totalLifespan = lifespan || defaultLifespan;

  if (!manufactureDate) {
    return totalLifespan; // 如果没有生产日期，返回完整寿命
  }

  try {
    // 将生产日期转换为 Date 对象
    const mfgDate = new Date(manufactureDate);
    const now = new Date();

    // 检查生产日期是否有效
    if (isNaN(mfgDate.getTime())) {
      console.warn('生产日期无效:', manufactureDate);
      return totalLifespan;
    }

    // 计算生产日期到现在的月数
    const ageInMonths = Math.floor((now - mfgDate) / (30 * 24 * 60 * 60 * 1000));

    // 计算剩余寿命
    const remaining = Math.max(0, totalLifespan - ageInMonths);

    console.log(`生产日期: ${manufactureDate}, 寿命: ${totalLifespan} 月, 已使用: ${ageInMonths} 月, 剩余: ${remaining} 月`);

    return remaining;
  } catch (error) {
    console.error('计算剩余寿命出错:', error);
    return totalLifespan; // 出错时返回完整寿命
  }
}

const state = {
  // 电池列表
  batteryList: [],
  // 电池规格列表
  specList: [],
  // 当前选中的电池
  currentBattery: null,
  // 电池详情加载状态
  detailLoading: false,
  // 电池类别列表
  categoryList: [],
  // 当前类别电池列表
  categoryBatteries: [],
  // 类别电池列表加载状态
  categoryLoading: false,
  // 类别电池列表总数
  categoryBatteryTotal: 0,
  // 类别电池列表当前页
  categoryBatteryPage: 1,
  // 类别电池列表总页数
  categoryBatteryTotalPages: 0
}

const mutations = {
  // 设置电池列表
  SET_BATTERY_LIST(state, list) {
    state.batteryList = list
  },
  // 设置电池规格列表
  SET_SPEC_LIST(state, list) {
    state.specList = list
  },
  // 设置当前选中的电池
  SET_CURRENT_BATTERY(state, battery) {
    state.currentBattery = battery
  },
  // 设置电池详情加载状态
  SET_DETAIL_LOADING(state, status) {
    state.detailLoading = status
  },
  // 设置电池类别列表
  SET_CATEGORY_LIST(state, list) {
    state.categoryList = list
  },
  // 设置当前类别电池列表
  SET_CATEGORY_BATTERIES(state, list) {
    state.categoryBatteries = list
  },
  // 设置类别电池列表加载状态
  SET_CATEGORY_LOADING(state, status) {
    state.categoryLoading = status
  },
  // 设置类别电池列表总数
  SET_CATEGORY_BATTERY_TOTAL(state, total) {
    state.categoryBatteryTotal = total
  },
  // 设置类别电池列表当前页
  SET_CATEGORY_BATTERY_PAGE(state, page) {
    state.categoryBatteryPage = page
  },
  // 设置类别电池列表总页数
  SET_CATEGORY_BATTERY_TOTAL_PAGES(state, totalPages) {
    state.categoryBatteryTotalPages = totalPages
  }
}

const actions = {
  // 获取电池列表
  getBatteryList({ commit, state }, params = {}) {
    console.log('获取电池列表', params);

    // 判断是否是加载更多
    const isLoadMore = params.page && params.page > 1;
    console.log('是否是加载更多:', isLoadMore);

    return new Promise((resolve, reject) => {
      batteryAPI.getBatteries(params)
        .then(response => {
          console.log('获取电池列表原始响应:', JSON.stringify(response));
          console.log('响应类型:', typeof response, '响应结构:', response ? Object.keys(response) : 'null');

          let items = [];
          let total = 0;

          try {
            // 如果响应是数组，直接使用
            if (Array.isArray(response)) {
              console.log('响应是数组，长度:', response.length);
              items = response;
              total = response.length;
            }
            // 如果响应是对象，则检查其结构
            else if (typeof response === 'object' && response !== null) {
              // 直接检查是否有 items 属性
              if (response.items && Array.isArray(response.items)) {
                console.log('使用 response.items，长度:', response.items.length);

                // 将 API 返回的数据转换为前端需要的格式
                items = response.items.map(item => {
                  // 处理价格字段，确保它们是数字
                  let price = 0;
                  let rentPrice = 0;

                  // 尝试将价格转换为数字
                  if (item.price !== undefined && item.price !== null) {
                    price = parseFloat(item.price);
                    if (isNaN(price)) price = 0;
                  }

                  if (item.rentPrice !== undefined && item.rentPrice !== null) {
                    rentPrice = parseFloat(item.rentPrice);
                    if (isNaN(rentPrice)) rentPrice = 0;
                  }

                  // 处理类别 ID 和名称
                  let categoryId = null;
                  let categoryName = '';
                  let categoryCode = '';

                  if (item.categoryId !== undefined && item.categoryId !== null) {
                    categoryId = item.categoryId;
                  }

                  if (item.categoryName !== undefined && item.categoryName !== null) {
                    categoryName = item.categoryName;
                  }

                  if (item.categoryCode !== undefined && item.categoryCode !== null) {
                    categoryCode = item.categoryCode;
                  }

                  // 处理描述字段
                  let description = '';
                  if (item.description !== undefined && item.description !== null) {
                    description = item.description;
                  }

                  // 处理生产日期
                  let manufactureDate = '未知';
                  if (item.manufactureDate) {
                    try {
                      // 如果是带有时区信息的日期字符串，先提取日期部分
                      let dateStr = item.manufactureDate;
                      if (typeof dateStr === 'string' && dateStr.includes('T')) {
                        dateStr = dateStr.split('T')[0];
                      }

                      // 尝试将日期字符串转换为 Date 对象
                      const date = new Date(dateStr);
                      if (!isNaN(date.getTime())) {
                        // 如果是有效的日期，直接使用提取的日期部分
                        manufactureDate = dateStr;
                        console.log('store中原始生产日期:', item.manufactureDate, '格式化后:', manufactureDate);
                      }
                    } catch (e) {
                      console.error('处理生产日期出错:', e);
                    }
                  }

                  return {
                    id: item.id,
                    code: item.serialNumber || item.code || `BAT-${item.id}`,
                    spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
                    categoryCode: categoryCode || item.categoryCode || categoryId,
                    categoryId: categoryId,
                    categoryName: categoryName,
                    status: batteryAPI._mapStatusFromApi(item.status),
                    price: price,
                    rentPrice: rentPrice,
                    manufactureDate: manufactureDate,
                    lifespan: item.lifespan || 36,
                    remainingLife: item.remainingLife || calculateRemainingLife(manufactureDate, item.lifespan || 36),
                    description: description,
                    notes: item.notes || '',
                    manufacturer: item.manufacturer || '',
                    model: item.model || ''
                  };
                });

                total = response.totalItems || items.length;
              }
              // 如果有 data 属性，则检查 data 的结构
              else if (response.data) {
                if (Array.isArray(response.data)) {
                  console.log('使用 response.data 数组，长度:', response.data.length);
                  items = response.data;
                  total = items.length;
                } else if (response.data.batteries && Array.isArray(response.data.batteries)) {
                  console.log('使用 response.data.batteries，长度:', response.data.batteries.length);
                  items = response.data.batteries;
                  total = response.data.totalItems || items.length;
                } else if (response.data.items && Array.isArray(response.data.items)) {
                  console.log('使用 response.data.items，长度:', response.data.items.length);
                  items = response.data.items;
                  total = response.data.totalItems || items.length;
                }
              }
              // 如果有 code 属性，则检查是否成功
              else if (response.code === 0 && response.data) {
                if (Array.isArray(response.data)) {
                  items = response.data;
                  total = items.length;
                } else if (response.data.batteries && Array.isArray(response.data.batteries)) {
                  items = response.data.batteries;
                  total = response.data.totalItems || items.length;
                } else if (response.data.items && Array.isArray(response.data.items)) {
                  items = response.data.items;
                  total = response.data.totalItems || items.length;
                }
              }
            }

            console.log('处理后的电池列表:', items, '总数量:', total);

            // 检查处理后的数据是否有效
            if (items.length === 0 && response) {
              console.warn('数据处理可能有问题，原始数据有内容但处理后为空');

              // 如果有 items 属性，尝试直接使用
              if (response.items && Array.isArray(response.items) && response.items.length > 0) {
                // 将原始数据转换为前端需要的格式
                items = response.items.map(item => ({
                  id: item.id,
                  code: item.serialNumber || item.code || `BAT-${item.id}`,
                  spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
                  categoryCode: item.categoryCode || item.categoryId,
                  status: batteryAPI._mapStatusFromApi(item.status),
                  price: item.price || 0,
                  rentPrice: item.rentPrice || 0,
                  manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(item.manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  manufacturer: item.manufacturer || '',
                  model: item.model || ''
                }));
                total = response.totalItems || items.length;
                console.log('直接使用原始数据并转换:', items);
              }
              // 如果没有 items 属性但响应本身是数组
              else if (Array.isArray(response) && response.length > 0) {
                // 将原始数据转换为前端需要的格式
                items = response.map(item => ({
                  id: item.id,
                  code: item.serialNumber || item.code || `BAT-${item.id}`,
                  spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
                  categoryCode: item.categoryCode || item.categoryId,
                  status: batteryAPI._mapStatusFromApi(item.status),
                  price: item.price || 0,
                  rentPrice: item.rentPrice || 0,
                  manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(item.manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  manufacturer: item.manufacturer || '',
                  model: item.model || ''
                }));
                total = response.length;
                console.log('直接使用响应数组并转换:', items);
              }
            }
          } catch (error) {
            console.error('处理电池列表数据时出错:', error);
            // 如果处理出错，尝试直接使用原始数据
            if (response) {
              if (response.items && Array.isArray(response.items)) {
                // 将原始数据转换为前端需要的格式
                items = response.items.map(item => ({
                  id: item.id,
                  code: item.serialNumber || item.code || `BAT-${item.id}`,
                  spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
                  categoryCode: item.categoryCode || item.categoryId,
                  status: batteryAPI._mapStatusFromApi(item.status),
                  price: item.price || 0,
                  rentPrice: item.rentPrice || 0,
                  manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(item.manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  manufacturer: item.manufacturer || '',
                  model: item.model || ''
                }));
                total = response.totalItems || items.length;
                console.log('处理出错，直接使用原始数据并转换:', items);
              } else if (Array.isArray(response)) {
                // 将原始数据转换为前端需要的格式
                items = response.map(item => ({
                  id: item.id,
                  code: item.serialNumber || item.code || `BAT-${item.id}`,
                  spec: item.spec || (item.voltage && item.capacity ? `${item.voltage}V${item.capacity}Ah` : '未知规格'),
                  categoryCode: item.categoryCode || item.categoryId,
                  status: batteryAPI._mapStatusFromApi(item.status),
                  price: item.price || 0,
                  rentPrice: item.rentPrice || 0,
                  manufactureDate: item.manufactureDate ? new Date(item.manufactureDate).toISOString().split('T')[0] : '未知',
                  lifespan: item.lifespan || 36,
                  remainingLife: item.remainingLife || calculateRemainingLife(item.manufactureDate, item.lifespan || 36),
                  description: item.description || '',
                  notes: item.notes || '',
                  manufacturer: item.manufacturer || '',
                  model: item.model || ''
                }));
                total = response.length;
                console.log('处理出错，直接使用响应数组并转换:', items);
              }
            }
          }

          if (items.length > 0) {
            // 如果是加载更多，则将新数据追加到现有数据中
            if (isLoadMore) {
              const combinedList = [...state.batteryList, ...items];
              console.log('合并后的列表长度:', combinedList.length);
              commit('SET_BATTERY_LIST', combinedList);
            } else {
              // 如果是首次加载或刷新，直接替换现有数据
              console.log('设置新列表，长度:', items.length);
              commit('SET_BATTERY_LIST', items);
            }

            // 返回包含列表和总数量的对象
            resolve({ list: items, total });
          } else {
            console.warn('电池列表数据格式不正确或为空:', response);

            // 如果不是加载更多，则清空列表
            if (!isLoadMore) {
              commit('SET_BATTERY_LIST', []);
            }

            resolve({ list: [], total: 0 });
          }
        })
        .catch(error => {
          console.error('获取电池列表失败', error);
          reject(error);
        });
    });
  },

  // 获取电池类别列表
  getBatteryCategories({ commit }, params = { keyword: 'empty' }) {
    commit('SET_CATEGORY_LOADING', true);

    console.log('获取电池类别列表，参数:', params);

    return new Promise((resolve, reject) => {
      batteryAPI.getBatteryCategories(params)
        .then(response => {
          console.log('获取电池类别列表响应:', response);

          // 处理响应数据
          if (response && response.code === 0 && Array.isArray(response.data)) {
            // 确保所有分类的ID是数字类型
            const normalizedCategories = response.data.map(category => {
              if (category.id && typeof category.id === 'string') {
                return {
                  ...category,
                  id: parseInt(category.id)
                };
              }
              return category;
            });

            console.log('规范化后的分类列表:', normalizedCategories);

            commit('SET_CATEGORY_LIST', normalizedCategories);
            commit('SET_CATEGORY_LOADING', false);
            resolve(normalizedCategories);
          } else {
            console.warn('电池类别数据格式不正确:', response);
            commit('SET_CATEGORY_LIST', []);
            commit('SET_CATEGORY_LOADING', false);
            resolve([]);
          }
        })
        .catch(error => {
          console.error('获取电池类别列表失败', error);
          commit('SET_CATEGORY_LOADING', false);
          reject(error);
        });
    });
  },

  // 获取电池类别列表 (别名，与 getBatteryCategories 功能相同)
  getCategoryList({ dispatch }, params = { keyword: 'empty' }) {
    console.log('调用 getCategoryList action (别名)，参数:', params);
    return dispatch('getBatteryCategories', params);
  },

  // 获取指定类别的电池列表
  getBatteriesByCategory({ commit }, payload) {
    // 处理不同的调用方式
    let categoryCode = '';
    let params = { pageNumber: 1, pageSize: 15 };

    // 如果 payload 是字符串，则它是 categoryCode
    if (typeof payload === 'string') {
      categoryCode = payload;
    }
    // 如果 payload 是数组，则第一个元素是 categoryCode，第二个元素是 params
    else if (Array.isArray(payload) && payload.length > 0) {
      categoryCode = payload[0];
      if (payload.length > 1 && typeof payload[1] === 'object') {
        params = payload[1];
      }
    }
    // 如果 payload 是对象，则兼容旧的调用方式
    else if (typeof payload === 'object' && payload !== null) {
      params = payload.params || { pageNumber: 1, pageSize: 15 };
      categoryCode = payload.categoryCode || '';
    }

    console.log('获取类别电池列表，类别编码:', categoryCode, '参数:', params);

    commit('SET_CATEGORY_LOADING', true);
    commit('SET_CATEGORY_BATTERY_PAGE', params.pageNumber);

    return new Promise((resolve, reject) => {
      batteryAPI.getBatteriesByCategory(categoryCode, params)
        .then(response => {
          console.log('获取类别电池列表响应:', response);

          // 处理不同的响应格式
          let items = [];
          let totalItems = 0;
          let totalPages = 0;

          // 如果响应是数组，直接使用
          if (Array.isArray(response)) {
            items = response;
            totalItems = response.length;
            totalPages = 1;
          }
          // 如果响应是对象，则检查其结构
          else if (typeof response === 'object' && response !== null) {
            console.log('响应对象结构:', Object.keys(response));

            // 如果有 items 属性，直接使用
            if (Array.isArray(response.items)) {
              console.log('使用 response.items');
              items = response.items;
              totalItems = response.totalItems || items.length;
              totalPages = response.totalPages || 1;
            }
            // 如果有 data 属性，则检查 data 的结构
            else if (response.data) {
              console.log('使用 response.data 结构:', typeof response.data, Array.isArray(response.data));

              if (Array.isArray(response.data)) {
                items = response.data;
                totalItems = items.length;
                totalPages = 1;
              } else if (response.data.batteries && Array.isArray(response.data.batteries)) {
                console.log('使用 response.data.batteries');
                items = response.data.batteries;
                totalItems = response.data.totalItems || items.length;
                totalPages = response.data.totalPages || 1;
              } else if (response.data.items && Array.isArray(response.data.items)) {
                console.log('使用 response.data.items');
                items = response.data.items;
                totalItems = response.data.totalItems || items.length;
                totalPages = response.data.totalPages || 1;
              }
            }
          }

          console.log('处理后的电池列表:', items);

          commit('SET_CATEGORY_BATTERIES', items);
          commit('SET_CATEGORY_BATTERY_TOTAL', totalItems);
          commit('SET_CATEGORY_BATTERY_TOTAL_PAGES', totalPages);
          commit('SET_CATEGORY_LOADING', false);
          resolve(response);
        })
        .catch(error => {
          console.error('获取类别电池列表失败', error);
          commit('SET_CATEGORY_LOADING', false);
          reject(error);
        });
    });
  },

  // 获取电池规格列表
  getSpecList({ commit, dispatch }) {
    return dispatch('getBatterySpecs');
  },

  // 获取电池规格列表 (新方法，用于商品进销存管理)
  getBatterySpecs({ commit, rootGetters }) {
    return new Promise((resolve, reject) => {
      // 检查用户是否已登录
      const isLoggedIn = rootGetters['user/isLoggedIn'];
      if (!isLoggedIn) {
        console.log('用户未登录，使用默认电池规格列表');

        // 使用默认数据
        const defaultSpecList = [
          {
            id: 1,
            name: '48V20Ah',
            categoryId: 1,
            spec: '48V20Ah',
            price: 1500,
            rentPrice: 50
          },
          {
            id: 2,
            name: '60V20Ah',
            categoryId: 1,
            spec: '60V20Ah',
            price: 1800,
            rentPrice: 60
          },
          {
            id: 3,
            name: '72V20Ah',
            categoryId: 1,
            spec: '72V20Ah',
            price: 2100,
            rentPrice: 70
          },
          {
            id: 4,
            name: '48V30Ah',
            categoryId: 1,
            spec: '48V30Ah',
            price: 1700,
            rentPrice: 55
          }
        ];

        commit('SET_SPEC_LIST', defaultSpecList);
        return resolve(defaultSpecList);
      }

      // 调用真实API获取电池规格列表
      batteryAPI.getBatterySpecs()
        .then(response => {
          console.log('获取电池规格列表响应:', response);

          // 处理响应数据
          let specList = [];

          if (response && response.code === 0 && Array.isArray(response.data)) {
            specList = response.data;
          } else if (Array.isArray(response)) {
            specList = response;
          } else if (response && Array.isArray(response.data)) {
            specList = response.data;
          } else {
            console.warn('电池规格数据格式不正确:', response);

            // 使用模拟数据
            specList = [
              {
                id: 1,
                name: '48V20Ah',
                categoryId: 1,
                spec: '48V20Ah',
                price: 1500,
                rentPrice: 50
              },
              {
                id: 2,
                name: '60V20Ah',
                categoryId: 1,
                spec: '60V20Ah',
                price: 1800,
                rentPrice: 60
              },
              {
                id: 3,
                name: '72V20Ah',
                categoryId: 1,
                spec: '72V20Ah',
                price: 2100,
                rentPrice: 70
              },
              {
                id: 4,
                name: '48V30Ah',
                categoryId: 1,
                spec: '48V30Ah',
                price: 1700,
                rentPrice: 55
              }
            ];
          }

          commit('SET_SPEC_LIST', specList);
          resolve(specList);
        })
        .catch(error => {
          console.error('获取电池规格列表失败', error);

          // 出错时使用模拟数据
          const mockData = [
            {
              id: 1,
              name: '48V20Ah',
              categoryId: 1,
              spec: '48V20Ah',
              price: 1500,
              rentPrice: 50
            },
            {
              id: 2,
              name: '60V20Ah',
              categoryId: 1,
              spec: '60V20Ah',
              price: 1800,
              rentPrice: 60
            },
            {
              id: 3,
              name: '72V20Ah',
              categoryId: 1,
              spec: '72V20Ah',
              price: 2100,
              rentPrice: 70
            },
            {
              id: 4,
              name: '48V30Ah',
              categoryId: 1,
              spec: '48V30Ah',
              price: 1700,
              rentPrice: 55
            }
          ];

          commit('SET_SPEC_LIST', mockData);
          resolve(mockData);
        });
    });
  },

  // 根据ID获取电池详情
  getBatteryDetail({ commit }, id) {
    console.log('开始获取电池详情, ID:', id);

    // 检查 ID 是否有效
    if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
      console.error('无效的电池 ID:', id);
      return Promise.reject(new Error('无效的电池 ID'));
    }

    commit('SET_DETAIL_LOADING', true);

    return new Promise((resolve, reject) => {
      batteryAPI.getBatteryDetail(parseInt(id))
        .then(response => {
          console.log('获取电池详情响应:', response);

          // 防止 response 为 undefined
          if (!response) {
            throw new Error('获取电池详情失败，返回数据为空');
          }

          // 处理响应数据
          let batteryData = null;

          if (response.code === 0 && response.data) {
            batteryData = response.data;
          } else if (response.data) {
            // 兼容直接返回数据的情况
            batteryData = response.data;
          } else if (typeof response === 'object' && response !== null) {
            // 兼容直接返回对象的情况
            batteryData = response;
          }

          // 检查返回的数据是否有效
          if (!batteryData || !batteryData.id) {
            console.warn('电池详情数据无效:', batteryData);
            throw new Error('获取电池详情失败，返回数据无效');
          }

          // 添加额外的详情信息
          const storeInfo = {
            id: 1,
            name: '北京海淀门店',
            address: '北京市海淀区中关村大街1号',
            phone: '010-12345678',
            businessHours: '09:00-18:00',
            latitude: 39.9789,
            longitude: 116.3088
          };

          // 确保 batteryData 是一个对象
          batteryData = typeof batteryData === 'object' && batteryData !== null ? batteryData : {};

          const batteryWithDetails = {
            ...batteryData,
            storeInfo,
            hardware: {
              voltage: batteryData.voltage || 0,
              current: 5.2,
              temperature: 25,
              cycles: batteryData.cycleCount || 0,
              health: batteryData.healthPercentage || 95
            }
          };

          commit('SET_CURRENT_BATTERY', batteryWithDetails);
          commit('SET_DETAIL_LOADING', false);
          resolve(batteryWithDetails);
        })
        .catch(error => {
          console.error('获取电池详情失败', error);
          commit('SET_DETAIL_LOADING', false);
          reject(error);
        });
    });
  },

  // 删除电池
  deleteBattery({ commit, state }, id) {
    console.log(`开始删除电池, ID: ${id}`);

    return new Promise(async (resolve, reject) => {
      try {
        // 导入 BatteryAPI
        const batteryAPI = (await import('@/api/battery')).default;

        // 调用API删除电池
        const response = await batteryAPI.deleteBattery(id);
        console.log('删除电池响应:', response);

        // 如果删除成功，从列表中移除该电池
        if (response && (response.code === 0 || response.code === 204 || response === true || (response.data && response.data === true))) {
          // 从state中找到要删除的电池
          const batteryIndex = state.batteryList.findIndex(item => item.id === id);

          if (batteryIndex !== -1) {
            // 创建新的电池列表（不包含要删除的电池）
            const newList = [
              ...state.batteryList.slice(0, batteryIndex),
              ...state.batteryList.slice(batteryIndex + 1)
            ];

            // 更新state
            commit('SET_BATTERY_LIST', newList);

            // 如果当前选中的电池就是要删除的电池，则清空选中状态
            if (state.currentBattery && state.currentBattery.id === id) {
              commit('SET_CURRENT_BATTERY', null);
            }
          } else {
            console.warn(`在列表中找不到 ID 为 ${id} 的电池`);
          }

          resolve({ success: true });
        } else {
          // 如果响应不成功，抛出错误
          throw new Error(response.message || '删除电池失败');
        }
      } catch (error) {
        console.error('删除电池失败:', error);
        reject(new Error('删除电池失败：' + (error.message || '未知错误')));
      }
    });
  },

  // 保存电池（支持 IFormFile 方式）
  async saveBatteryWithFiles({ commit, state }, formData) {
    console.log('调用 saveBatteryWithFiles action');

    try {
      // 导入 BatteryAPI
      const batteryAPI = (await import('@/api/battery')).default;

      // 检查是否为编辑模式（通过 FormData 中的 ID 判断）
      const batteryId = formData.get('id');
      const isEdit = batteryId && parseInt(batteryId) > 0;
      let result;

      if (isEdit) {
        // 更新已有电池
        console.log('更新电池（FormData），ID:', batteryId);
        result = await batteryAPI.updateBatteryWithFiles(batteryId, formData);
      } else {
        // 创建新电池
        console.log('创建新电池（FormData）');
        result = await batteryAPI.createBatteryWithFiles(formData);
      }

      console.log('电池保存成功（FormData）:', result);
      return result;
    } catch (error) {
      console.error('保存电池失败（FormData）:', error);
      throw error;
    }
  },

  // 保存电池 (新增或更新)
  async saveBattery({ commit, state }, batteryData) {
    console.log('调用 saveBattery action，数据:', batteryData);

    try {
      // 导入 BatteryAPI
      const batteryAPI = (await import('@/api/battery')).default;

      const isEdit = batteryData.id > 0;
      let result;

      if (isEdit) {
        // 更新已有电池
        console.log('更新电池，ID:', batteryData.id);

        // 调用API更新电池
        const response = await batteryAPI.updateBattery(batteryData.id, batteryData);
        console.log('更新电池响应:', response);

        if (response.code === 0 && response.data) {
          const updatedBattery = response.data;

          // 查找当前列表中是否存在该电池
          const batteryIndex = state.batteryList.findIndex(item => item.id === updatedBattery.id);

          if (batteryIndex !== -1) {
            // 如果存在，更新列表中的电池
            const newList = [...state.batteryList];
            newList[batteryIndex] = updatedBattery;
            commit('SET_BATTERY_LIST', newList);
          } else {
            // 如果不存在，添加到列表中
            commit('SET_BATTERY_LIST', [...state.batteryList, updatedBattery]);
          }

          // 更新当前电池
          if (state.currentBattery && state.currentBattery.id === updatedBattery.id) {
            commit('SET_CURRENT_BATTERY', updatedBattery);
          }

          result = updatedBattery;
        } else {
          throw new Error(response.message || '更新电池失败');
        }
      } else {
        // 新增电池
        console.log('新增电池');

        // 调用API创建电池
        const response = await batteryAPI.createBattery(batteryData);
        console.log('创建电池响应:', response);

        if (response.code === 0 && response.data) {
          const newBattery = response.data;

          // 更新state
          commit('SET_BATTERY_LIST', [...state.batteryList, newBattery]);

          result = newBattery;
        } else {
          throw new Error(response.message || '创建电池失败');
        }
      }

      return result;
    } catch (error) {
      console.error('保存电池失败:', error);
      throw new Error('保存电池失败: ' + (error.message || '未知错误'));
    }
  },

  // 计算电池价格（根据年限递减）
  calculatePrice({ commit }, { basePrice, manufactureDate }) {
    // 计算电池已使用年限
    const now = new Date()
    const mDate = new Date(manufactureDate)
    const years = now.getFullYear() - mDate.getFullYear()

    // 根据年限递减价格，每年递减10%
    let finalPrice = basePrice
    for (let i = 0; i < years; i++) {
      finalPrice = finalPrice * 0.9
    }

    return finalPrice
  }
}

const getters = {
  // 获取可用电池列表
  availableBatteries: state => state.batteryList.filter(item => item.status === 'Available' || item.status === 1),
  // 根据规格获取电池列表
  getBatteriesBySpec: state => spec => state.batteryList.filter(item => item.spec === spec || item.model === spec),
  // 获取电池规格列表
  specList: state => state.specList,
  // 获取当前选中的电池
  currentBattery: state => state.currentBattery,
  // 获取电池详情加载状态
  detailLoading: state => state.detailLoading,
  // 获取电池类别列表
  categoryList: state => state.categoryList,
  // 获取当前类别电池列表
  categoryBatteries: state => state.categoryBatteries,
  // 获取类别电池列表加载状态
  categoryLoading: state => state.categoryLoading,
  // 获取类别电池列表总数
  categoryBatteryTotal: state => state.categoryBatteryTotal,
  // 获取类别电池列表当前页
  categoryBatteryPage: state => state.categoryBatteryPage,
  // 获取类别电池列表总页数
  categoryBatteryTotalPages: state => state.categoryBatteryTotalPages,
  // 根据类别编码获取类别信息
  getCategoryByCode: state => code => state.categoryList.find(item => item.code === code)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}