using SqlSugar;
using System;

namespace BatteryApi.Models
{
    /// <summary>
    /// 库存清理记录
    /// </summary>
    [SugarTable("inventory_clears")]
    public class InventoryClear
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 门店ID
        /// </summary>
        [SugarColumn(ColumnName = "store_id")]
        public int StoreId { get; set; }

        /// <summary>
        /// 电池ID
        /// </summary>
        [SugarColumn(ColumnName = "battery_id")]
        public int BatteryId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(ColumnName = "product_id")]
        public int ProductId { get; set; }

        /// <summary>
        /// 清理数量
        /// </summary>
        [SugarColumn(ColumnName = "quantity")]
        public int Quantity { get; set; }

        /// <summary>
        /// 清理原因
        /// </summary>
        [SugarColumn(ColumnName = "reason")]
        public string Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "created_at")]
        public DateTime CreatedAt { get; set; }
    }
}
