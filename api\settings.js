/**
 * 系统设置 API
 */
import request from '@/utils/request';

// API 接口
const SettingsAPI = {
  /**
   * 获取所有系统设置
   * @returns {Promise} Promise 对象
   */
  getAllSettings() {
    return request.get('/api/settings');
  },

  /**
   * 获取所有分组的系统设置
   * @returns {Promise} Promise 对象
   */
  getAllGroupedSettings() {
    return request.get('/api/settings/grouped');
  },

  /**
   * 获取指定组的系统设置
   * @param {String} group 设置组
   * @returns {Promise} Promise 对象
   */
  getSettingsByGroup(group) {
    return request.get(`/api/settings/group/${group}`);
  },

  /**
   * 获取指定键的系统设置
   * @param {String} key 设置键
   * @returns {Promise} Promise 对象
   */
  getSettingByKey(key) {
    return request.get(`/api/settings/${key}`);
  },

  /**
   * 创建系统设置
   * @param {Object} data 设置数据
   * @returns {Promise} Promise 对象
   */
  createSetting(data) {
    return request.post('/api/settings', data);
  },

  /**
   * 更新系统设置
   * @param {String} key 设置键
   * @param {Object} data 设置数据
   * @returns {Promise} Promise 对象
   */
  updateSetting(key, data) {
    return request.put(`/api/settings/${key}`, data);
  },

  /**
   * 批量更新系统设置
   * @param {Object} data 设置数据
   * @returns {Promise} Promise 对象
   */
  batchUpdateSettings(data) {
    return request.put('/api/settings/batch', data);
  },

  /**
   * 删除系统设置
   * @param {String} key 设置键
   * @returns {Promise} Promise 对象
   */
  deleteSetting(key) {
    return request.delete(`/api/settings/${key}`);
  },

  /**
   * 更新用户管理设置
   * @param {Object} data 用户管理设置数据
   * @returns {Promise} Promise 对象
   */
  updateUserManagementSettings(data) {
    return request.put('/api/settings/user-management', data);
  },

  /**
   * 更新支付设置
   * @param {Object} data 支付设置数据
   * @returns {Promise} Promise 对象
   */
  updatePaymentSettings(data) {
    return request.put('/api/settings/payment', data);
  },

  /**
   * 更新租赁设置
   * @param {Object} data 租赁设置数据
   * @returns {Promise} Promise 对象
   */
  updateRentalSettings(data) {
    return request.put('/api/settings/rental', data);
  },

  /**
   * 更新系统维护设置
   * @param {Object} data 系统维护设置数据
   * @returns {Promise} Promise 对象
   */
  updateMaintenanceSettings(data) {
    return request.put('/api/settings/maintenance', data);
  },

  /**
   * 备份数据
   * @returns {Promise} Promise 对象
   */
  backupData() {
    return request.post('/api/settings/backup');
  },

  /**
   * 清除缓存
   * @returns {Promise} Promise 对象
   */
  clearCache() {
    return request.post('/api/settings/clear-cache');
  }
};

export default SettingsAPI;
