using BatteryApi.DTOs.Product;
using BatteryApi.DTOs.Common;

namespace BatteryApi.Services.Interfaces;

/// <summary>
/// 商品服务接口
/// </summary>
public interface IProductService
{
    /// <summary>
    /// 创建商品
    /// </summary>
    /// <param name="request">创建商品请求</param>
    /// <returns>创建的商品信息</returns>
    Task<ProductDto> CreateProductAsync(CreateProductRequest request);

    /// <summary>
    /// 更新商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <param name="request">更新商品请求</param>
    /// <returns>更新的商品信息</returns>
    Task<ProductDto> UpdateProductAsync(int id, UpdateProductRequest request);

    /// <summary>
    /// 根据ID获取商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>商品信息</returns>
    Task<ProductDto?> GetProductByIdAsync(int id);

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页数量</param>
    /// <param name="categoryId">分类ID</param>
    /// <param name="keyword">搜索关键词</param>
    /// <param name="status">状态</param>
    /// <returns>商品列表</returns>
    Task<PagedResult<ProductDto>> GetProductsAsync(int page, int pageSize, int? categoryId = null, string? keyword = null, string? status = null);

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="id">商品ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteProductAsync(int id);

    /// <summary>
    /// 处理商品图片
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="images">图片列表</param>
    /// <returns>处理结果</returns>
    Task ProcessProductImagesAsync(int productId, List<ProductImageRequest> images);

    /// <summary>
    /// 处理商品图片（从文件）
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="files">文件列表</param>
    /// <param name="imageType">图片类型</param>
    /// <returns>处理结果</returns>
    Task ProcessProductImagesFromFilesAsync(int productId, List<IFormFile> files, string imageType);

    /// <summary>
    /// 处理商品服务
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="services">服务列表</param>
    /// <returns>处理结果</returns>
    Task ProcessProductServicesAsync(int productId, List<ProductServiceRequest> services);

    /// <summary>
    /// 处理商品安装费用
    /// </summary>
    /// <param name="productId">商品ID</param>
    /// <param name="installationFees">安装费用列表</param>
    /// <returns>处理结果</returns>
    Task ProcessProductInstallationFeesAsync(int productId, List<ProductInstallationFeeRequest> installationFees);

    /// <summary>
    /// 检查商品是否重复
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <param name="spec">规格</param>
    /// <param name="excludeId">排除的商品ID</param>
    /// <returns>是否重复</returns>
    Task<bool> CheckProductDuplicateAsync(int categoryId, string spec, int? excludeId = null);
}
