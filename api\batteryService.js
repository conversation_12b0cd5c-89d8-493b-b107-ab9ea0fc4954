import request from '@/utils/request'

/**
 * 商品服务管理API
 */

// 获取商品的所有服务
export function getBatteryServices(batteryId) {
  return request({
    url: `/batteries/${batteryId}/services`,
    method: 'get'
  })
}

// 创建商品服务
export function createBatteryService(batteryId, data) {
  return request({
    url: `/batteries/${batteryId}/services`,
    method: 'post',
    data
  })
}

// 更新商品服务
export function updateBatteryService(id, data) {
  return request({
    url: `/batteries/services/${id}`,
    method: 'put',
    data
  })
}

// 删除商品服务
export function deleteBatteryService(id) {
  return request({
    url: `/batteries/services/${id}`,
    method: 'delete'
  })
}

// 批量创建商品服务
export function batchCreateBatteryServices(batteryId, services) {
  const promises = services.map(service => 
    createBatteryService(batteryId, service)
  )
  return Promise.all(promises)
}

// 批量更新商品服务（先删除所有，再重新创建）
export async function batchUpdateBatteryServices(batteryId, services) {
  try {
    // 1. 获取现有服务
    const existingServices = await getBatteryServices(batteryId)
    
    // 2. 删除现有服务
    if (existingServices.data && existingServices.data.length > 0) {
      const deletePromises = existingServices.data.map(service => 
        deleteBatteryService(service.id)
      )
      await Promise.all(deletePromises)
    }
    
    // 3. 创建新服务
    if (services && services.length > 0) {
      return await batchCreateBatteryServices(batteryId, services)
    }
    
    return { code: 200, message: '服务更新成功', data: [] }
  } catch (error) {
    console.error('批量更新服务失败:', error)
    throw error
  }
}

/**
 * 商品安装费用管理API
 */

// 获取商品的所有安装费用
export function getBatteryInstallationFees(batteryId) {
  return request({
    url: `/batteries/${batteryId}/installation-fees`,
    method: 'get'
  })
}

// 创建商品安装费用
export function createBatteryInstallationFee(batteryId, data) {
  return request({
    url: `/batteries/${batteryId}/installation-fees`,
    method: 'post',
    data
  })
}

// 更新商品安装费用
export function updateBatteryInstallationFee(id, data) {
  return request({
    url: `/batteries/installation-fees/${id}`,
    method: 'put',
    data
  })
}

// 删除商品安装费用
export function deleteBatteryInstallationFee(id) {
  return request({
    url: `/batteries/installation-fees/${id}`,
    method: 'delete'
  })
}

// 批量创建商品安装费用
export function batchCreateBatteryInstallationFees(batteryId, fees) {
  const promises = fees.map(fee => 
    createBatteryInstallationFee(batteryId, fee)
  )
  return Promise.all(promises)
}

// 批量更新商品安装费用（先删除所有，再重新创建）
export async function batchUpdateBatteryInstallationFees(batteryId, fees) {
  try {
    // 1. 获取现有安装费用
    const existingFees = await getBatteryInstallationFees(batteryId)
    
    // 2. 删除现有安装费用
    if (existingFees.data && existingFees.data.length > 0) {
      const deletePromises = existingFees.data.map(fee => 
        deleteBatteryInstallationFee(fee.id)
      )
      await Promise.all(deletePromises)
    }
    
    // 3. 创建新安装费用
    if (fees && fees.length > 0) {
      return await batchCreateBatteryInstallationFees(batteryId, fees)
    }
    
    return { code: 200, message: '安装费用更新成功', data: [] }
  } catch (error) {
    console.error('批量更新安装费用失败:', error)
    throw error
  }
}

/**
 * 组合操作：保存商品时同时保存服务和安装费用
 */
export async function saveBatteryServicesAndFees(batteryId, services, installationFees) {
  try {
    const results = {}
    
    // 保存服务
    if (services && services.length > 0) {
      console.log('保存商品服务:', services)
      results.services = await batchUpdateBatteryServices(batteryId, services)
    }
    
    // 保存安装费用
    if (installationFees && installationFees.length > 0) {
      console.log('保存安装费用:', installationFees)
      results.installationFees = await batchUpdateBatteryInstallationFees(batteryId, installationFees)
    }
    
    return {
      code: 200,
      message: '服务和安装费用保存成功',
      data: results
    }
  } catch (error) {
    console.error('保存服务和安装费用失败:', error)
    throw error
  }
}

/**
 * 数据验证工具函数
 */

// 验证服务数据
export function validateServiceData(service) {
  if (!service.name || service.name.trim() === '') {
    return { valid: false, message: '服务名称不能为空' }
  }
  
  if (service.name.length > 100) {
    return { valid: false, message: '服务名称不能超过100个字符' }
  }
  
  if (service.description && service.description.length > 500) {
    return { valid: false, message: '服务描述不能超过500个字符' }
  }
  
  return { valid: true, message: '验证通过' }
}

// 验证安装费用数据
export function validateInstallationFeeData(fee) {
  if (!fee.name || fee.name.trim() === '') {
    return { valid: false, message: '安装费用名称不能为空' }
  }
  
  if (fee.name.length > 100) {
    return { valid: false, message: '安装费用名称不能超过100个字符' }
  }
  
  if (fee.price < 0) {
    return { valid: false, message: '安装费用价格不能为负数' }
  }
  
  if (fee.description && fee.description.length > 500) {
    return { valid: false, message: '安装费用描述不能超过500个字符' }
  }
  
  return { valid: true, message: '验证通过' }
}

// 批量验证服务数据
export function validateServicesData(services) {
  if (!Array.isArray(services)) {
    return { valid: false, message: '服务数据必须是数组' }
  }
  
  for (let i = 0; i < services.length; i++) {
    const validation = validateServiceData(services[i])
    if (!validation.valid) {
      return { valid: false, message: `第${i + 1}个服务: ${validation.message}` }
    }
  }
  
  return { valid: true, message: '所有服务数据验证通过' }
}

// 批量验证安装费用数据
export function validateInstallationFeesData(fees) {
  if (!Array.isArray(fees)) {
    return { valid: false, message: '安装费用数据必须是数组' }
  }
  
  for (let i = 0; i < fees.length; i++) {
    const validation = validateInstallationFeeData(fees[i])
    if (!validation.valid) {
      return { valid: false, message: `第${i + 1}个安装费用: ${validation.message}` }
    }
  }
  
  return { valid: true, message: '所有安装费用数据验证通过' }
}

export default {
  // 服务相关
  getBatteryServices,
  createBatteryService,
  updateBatteryService,
  deleteBatteryService,
  batchCreateBatteryServices,
  batchUpdateBatteryServices,
  
  // 安装费用相关
  getBatteryInstallationFees,
  createBatteryInstallationFee,
  updateBatteryInstallationFee,
  deleteBatteryInstallationFee,
  batchCreateBatteryInstallationFees,
  batchUpdateBatteryInstallationFees,
  
  // 组合操作
  saveBatteryServicesAndFees,
  
  // 验证工具
  validateServiceData,
  validateInstallationFeeData,
  validateServicesData,
  validateInstallationFeesData
}
