import request from '@/utils/request'

/**
 * 电池图片上传 API
 */
export default {
  /**
   * 通用图片上传方法
   * @param {File} file 图片文件
   * @param {number} productId 商品ID
   * @param {string} imageType 图片类型（main/detail/spec/gallery）
   * @returns {Promise} Promise对象
   */
  uploadImage(file, productId, imageType = 'main') {
    console.log('上传商品图片:', file.name, 'productId:', productId, 'imageType:', imageType);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('imageType', imageType);

    return request.post('/api/batteryimage/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 上传商品主图片（兼容性方法）
   * @param {File} file 图片文件
   * @param {number} productId 商品ID
   * @returns {Promise} Promise对象
   */
  uploadMainImage(file, productId) {
    return this.uploadImage(file, productId, 'main');
  },

  /**
   * 上传商品详情图片（兼容性方法）
   * @param {File} file 图片文件
   * @param {number} productId 商品ID
   * @returns {Promise} Promise对象
   */
  uploadDetailImage(file, productId) {
    return this.uploadImage(file, productId, 'detail');
  },

  /**
   * 上传商品规格图片（兼容性方法）
   * @param {File} file 图片文件
   * @param {number} productId 商品ID
   * @returns {Promise} Promise对象
   */
  uploadSpecImage(file, productId) {
    return this.uploadImage(file, productId, 'spec');
  },

  /**
   * 批量上传商品图片
   * @param {Object} images 图片对象 { mainImage, detailImage, specImage }
   * @param {number} productId 商品ID（必需，用于组织图片存储路径）
   * @returns {Promise} Promise对象
   */
  uploadBatchImages(images, productId) {
    console.log('批量上传商品图片:', images, 'productId:', productId);

    const formData = new FormData();

    if (images.mainImage) {
      formData.append('mainImage', images.mainImage);
    }
    if (images.detailImage) {
      formData.append('detailImage', images.detailImage);
    }
    if (images.specImage) {
      formData.append('specImage', images.specImage);
    }
    formData.append('productId', productId);

    return request.post('/api/batteryimage/upload-batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 多图片上传（支持一次上传多个图片）
   * @param {Array} files 图片文件数组
   * @param {number} productId 商品ID（必需，用于组织图片存储路径）
   * @param {string} imageType 图片类型（main/detail/spec/gallery）
   * @returns {Promise} Promise对象
   */
  uploadMultipleImages(files, productId, imageType = 'gallery') {
    console.log('多图片上传:', files, 'productId:', productId, 'imageType:', imageType);

    const formData = new FormData();

    // 添加所有文件
    files.forEach((file, index) => {
      formData.append('files', file);
    });

    formData.append('productId', productId);
    formData.append('imageType', imageType);

    return request.post('/api/batteryimage/upload-multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 删除电池图片
   * @param {string} imagePath 图片路径
   * @returns {Promise} Promise对象
   */
  deleteImage(imagePath) {
    console.log('删除电池图片:', imagePath);

    return request.delete('/api/batteryimage/delete', {
      params: {
        imagePath: imagePath
      }
    });
  },

  /**
   * 获取图片完整URL
   * @param {string} imagePath 图片路径
   * @returns {string} 完整的图片URL
   */
  getImageUrl(imagePath) {
    if (!imagePath) {
      return '';
    }

    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    // 如果是相对路径，拼接基础URL
    const baseUrl = process.env.NODE_ENV === 'development'
      ? 'http://localhost:5242'
      : window.location.origin;

    // 确保路径以 / 开头
    const path = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;

    return `${baseUrl}${path}`;
  },

  /**
   * 验证图片文件
   * @param {File} file 文件对象
   * @returns {Object} 验证结果 { valid: boolean, message: string }
   */
  validateImageFile(file) {
    if (!file) {
      return { valid: false, message: '请选择要上传的图片文件' };
    }

    // 检查文件大小（限制为5MB）
    const maxFileSize = 5 * 1024 * 1024;
    if (file.size > maxFileSize) {
      return { valid: false, message: '图片文件大小不能超过5MB' };
    }

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, message: '只支持 JPG、PNG、GIF、BMP、WEBP 格式的图片文件' };
    }

    return { valid: true, message: '文件验证通过' };
  },

  /**
   * 压缩图片（可选功能）
   * @param {File} file 原始图片文件
   * @param {number} quality 压缩质量 (0-1)
   * @param {number} maxWidth 最大宽度
   * @returns {Promise<File>} 压缩后的文件
   */
  compressImage(file, quality = 0.8, maxWidth = 1200) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = function() {
        // 计算新的尺寸
        let { width, height } = img;
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制压缩后的图片
        ctx.drawImage(img, 0, 0, width, height);

        // 转换为Blob
        canvas.toBlob((blob) => {
          if (blob) {
            // 创建新的File对象
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        }, file.type, quality);
      };

      img.onerror = function() {
        reject(new Error('图片加载失败'));
      };

      img.src = URL.createObjectURL(file);
    });
  },

  /**
   * 获取图片预览URL
   * @param {File} file 图片文件
   * @returns {string} 预览URL
   */
  getPreviewUrl(file) {
    if (!file) {
      return '';
    }
    return URL.createObjectURL(file);
  },

  /**
   * 释放预览URL
   * @param {string} url 预览URL
   */
  revokePreviewUrl(url) {
    if (url && url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
    }
  },

  // ========== 新增：图片数据管理API ==========

  /**
   * 获取商品的所有图片
   * @param {number} batteryId 商品ID
   * @returns {Promise} Promise对象
   */
  getBatteryImages(batteryId) {
    return request.get(`/api/battery-image-management/battery/${batteryId}`);
  },

  /**
   * 根据图片类型获取商品图片
   * @param {number} batteryId 商品ID
   * @param {string} imageType 图片类型
   * @returns {Promise} Promise对象
   */
  getBatteryImagesByType(batteryId, imageType) {
    return request.get(`/api/battery-image-management/battery/${batteryId}/type/${imageType}`);
  },

  /**
   * 获取商品主图
   * @param {number} batteryId 商品ID
   * @returns {Promise} Promise对象
   */
  getBatteryMainImage(batteryId) {
    return request.get(`/api/battery-image-management/battery/${batteryId}/main`);
  },

  /**
   * 创建商品图片记录
   * @param {number} batteryId 商品ID
   * @param {Object} data 图片数据
   * @returns {Promise} Promise对象
   */
  createBatteryImage(batteryId, data) {
    return request.post(`/api/battery-image-management/battery/${batteryId}`, data);
  },

  /**
   * 批量创建商品图片记录
   * @param {number} batteryId 商品ID
   * @param {Object} data 批量图片数据
   * @returns {Promise} Promise对象
   */
  batchCreateBatteryImages(batteryId, data) {
    return request.post(`/api/battery-image-management/battery/${batteryId}/batch`, data);
  },

  /**
   * 更新商品图片记录
   * @param {number} id 图片ID
   * @param {Object} data 更新数据
   * @returns {Promise} Promise对象
   */
  updateBatteryImage(id, data) {
    return request.put(`/api/battery-image-management/${id}`, data);
  },

  /**
   * 删除商品图片记录
   * @param {number} id 图片ID
   * @returns {Promise} Promise对象
   */
  deleteBatteryImage(id) {
    return request.delete(`/api/battery-image-management/${id}`);
  },

  /**
   * 设置主图
   * @param {number} batteryId 商品ID
   * @param {number} imageId 图片ID
   * @returns {Promise} Promise对象
   */
  setMainImage(batteryId, imageId) {
    return request.put(`/api/battery-image-management/battery/${batteryId}/main/${imageId}`);
  },

  /**
   * 上传图片并创建记录的组合方法
   * @param {File} file 图片文件
   * @param {number} productId 商品ID
   * @param {string} imageType 图片类型
   * @param {boolean} isMain 是否为主图
   * @returns {Promise} Promise对象
   */
  async uploadAndCreateImage(file, productId, imageType = 'main', isMain = false) {
    try {
      // 1. 先上传文件
      const uploadResult = await this.uploadImage(file, productId, imageType);

      if (uploadResult.code === 200 && uploadResult.data) {
        // 2. 创建图片记录
        const imageData = {
          imageUrl: uploadResult.data.imageUrl,
          imageType: imageType,
          isMain: isMain,
          fileName: uploadResult.data.fileName,
          fileSize: uploadResult.data.fileSize,
          sortOrder: 0
        };

        const createResult = await this.createBatteryImage(productId, imageData);

        return {
          code: 200,
          message: '图片上传并创建记录成功',
          data: {
            upload: uploadResult.data,
            record: createResult.data
          }
        };
      } else {
        throw new Error(uploadResult.message || '图片上传失败');
      }
    } catch (error) {
      console.error('上传并创建图片记录失败:', error);
      throw error;
    }
  }
}
