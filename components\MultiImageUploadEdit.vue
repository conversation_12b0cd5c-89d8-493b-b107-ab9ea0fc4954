<template>
  <view class="multi-image-upload-edit">
    <!-- 标题 -->
    <view class="upload-header">
      <text class="upload-title">{{ title }}</text>
      <text class="upload-count">{{ imageList.length }}/{{ maxCount }}</text>
    </view>

    <!-- 图片列表 -->
    <view class="image-grid">
      <!-- 已上传的图片 -->
      <view
        class="image-item"
        v-for="(image, index) in imageList"
        :key="index"
      >
        <image
          :src="getImageDisplayUrl(image.url || image.tempPath)"
          class="image-preview"
          mode="aspectFill"
          @tap="previewImage(index)"
        />

        <!-- 图片操作按钮 -->
        <view class="image-actions">
          <view class="action-btn delete-btn" @tap="removeImage(index)">
            <text class="action-icon">×</text>
          </view>
          <view class="action-btn main-btn" v-if="showMainButton && !image.isMain" @tap="setAsMain(index)">
            <text class="action-text">主图</text>
          </view>
          <view class="main-badge" v-if="image.isMain">
            <text class="badge-text">主图</text>
          </view>
        </view>

        <!-- 上传状态 -->
        <view class="upload-status" v-if="image.status">
          <view class="status-uploading" v-if="image.status === 'uploading'">
            <text class="status-text">上传中...</text>
          </view>
          <view class="status-success" v-if="image.status === 'success'">
            <text class="status-icon">✓</text>
          </view>
          <view class="status-error" v-if="image.status === 'error'">
            <text class="status-icon">!</text>
          </view>
        </view>

        <!-- 相对路径显示 -->
        <view class="image-path" v-if="image.relativePath">
          <text class="path-text">{{ image.relativePath }}</text>
        </view>
      </view>

      <!-- 添加按钮 -->
      <view
        class="add-image-btn"
        v-if="imageList.length < maxCount"
        @tap="chooseImages"
      >
        <text class="add-icon">+</text>
        <text class="add-text">添加图片</text>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="upload-tips" v-if="showTips">
      <text class="tip-text">• 支持JPG、PNG、GIF格式</text>
      <text class="tip-text">• 单张图片不超过5MB</text>
      <text class="tip-text">• 最多上传{{ maxCount }}张图片</text>
      <text class="tip-text" v-if="showMainButton">• 点击"主图"设置商品主图</text>
    </view>
  </view>
</template>

<script>
import { getImageDisplayUrl, processChooseImageResponse, createImageItem, debugImageUrl } from '../utils/imageUtils.js'

export default {
  name: 'MultiImageUploadEdit',
  props: {
    // 组件标题
    title: {
      type: String,
      default: '商品图片'
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 9
    },
    // 图片类型
    imageType: {
      type: String,
      default: 'gallery'
    },
    // 商品ID
    productId: {
      type: [Number, String],
      default: 0
    },
    // 初始图片列表
    value: {
      type: Array,
      default: () => []
    },
    // 是否显示主图按钮
    showMainButton: {
      type: Boolean,
      default: false
    },
    // 是否显示提示信息
    showTips: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      imageList: []
    }
  },
  computed: {
    // 检测是否为 H5 环境
    isH5() {
      // #ifdef H5
      return true;
      // #endif
      // #ifndef H5
      return false;
      // #endif
    }
  },
  watch: {
    value: {
      handler(newVal) {
        // 避免无限循环，只在数据真正变化时更新
        if (JSON.stringify(newVal) !== JSON.stringify(this.imageList)) {
          this.imageList = [...(newVal || [])];
        }
      },
      immediate: true,
      deep: true
    },
    imageList: {
      handler(newVal) {
        // 避免无限循环，添加防抖
        this.$nextTick(() => {
          this.$emit('input', newVal);
          this.$emit('change', newVal);
        });
      },
      deep: true
    }
  },
  methods: {
    // 选择图片
    chooseImages() {
      const remainingCount = this.maxCount - this.imageList.length;
      if (remainingCount <= 0) {
        uni.showToast({
          title: `最多只能上传${this.maxCount}张图片`,
          icon: 'none'
        });
        return;
      }

      // H5 环境使用原生文件选择器
      if (this.isH5) {
        this.triggerFileInput(remainingCount);
        return;
      }

      // 非 H5 环境使用 uni.chooseImage
      this.chooseImagesUni(remainingCount);
    },

    // 触发文件选择器（H5 环境）
    triggerFileInput(remainingCount) {
      // 创建一个临时的 input 元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = true;

      input.onchange = (event) => {
        this.handleFileChange(event);
      };

      // 触发文件选择
      input.click();
    },

    // 使用 uni.chooseImage 选择图片
    chooseImagesUni(remainingCount) {
      uni.chooseImage({
        count: Math.min(remainingCount, 9), // uni.chooseImage最多支持9张
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          console.log('uni.chooseImage 响应:', res);

          // 使用工具函数处理响应
          const tempFiles = processChooseImageResponse(res);

          if (tempFiles.length > 0) {
            this.handleSelectedImages(tempFiles);
          } else {
            console.error('没有获取到有效的图片文件');
            uni.showToast({
              title: '没有获取到图片文件',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理 H5 环境下的文件选择
    handleFileChange(event) {
      const files = event.target.files;
      if (!files || files.length === 0) {
        return;
      }

      console.log('H5 文件选择:', files);

      const remainingCount = this.maxCount - this.imageList.length;
      const filesToProcess = Array.from(files).slice(0, remainingCount);

      const tempFiles = filesToProcess.map((file, index) => ({
        path: URL.createObjectURL(file), // 创建 blob URL 用于预览
        size: file.size,
        name: file.name,
        originalFile: file // 保存原始 File 对象
      }));

      this.handleSelectedImages(tempFiles);

      // 清空 input 值，允许重复选择同一文件
      event.target.value = '';
    },

    // 处理选择的图片
    handleSelectedImages(tempFiles) {
      console.log('处理选择的图片:', tempFiles);

      tempFiles.forEach((file, index) => {
        // 调试图片URL
        debugImageUrl(file.path, `选择图片 ${index + 1}`);

        // 使用工具函数创建图片对象
        const imageItem = createImageItem(file, index, false);

        console.log('创建的图片对象:', imageItem);
        this.imageList.push(imageItem);
      });

      // 如果是第一张图片且显示主图按钮，自动设为主图
      if (this.showMainButton && this.imageList.length === 1) {
        this.imageList[0].isMain = true;
      }

      console.log('图片处理完成，当前列表:', this.imageList);

      uni.showToast({
        title: `已选择${tempFiles.length}张图片`,
        icon: 'success'
      });
    },

    // 移除图片
    removeImage(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？',
        success: (res) => {
          if (res.confirm) {
            const removedImage = this.imageList[index];
            this.imageList.splice(index, 1);

            // 如果删除的是主图，自动设置第一张为主图
            if (removedImage.isMain && this.imageList.length > 0 && this.showMainButton) {
              this.imageList[0].isMain = true;
            }

            this.$emit('remove', removedImage);

            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    // 设置为主图
    setAsMain(index) {
      // 清除其他图片的主图标记
      this.imageList.forEach(image => {
        image.isMain = false;
      });

      // 设置当前图片为主图
      this.imageList[index].isMain = true;

      this.$emit('main-change', this.imageList[index]);

      uni.showToast({
        title: '已设为主图',
        icon: 'success'
      });
    },

    // 预览图片
    previewImage(index) {
      const urls = this.imageList.map(image =>
        this.getImageDisplayUrl(image.url || image.tempPath)
      );

      uni.previewImage({
        urls: urls,
        current: index
      });
    },

    // 获取图片显示URL
    getImageDisplayUrl(imagePath) {
      // 使用工具函数处理图片URL
      const url = getImageDisplayUrl(imagePath);

      // 调试输出
      if (imagePath && imagePath !== url) {
        console.log('图片URL转换:', {
          原始路径: imagePath,
          转换后: url
        });
      }

      return url;
    },

    // 获取新添加的图片列表
    getNewImages() {
      return this.imageList.filter(image => image.isNew && image.tempPath);
    },

    // 获取已删除的图片列表
    getRemovedImages() {
      // 这个需要在父组件中维护删除列表
      return [];
    },

    // 获取主图
    getMainImage() {
      return this.imageList.find(image => image.isMain);
    },

    // 更新图片上传状态
    updateImageStatus(imageId, status, url = '', relativePath = '') {
      const image = this.imageList.find(img => img.id === imageId);
      if (image) {
        image.status = status;
        if (url) {
          image.url = url;
          image.isNew = false; // 上传成功后不再是新图片
        }
        if (relativePath) {
          image.relativePath = relativePath; // 更新相对路径
        }
      }
    },

    // 清空所有图片
    clearAll() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.imageList = [];
            uni.showToast({
              title: '已清空',
              icon: 'success'
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.multi-image-upload-edit {
  width: 100%;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.upload-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.upload-count {
  font-size: 24rpx;
  color: #666;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

.image-preview {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-btn {
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  background-color: rgba(255, 71, 87, 0.8);
}

.action-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.main-btn {
  background-color: rgba(41, 121, 255, 0.8);
  border-radius: 8rpx;
  width: auto;
  height: 32rpx;
  padding: 0 12rpx;
}

.action-text {
  color: white;
  font-size: 20rpx;
}

.main-badge {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background-color: #ff6b35;
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
}

.badge-text {
  color: white;
  font-size: 20rpx;
}

.upload-status {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-uploading {
  color: white;
  font-size: 24rpx;
}

.status-success {
  background-color: #52c41a;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-error {
  background-color: #ff4d4f;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.image-path {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 8rpx;
  max-height: 60rpx;
  overflow: hidden;
}

.path-text {
  color: white;
  font-size: 20rpx;
  line-height: 1.2;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.add-image-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

.upload-tips {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
