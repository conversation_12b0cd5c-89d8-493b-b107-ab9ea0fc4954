/**
 * 短信API服务
 */
import request from '@/utils/request';

// API接口
const SmsAPI = {
  /**
   * 发送验证码
   * @param {Object} data 请求数据
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.type 验证码类型 (register/login/reset)
   * @returns {Promise} Promise对象
   */
  sendVerificationCode(data) {
    console.log('发送验证码请求:', data);
    
    return request.post('/api/sms/send', {
      phoneNumber: data.phoneNumber,
      type: data.type
    })
    .then(response => {
      console.log('发送验证码成功:', response);
      return response;
    })
    .catch(error => {
      console.error('发送验证码失败:', error);
      throw error;
    });
  },
  
  /**
   * 验证验证码
   * @param {Object} data 请求数据
   * @param {string} data.phoneNumber 手机号码
   * @param {string} data.code 验证码
   * @param {string} data.type 验证码类型 (register/login/reset)
   * @returns {Promise} Promise对象
   */
  verifyCode(data) {
    console.log('验证验证码请求:', data);
    
    return request.post('/api/sms/verify', {
      phoneNumber: data.phoneNumber,
      code: data.code,
      type: data.type
    })
    .then(response => {
      console.log('验证验证码成功:', response);
      return response;
    })
    .catch(error => {
      console.error('验证验证码失败:', error);
      throw error;
    });
  }
};

export default SmsAPI;
