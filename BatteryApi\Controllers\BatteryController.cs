using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using BatteryApi.Services.Interfaces;
using BatteryApi.DTOs;
using BatteryApi.DTOs.Battery;
using BatteryApi.DTOs.Common;
using System.Security.Claims;

namespace BatteryApi.Controllers;
[ApiController]
[Route("api/batteries")]
[Authorize]
public class BatteryController : ControllerBase
{
    private readonly IBatteryService _batteryService;

    public BatteryController(IBatteryService batteryService)
    {
        _batteryService = batteryService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(PagedResponse<BatteryDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatteries([FromQuery] BatteryQueryParameters parameters)
    {
        var result = await _batteryService.GetBatteriesAsync(parameters);
        return Ok(result);
    }

    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(BatteryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetBattery(int id)
    {
        try
        {
            var battery = await _batteryService.GetBatteryByIdAsync(id);
            return Ok(battery);
        }
        catch (Exception ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }



    [HttpPost]
    // 暂时移除权限验证以便调试
    // [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CreateBattery([FromForm] CreateBatteryRequest request)
    {
        Console.WriteLine("🔍 CreateBattery 接收到请求");
        Console.WriteLine($"📋 请求数据: Spec={request.Spec}, CategoryId={request.CategoryId}");
        Console.WriteLine($"📋 MainImages 数量: {request.MainImages?.Count ?? 0}");
        Console.WriteLine($"📋 MainImagesJson 数量: {request.MainImagesJson?.Count ?? 0}");

        if (request.MainImages != null)
        {
            for (int i = 0; i < request.MainImages.Count; i++)
            {
                var file = request.MainImages[i];
                Console.WriteLine($"📎 文件 {i + 1}: {file.FileName}, 大小: {file.Length} bytes");
            }
        }

        if (!ModelState.IsValid)
        {
            Console.WriteLine("❌ 模型验证失败:");
            foreach (var error in ModelState)
            {
                Console.WriteLine($"字段 {error.Key}: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
            }
            return BadRequest(ModelState);
        }

        try
        {
            // 记录接收到的请求数据
            Console.WriteLine($"收到创建电池请求: {System.Text.Json.JsonSerializer.Serialize(request)}");

            var battery = await _batteryService.CreateBatteryAsync(request);
            return CreatedAtAction(nameof(GetBattery), new { id = battery.Id }, new {
                code = 0,
                message = "创建成功",
                data = battery
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建电池异常: {ex.Message}");
            Console.WriteLine($"异常堆栈: {ex.StackTrace}");
            return BadRequest(new {
                message = ex.Message,
                code = 1,
                type = ex.GetType().Name
            });
        }
    }

    [HttpPut("{id:int}")]
    // 暂时移除权限验证以便调试
    // [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateBattery(int id, [FromForm] UpdateBatteryRequest request)
    {
        try
        {
            var battery = await _batteryService.UpdateBatteryAsync(id, request);
            return Ok(battery);
        }
        catch (Exception ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    /// <summary>
    /// 创建电池（支持 IFormFile 图片上传）
    /// </summary>
    [HttpPost("create-with-files")]
    // 暂时移除权限验证以便调试
    // [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryDto), StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateBatteryWithFiles([FromForm] CreateBatteryWithFilesRequest request)
    {
        try
        {
            var result = await _batteryService.CreateBatteryWithFilesAsync(request);
            return Ok(new { code = 0, message = "创建成功", data = result });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建电池异常: {ex.Message}");
            Console.WriteLine($"异常堆栈: {ex.StackTrace}");
            return BadRequest(new {
                message = ex.Message,
                code = 1,
                type = ex.GetType().Name
            });
        }
    }

    /// <summary>
    /// 更新电池（支持 IFormFile 图片上传）
    /// </summary>
    [HttpPut("{id}/update-with-files")]
    // 暂时移除权限验证以便调试
    // [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateBatteryWithFiles(int id, [FromForm] UpdateBatteryWithFilesRequest request)
    {
        try
        {
            var result = await _batteryService.UpdateBatteryWithFilesAsync(id, request);
            return Ok(new { code = 0, message = "更新成功", data = result });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { code = 404, message = ex.Message });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新电池异常: {ex.Message}");
            Console.WriteLine($"异常堆栈: {ex.StackTrace}");
            return BadRequest(new {
                message = ex.Message,
                code = 1,
                type = ex.GetType().Name
            });
        }
    }

    [HttpDelete("{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteBattery(int id)
    {
        var result = await _batteryService.DeleteBatteryAsync(id);
        return result ? NoContent() : NotFound();
    }

    // 移除了电池借出归还和使用记录相关的API端点

    // 移除了电池状态历史和维护相关的API端点

    [HttpGet("status-summary")]
    [ProducesResponseType(typeof(Dictionary<string, int>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatteryStatusSummary()
    {
        var summary = await _batteryService.GetBatteryStatusSummaryAsync();
        return Ok(summary);
    }

    [HttpGet("average-health")]
    [ProducesResponseType(typeof(double), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetAverageBatteryHealth()
    {
        var average = await _batteryService.GetAverageBatteryHealthAsync();
        return Ok(average);
    }

    // 移除了维护管理和使用记录相关的API端点

    [HttpGet("specs")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(List<BatterySpecDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatterySpecs()
    {
        var specs = await _batteryService.GetBatterySpecsAsync();
        return Ok(specs);
    }

    [HttpGet("{id:int}/services")]
    [ProducesResponseType(typeof(List<BatteryServiceDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatteryServices(int id)
    {
        try
        {
            var services = await _batteryService.GetBatteryServicesAsync(id);
            return Ok(new { code = 0, message = "success", data = services });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpPost("{batteryId:int}/services")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryServiceDto), StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateBatteryService(int batteryId, [FromBody] CreateBatteryServiceRequest request)
    {
        try
        {
            var service = await _batteryService.CreateBatteryServiceAsync(batteryId, request);
            return CreatedAtAction(nameof(GetBatteryServices), new { id = service.BatteryId }, new { code = 0, message = "服务项添加成功", data = service });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpPut("services/{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryServiceDto), StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateBatteryService(int id, [FromBody] UpdateBatteryServiceRequest request)
    {
        try
        {
            var service = await _batteryService.UpdateBatteryServiceAsync(id, request);
            return Ok(new { code = 0, message = "服务项更新成功", data = service });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpDelete("services/{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> DeleteBatteryService(int id)
    {
        try
        {
            await _batteryService.DeleteBatteryServiceAsync(id);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpGet("{id:int}/installation-fees")]
    [ProducesResponseType(typeof(List<BatteryInstallationFeeDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatteryInstallationFees(int id)
    {
        try
        {
            var fees = await _batteryService.GetBatteryInstallationFeesAsync(id);
            return Ok(new { code = 0, message = "success", data = fees });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpPost("{batteryId:int}/installation-fees")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryInstallationFeeDto), StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateBatteryInstallationFee(int batteryId, [FromBody] CreateBatteryInstallationFeeRequest request)
    {
        try
        {
            var fee = await _batteryService.CreateBatteryInstallationFeeAsync(batteryId, request);
            return CreatedAtAction(nameof(GetBatteryInstallationFees), new { id = fee.BatteryId }, new { code = 0, message = "安装费用添加成功", data = fee });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpPut("installation-fees/{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(typeof(BatteryInstallationFeeDto), StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateBatteryInstallationFee(int id, [FromBody] UpdateBatteryInstallationFeeRequest request)
    {
        try
        {
            var fee = await _batteryService.UpdateBatteryInstallationFeeAsync(id, request);
            return Ok(new { code = 0, message = "安装费用更新成功", data = fee });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpDelete("installation-fees/{id:int}")]
    [Authorize(Roles = "Admin")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> DeleteBatteryInstallationFee(int id)
    {
        try
        {
            await _batteryService.DeleteBatteryInstallationFeeAsync(id);
            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    [HttpGet("stats")]
    [ProducesResponseType(typeof(BatteryStatsDto), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetBatteryStats()
    {
        var summary = await _batteryService.GetBatteryStatusSummaryAsync();
        var averageHealth = await _batteryService.GetAverageBatteryHealthAsync();

        // 获取所有电池类别
        var categories = await _batteryService.GetBatteryCategoriesAsync();

        // 构建类别统计信息
        var categoryStats = categories.Select(c => new CategoryStatDto
        {
            Category = c.Name,
            Count = summary.ContainsKey(c.Code) ? summary[c.Code] : 0,
            Percentage = summary.ContainsKey(c.Code) && summary.Values.Sum() > 0
                ? (int)Math.Round((double)summary[c.Code] / summary.Values.Sum() * 100)
                : 0
        }).ToList();

        // 构建月度租赁统计（示例数据）
        var monthlyRentals = new List<MonthlyRentalDto>
        {
            new MonthlyRentalDto { Month = "1月", Count = 5 },
            new MonthlyRentalDto { Month = "2月", Count = 8 },
            new MonthlyRentalDto { Month = "3月", Count = 12 },
            new MonthlyRentalDto { Month = "4月", Count = 7 },
            new MonthlyRentalDto { Month = "5月", Count = 10 },
            new MonthlyRentalDto { Month = "6月", Count = 15 }
        };

        var stats = new BatteryStatsDto
        {
            TotalBatteries = summary.Values.Sum(),
            AvailableBatteries = summary.ContainsKey("Available") ? summary["Available"] : 0,
            RentedBatteries = summary.ContainsKey("Rented") ? summary["Rented"] : 0,
            SoldBatteries = summary.ContainsKey("Sold") ? summary["Sold"] : 0,
            MaintenanceBatteries = summary.ContainsKey("Maintenance") ? summary["Maintenance"] : 0,
            CategoryStats = categoryStats,
            MonthlyRentals = monthlyRentals
        };

        return Ok(stats);
    }

    /// <summary>
    /// 检查商品是否重复
    /// </summary>
    /// <param name="request">查重请求参数</param>
    /// <returns>查重结果</returns>
    [HttpPost("check-duplicate")]
    [ProducesResponseType(typeof(DuplicateCheckResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CheckDuplicate([FromBody] DuplicateCheckRequest request)
    {
        try
        {
            var isDuplicate = await _batteryService.CheckDuplicateAsync(request.CategoryId, request.Spec);
            return Ok(new { code = 0, message = "success", data = new { isDuplicate = isDuplicate } });
        }
        catch (Exception ex)
        {
            return BadRequest(new { code = 1, message = ex.Message });
        }
    }

    /// <summary>
    /// 测试 JSON 反序列化
    /// </summary>
    /// <param name="request">创建电池请求</param>
    /// <returns>反序列化结果</returns>
    [HttpPost("test-json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult TestJsonDeserialization([FromBody] CreateBatteryRequest request)
    {
        try
        {
            Console.WriteLine($"收到测试请求: {System.Text.Json.JsonSerializer.Serialize(request)}");

            return Ok(new {
                code = 0,
                message = "JSON 反序列化成功",
                data = new {
                    spec = request.Spec,
                    status = request.Status,
                    price = request.Price,
                    categoryId = request.CategoryId,
                    mainImagesCount = request.MainImages?.Count ?? 0,
                    servicesCount = request.Services?.Count ?? 0,
                    installationFeesCount = request.InstallationFees?.Count ?? 0
                }
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"JSON 反序列化失败: {ex.Message}");
            return BadRequest(new {
                code = 1,
                message = ex.Message,
                type = ex.GetType().Name
            });
        }
    }
}

public class DuplicateCheckRequest
{
    public int CategoryId { get; set; }
    public string Spec { get; set; }
}

public class DuplicateCheckResponse
{
    public bool IsDuplicate { get; set; }
}