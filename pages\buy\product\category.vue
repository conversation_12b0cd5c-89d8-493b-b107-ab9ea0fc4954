<template>
  <view class="category-container">
    <!-- Banner区域 -->
    <view class="banner-section">
      <swiper class="banner-swiper" circular autoplay interval="3000" duration="500">
        <swiper-item v-for="(item, index) in bannerList" :key="index">
          <image class="banner-image" :src="item.image" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
    </view>

    <!-- 公告区域 -->
    <view class="notice-section">
      <view class="notice-icon">
        <text class="iconfont icon-notice"></text>
      </view>
      <swiper class="notice-swiper" vertical autoplay circular interval="3000" duration="500">
        <swiper-item v-for="(item, index) in noticeList" :key="index">
          <view class="notice-item">{{ item.content }}</view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 分类导航栏 -->
    <scroll-view scroll-x class="category-nav">
      <view 
        class="nav-item" 
        v-for="(item, index) in categories" 
        :key="index"
        :class="{ active: currentCategory === item.id }"
        @tap="switchCategory(item.id)"
      >
        {{ item.name }}
      </view>
    </scroll-view>

    <!-- 产品列表 -->
    <scroll-view 
      scroll-y 
      class="product-list"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshList"
      @scrolltolower="loadMore"
    >
      <!-- 空状态 -->
      <view class="empty-tip" v-if="filteredProducts.length === 0 && !loading">
        <image src="/static/battery-default.svg" mode="aspectFit" class="empty-icon"></image>
        <text>暂无相关产品</text>
      </view>

      <!-- 产品网格 -->
      <view class="product-grid">
        <view 
          class="product-item"
          v-for="product in filteredProducts"
          :key="product.id"
          @tap="navigateToDetail(product.id)"
        >
          <!-- 状态标签 -->
          <view class="status-tag" :class="getProductStatusClass(product.status)" v-if="product.status">
            {{ getProductStatusText(product.status) }}
          </view>

          <!-- 产品图片 -->
          <image 
            class="product-image" 
            src="/static/battery-default.svg"
            mode="aspectFit"
          ></image>

          <!-- 产品信息 -->
          <view class="product-info">
            <view class="product-name">{{ product.name || product.spec }}</view>
            <view class="product-desc">{{ product.description || product.spec }}</view>
            <view class="product-price">
              <text class="price">¥{{ product.price }}</text>
              <text class="rent-price" v-if="product.rentPrice">¥{{ product.rentPrice }}/天</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" v-if="loading">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>

      <!-- 无更多数据 -->
      <view class="no-more" v-if="noMore && filteredProducts.length > 0">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  data() {
    return {
      bannerList: [
        { image: "/static/banner/banner1.svg" },
        { image: "/static/banner/banner2.svg" },
        { image: "/static/banner/banner3.svg" },
      ],
      noticeList: [
        { content: "电池租售管理系统正式上线，欢迎使用！" },
        { content: "门店库存实时更新，请随时关注！" },
      ],
      categories: [
        { id: 'recommend', name: '推荐' },
        { id: 'lithium', name: '锂电池' },
        { id: 'electric', name: '电动车' },
        { id: 'tricycle', name: '三轮车' },
        { id: 'charger', name: '充电器' },
        { id: 'protection', name: '保护板' },
        { id: 'cell', name: '电芯' },
        { id: 'box', name: '电池盒' },
        { id: 'accessory', name: '辅材' },
      ],
      currentCategory: 'recommend',
      page: 1,
      pageSize: 10,
      loading: false,
      refreshing: false,
      noMore: false,
      // 模拟产品数据，实际应从API获取
      productList: []
    }
  },

  computed: {
    ...mapState('battery', ['batteryList']),
    
    // 根据当前分类过滤产品
    filteredProducts() {
      // 这里只是简单模拟，实际应根据API返回的数据进行过滤
      if (this.currentCategory === 'recommend') {
        return this.batteryList.slice(0, 10);
      } else if (this.currentCategory === 'lithium') {
        return this.batteryList.filter(item => item.type === 'lithium' || true);
      } else {
        // 其他分类暂时返回空数组，实际应根据API返回的数据
        return this.batteryList.filter(item => item.category === this.currentCategory || true).slice(0, 6);
      }
    }
  },

  onLoad() {
    this.initData();
    // 页面加载时显示tabBar
    uni.showTabBar();
  },

  onShow() {
    // 每次页面显示时都确保tabBar显示
    uni.showTabBar();
  },

  methods: {
    ...mapActions('battery', ['getBatteryList']),

    // 初始化数据
    async initData() {
      this.loading = true;
      try {
        await this.getBatteryList();
      } catch (error) {
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
      this.loading = false;
    },

    // 切换分类
    switchCategory(categoryId) {
      this.currentCategory = categoryId;
      this.page = 1;
      this.noMore = false;
      this.refreshList();
    },

    // 刷新列表
    async refreshList() {
      this.refreshing = true;
      await this.initData();
      this.refreshing = false;
      uni.stopPullDownRefresh();
    },

    // 加载更多
    loadMore() {
      if (this.loading || this.noMore) return;
      this.page++;
      // 这里应该调用API加载更多数据
      // 模拟加载完成
      this.noMore = true;
    },

    // 获取产品状态样式类
    getProductStatusClass(status) {
      const statusMap = {
        1: 'status-available',
        2: 'status-rented',
        3: 'status-sold',
        4: 'status-maintenance'
      }
      return statusMap[status] || ''
    },

    // 获取产品状态文本
    getProductStatusText(status) {
      const statusMap = {
        1: '可用',
        2: '已租赁',
        3: '已售出',
        4: '维修中'
      }
      return statusMap[status] || '未知'
    },

    // 跳转到详情页
    navigateToDetail(productId) {
      uni.navigateTo({
        url: `/pages/buy/battery/detail?id=${productId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.category-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: env(safe-area-inset-bottom);
}

.banner-section {
  width: 100%;
  height: 350rpx;

  .banner-swiper {
    width: 100%;
    height: 100%;
  }

  .banner-image {
    width: 100%;
    height: 100%;
  }
}

.notice-section {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 2rpx;

  .notice-icon {
    margin-right: 20rpx;

    .iconfont {
      font-size: 40rpx;
      color: #ff6b00;
    }
  }

  .notice-swiper {
    flex: 1;
    height: 60rpx;
  }

  .notice-item {
    font-size: 28rpx;
    color: #666;
    line-height: 60rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.category-nav {
  white-space: nowrap;
  background-color: #fff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
  
  .nav-item {
    display: inline-block;
    padding: 15rpx 30rpx;
    font-size: 28rpx;
    color: #333;
    position: relative;
    
    &.active {
      color: #ff6b00;
      font-weight: bold;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #ff6b00;
        border-radius: 2rpx;
      }
    }
  }
}

.product-list {
  height: calc(100vh - 520rpx); /* 调整高度以适应上方内容 */
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  
  .product-item {
    width: calc(50% - 20rpx);
    margin: 10rpx;
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .status-tag {
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      color: #fff;
      z-index: 1;
      
      &.status-available {
        background-color: #2979ff;
      }
      &.status-rented {
        background-color: #ff9500;
      }
      &.status-sold {
        background-color: #8e8e93;
      }
      &.status-maintenance {
        background-color: #ff3b30;
      }
    }
    
    .product-image {
      width: 100%;
      height: 300rpx;
      display: block;
    }
    
    .product-info {
      padding: 20rpx;
      
      .product-name {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .product-desc {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 15rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .product-price {
        display: flex;
        align-items: center;
        
        .price {
          font-size: 32rpx;
          color: #ff6b00;
          font-weight: bold;
        }
        
        .rent-price {
          font-size: 24rpx;
          color: #999;
          margin-left: 15rpx;
        }
      }
    }
  }
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

.loading-more, .no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    margin-left: 10rpx;
  }
}
</style>
