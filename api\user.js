/**
 * 用户管理API服务
 */
import request from '@/utils/request';

// API接口
const UserAPI = {
  /**
   * 获取用户列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getUserList(params = {}) {
    // 构建查询字符串
    let queryString = '';
    if (params) {
      const queryParams = [];
      for (const key in params) {
        // 只添加非空的参数
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          queryParams.push(`${key}=${encodeURIComponent(params[key])}`);
        }
      }
      if (queryParams.length > 0) {
        queryString = '?' + queryParams.join('&');
      }
    }

    return request.get(`/api/users${queryString}`);
  },

  /**
   * 获取用户详情
   * @param {Number} id 用户ID
   * @returns {Promise} Promise对象
   */
  getUserDetail(id) {
    return request.get(`/api/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data 用户数据
   * @returns {Promise} Promise对象
   */
  createUser(data) {
    return request.post('/api/users', data);
  },

  /**
   * 更新用户
   * @param {Number} id 用户ID
   * @param {Object} data 用户数据
   * @returns {Promise} Promise对象
   */
  updateUser(id, data) {
    return request.put(`/api/users/${id}`, data);
  },

  /**
   * 更新用户状态
   * @param {Number} id 用户ID
   * @param {Object} data 状态数据
   * @returns {Promise} Promise对象
   */
  updateUserStatus(id, data) {
    // 确保使用正确的属性名
    const requestData = {
      status: data.status,
      statusReason: data.reason
    };

    console.log(`发送用户状态更新请求: id=${id}, 数据=`, JSON.stringify(requestData));

    return request.put(`/api/users/${id}/status`, requestData)
      .then(response => {
        console.log('用户状态更新成功:', response);
        return response;
      })
      .catch(error => {
        console.error('用户状态更新失败:', error);
        throw error;
      });
  },

  /**
   * 删除用户
   * @param {Number} id 用户ID
   * @returns {Promise} Promise对象
   */
  deleteUser(id) {
    return request.delete(`/api/users/${id}`);
  },

  /**
   * 重置用户密码
   * @param {Number} id 用户ID
   * @param {Object} data 密码数据
   * @returns {Promise} Promise对象
   */
  resetPassword(id, data) {
    return request.post(`/api/users/${id}/reset-password`, data);
  }
};

export default UserAPI;
