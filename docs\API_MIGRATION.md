# Battery → Product API 迁移文档

## 📋 **迁移概述**

本文档记录了从电池（Battery）API 到商品（Product）API 的完整迁移过程。

### **迁移原因**
- 统一商品管理：将电池作为商品的一个类别，而不是独立的实体
- 简化系统架构：减少重复的API端点和数据模型
- 提高可扩展性：支持更多类型的商品，不仅限于电池

## 🔄 **API 端点迁移对照表**

### **已废弃的电池API端点**

| 废弃的电池API | 新的商品API | 状态 | 说明 |
|--------------|------------|------|------|
| `GET /api/batteries` | `GET /api/product` | ✅ 已迁移 | 获取商品列表 |
| `GET /api/batteries/{id}` | `GET /api/product/{id}` | ✅ 已迁移 | 获取商品详情 |
| `POST /api/batteries` | `POST /api/product` | ✅ 已迁移 | 创建商品 |
| `PUT /api/batteries/{id}` | `PUT /api/product/{id}` | ✅ 已迁移 | 更新商品 |
| `DELETE /api/batteries/{id}` | `DELETE /api/product/{id}` | ✅ 已迁移 | 删除商品 |
| `GET /api/battery-categories` | `GET /api/product-categories` | ✅ 已迁移 | 获取商品分类 |
| `POST /api/battery-categories` | `POST /api/product-categories` | ✅ 已迁移 | 创建商品分类 |
| `PUT /api/battery-categories/{id}` | `PUT /api/product-categories/{id}` | ✅ 已迁移 | 更新商品分类 |
| `DELETE /api/battery-categories/{id}` | `DELETE /api/product-categories/{id}` | ✅ 已迁移 | 删除商品分类 |
| `GET /api/battery-specs` | ❌ 已废弃 | ⚠️ 已移除 | 商品规格已整合到商品管理中 |
| `GET /api/battery-specs/category/{id}` | ❌ 已废弃 | ⚠️ 已移除 | 商品规格已整合到商品管理中 |
| `POST /api/battery-specs` | ❌ 已废弃 | ⚠️ 已移除 | 商品规格已整合到商品管理中 |
| `PUT /api/battery-specs/{id}` | ❌ 已废弃 | ⚠️ 已移除 | 商品规格已整合到商品管理中 |
| `DELETE /api/battery-specs/{id}` | ❌ 已废弃 | ⚠️ 已移除 | 商品规格已整合到商品管理中 |

## 🔧 **前端代码迁移**

### **API 模块迁移**

#### **已迁移的文件**
- ✅ `api/product.js` - 新的商品API模块
- ✅ `store/modules/product.js` - 新的商品状态管理模块
- ⚠️ `api/battery.js` - 保留但标记为废弃，部分方法返回空数据

#### **状态管理迁移**
```javascript
// 旧的电池模块调用
this.$store.dispatch('battery/getBatteryList')
this.$store.dispatch('battery/getCategoryList')
this.$store.dispatch('battery/getSpecList')

// 新的商品模块调用
this.$store.dispatch('product/getProductList')
this.$store.dispatch('product/getCategoryList')
this.$store.dispatch('product/getSpecList')
```

### **组件迁移状态**

#### **已迁移的组件**
- ✅ `pages/admin/inventory-management/index.vue` - 库存管理页面
- ✅ `pages/admin/product/edit.vue` - 商品编辑页面
- ✅ `pages/admin/battery/list.vue` - 电池列表页面（已迁移到产品API）
- ✅ `store/index.js` - 全局状态管理初始化

#### **待迁移的组件**
- ⏳ `pages/admin/battery/edit.vue` - 电池编辑页面
- ⏳ `pages/buy/battery/list.vue` - 购买电池列表页面
- ⏳ `pages/buy/battery/detail.vue` - 电池详情页面
- ⏳ `pages/buy/my/my-batteries.vue` - 我的电池页面
- ⏳ `pages/store/stock-in.vue` - 门店入库页面
- ⏳ `pages/admin/inventory/list.vue` - 库存列表页面

## 📊 **数据模型变更**

### **电池 → 商品字段映射**

| 电池字段 | 商品字段 | 说明 |
|---------|---------|------|
| `serialNumber` | `productCode` | 序列号 → 商品编号 |
| `model` | `spec` | 型号 → 规格 |
| `voltage` | `voltage` | 电压（保持不变） |
| `capacity` | `capacity` | 容量（保持不变） |
| `cycleCount` | `cycleCount` | 循环次数（保持不变） |
| `chargeTime` | `chargeTime` | 充电时间（保持不变） |
| `batteryId` | `id` | 电池ID → 商品ID |

### **新增的商品字段**
- `name` - 商品名称
- `categoryName` - 分类名称
- `notes` - 备注信息

## ⚠️ **兼容性处理**

### **向后兼容策略**
1. **API层面**：`api/battery.js` 中的废弃方法返回空数据而不是抛出错误
2. **状态管理层面**：`store/modules/product.js` 提供电池模块的别名getters
3. **组件层面**：逐步迁移，保持系统稳定运行

### **废弃方法处理**
```javascript
// 废弃的方法现在返回空数据
getBatterySpecs() {
  console.log('getBatterySpecs: 直接返回空数据，避免调用已废弃的 API');
  return Promise.resolve({
    code: 0,
    message: 'success',
    data: []
  });
}
```

## 🚀 **迁移进度**

### **已完成**
- ✅ 创建完整的商品API模块（`api/product.js`）
- ✅ 创建商品状态管理模块（`store/modules/product.js`）
- ✅ 修复首页404错误（废弃API方法返回空数据）
- ✅ 迁移库存管理页面（`pages/admin/inventory-management/index.vue`）
- ✅ 迁移商品编辑页面（`pages/admin/product/edit.vue`）
- ✅ 迁移电池列表页面（`pages/admin/battery/list.vue`）
- ✅ 更新全局状态管理初始化（`store/index.js`）
- ✅ 添加兼容性别名和向后兼容处理

### **进行中**
- ⏳ 逐步迁移剩余组件（6个组件待迁移）
- ⏳ 更新所有电池相关的API调用

### **待完成**
- ⏳ 清理废弃的电池API方法
- ⏳ 更新用户界面文案（电池 → 商品）
- ⏳ 完善商品API的错误处理
- ⏳ 添加商品API的单元测试

## 📝 **注意事项**

1. **渐进式迁移**：为了保持系统稳定，采用渐进式迁移策略
2. **数据兼容**：确保新的商品API能够处理现有的电池数据
3. **错误处理**：废弃的API方法返回空数据而不是错误，避免破坏现有功能
4. **测试验证**：每次迁移后都要进行充分的测试验证

## 🔍 **故障排除**

### **常见问题**
1. **404错误**：检查API端点是否已更新
2. **数据格式错误**：检查字段映射是否正确
3. **状态管理错误**：检查模块名称是否已更新

### **调试建议**
1. 检查浏览器控制台的网络请求
2. 查看Vuex DevTools中的状态变化
3. 确认API响应的数据格式

---

**最后更新时间**：2024年12月19日
**文档版本**：1.0.0
**维护者**：开发团队
