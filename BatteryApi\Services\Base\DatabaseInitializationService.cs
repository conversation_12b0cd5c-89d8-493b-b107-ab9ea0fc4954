using BatteryApi.Models;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Threading.Tasks;

namespace BatteryApi.Services.Base
{
    public class DatabaseInitializationService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<DatabaseInitializationService> _logger;

        public DatabaseInitializationService(ISqlSugarClient db, ILogger<DatabaseInitializationService> logger)
        {
            _db = db;
            _logger = logger;
        }

        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化数据库表...");

                // 检查并创建所有实体对应的表
                await CheckAndCreateTable<User>("Users");
                await CheckAndCreateTable<Battery>("Batteries");
                await CheckAndCreateTable<BatteryCategory>("BatteryCategories");
                await CheckAndCreateTable<BatterySpec>("BatterySpecs");
                // 移除了 BatteryHistory 和 BatteryUsage 表的创建
                await CheckAndCreateTable<Store>("Stores");
                await CheckAndCreateTable<StoreInventory>("StoreInventory");
                await CheckAndCreateTable<Order>("Orders");
                await CheckAndCreateTable<OrderOperation>("OrderOperations");
                await CheckAndCreateTable<UserVerification>("UserVerifications");
                await CheckAndCreateTable<SmsVerificationCode>("SmsVerificationCodes");
                await CheckAndCreateTable<SystemSetting>("SystemSettings");
                await CheckAndCreateTable<ProductAssignRecord>("ProductAssignRecords");
                await CheckAndCreateTable<InventoryHistory>("InventoryHistory");
     
                await CheckAndCreateTable<BatteryImage>("BatteryImages");
                await CheckAndCreateTable<BatteryServiceEntity>("BatteryServices");
                await CheckAndCreateTable<BatteryInstallationFee>("BatteryInstallationFees");
                await CheckAndCreateTable<IdCardRecognitionRecord>("IdCardRecognitionRecords");
                await CheckAndCreateTable<ProductImage>("ProductImages");

                // 新增商品管理相关表
                await CheckAndCreateTable<Product>("Products");
                await CheckAndCreateTable<ProductService>("ProductServices");
                await CheckAndCreateTable<ProductInstallationFee>("ProductInstallationFees");

                // 移除了复杂的表结构更新，因为已经简化了实体结构

                _logger.LogInformation("数据库表初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化数据库表时发生错误");
                throw;
            }
        }

        private async Task CheckAndCreateTable<T>(string tableName) where T : class, new()
        {
            try
            {
                if (!_db.DbMaintenance.IsAnyTable(tableName))
                {
                    _logger.LogInformation($"表 {tableName} 不存在，正在创建...");
                    _db.CodeFirst.InitTables(typeof(T));
                    _logger.LogInformation($"表 {tableName} 创建成功");
                }
                else
                {
                    _logger.LogInformation($"表 {tableName} 已存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查和创建表 {tableName} 时发生错误");
                throw;
            }
        }

        // 移除了复杂的表结构更新方法，因为已经简化了实体结构
    }
}
