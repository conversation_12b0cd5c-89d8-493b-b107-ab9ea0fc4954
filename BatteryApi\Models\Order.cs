using SqlSugar;
using SqlSugar.Extensions;

namespace BatteryApi.Models;

[SugarTable("Orders")]
public class Order
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 50)]
    public string OrderNo { get; set; }

    public int UserId { get; set; }

    public int ProductId { get; set; }

    public int StoreId { get; set; }

    public int Type { get; set; } // 1: 租赁, 2: 购买

    public int Status { get; set; } // 1: 待支付, 2: 待提货, 3: 已完成, 4: 已取消

    public decimal TotalAmount { get; set; }

    public decimal RentAmount { get; set; }

    public decimal Deposit { get; set; }

    public int RentDays { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.UtcNow;

    [SugarColumn(IsNullable = true)]
    public DateTime? PayTime { get; set; }

    [SugarColumn(IsNullable = true)]
    public DateTime? CompleteTime { get; set; }

    [SugarColumn(IsNullable = true)]
    public DateTime? CancelTime { get; set; }

    [SugarColumn(Length = 500, IsNullable = true)]
    public string CancelReason { get; set; }

    public int PayMethod { get; set; } // 1: 微信, 2: 支付宝

    [SugarColumn(Length = 100, IsNullable = true)]
    public string TransactionId { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(UserId))]
    public User User { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(ProductId))]
    public Product Product { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(StoreId))]
    public Store Store { get; set; }

    [Navigate(NavigateType.OneToMany, nameof(OrderOperation.OrderId))]
    public List<OrderOperation> Operations { get; set; }
}

[SugarTable("OrderOperations")]
public class OrderOperation
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    public int OrderId { get; set; }

    public int OperationType { get; set; } // 1: 创建, 2: 支付, 3: 完成, 4: 取消

    public DateTime OperationTime { get; set; } = DateTime.UtcNow;

    public int OperatorId { get; set; }

    [SugarColumn(Length = 50)]
    public string OperatorName { get; set; }

    [SugarColumn(Length = 500)]
    public string Remark { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(OrderId))]
    public Order Order { get; set; }
}
